<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\AdminsDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AdminRequest;
use App\Models\Admin;
use App\Repositories\CountryRepository;
use App\Repositories\PermissionRepository;
use App\Repositories\RoleRepository;
use App\Services\Admin\AdminService;

class AdminController extends Controller
{
    public function __construct(
        private readonly AdminService $adminService,
        private readonly CountryRepository $countryRepository,
        private readonly RoleRepository $roleRepository,
        private readonly PermissionRepository $permissionRepository
    ) {}

    public function index(AdminsDataTable $dataTable)
    {
        return $dataTable->render('pages.admin.admins.index');
    }

    public function create()
    {
        $countries = $this->countryRepository->getAll();
        $roles = $this->roleRepository->getActiveRoles('admin')->load('permissions');
        $permissions = $this->permissionRepository->getAllGrouped('admin');

        return view('pages.admin.admins.create', ['countries' => $countries, 'roles' => $roles, 'permissions' => $permissions]);
    }

    public function store(AdminRequest $request)
    {
        $this->adminService->create();

        return to_route('admin.admins.index')->with('success', __('Created successfully'));
    }

    public function show(Admin $admin)
    {
        $admin = $this->adminService->getAdmin($admin);

        $permissions = $this->permissionRepository->getAllGrouped('admin');

        return view('pages.admin.admins.show', ['admin' => $admin, 'permissions' => $permissions]);
    }

    public function edit(Admin $admin)
    {
        $admin = $this->adminService->getAdmin($admin);
        $countries = $this->countryRepository->getAll();
        $roles = $this->roleRepository->getActiveRoles('admin')->load('permissions');
        $permissions = $this->permissionRepository->getAllGrouped('admin');

        return view('pages.admin.admins.edit', ['admin' => $admin, 'countries' => $countries, 'roles' => $roles, 'permissions' => $permissions]);
    }

    public function update(AdminRequest $request, Admin $admin)
    {
        $this->adminService->update($admin);

        return back()->with('success', __('Updated successfully'));
    }

    public function destroy(Admin $admin)
    {
        $this->adminService->delete($admin);

        return to_route('admin.admins.index')->with('success', __('Deleted successfully'));
    }
}
