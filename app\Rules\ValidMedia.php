<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Http\UploadedFile;

class ValidMedia implements ValidationRule
{
    protected array $allMimes = [
        'image' => ['image/jpeg', 'image/png', 'image/heic', 'image/heif'],
        'pdf' => ['application/pdf'],
        'video' => ['video/mp4', 'video/x-msvideo', 'video/x-ms-wmv', 'video/x-flv', 'video/x-matroska'],
    ];

    protected array $allowedMimes;

    protected int $maxSizeKB;

    public function __construct(?array $types = null, ?int $maxSizeKB = null)
    {
        // If no type filter is given, allow all known MIME types
        $types ??= ['image', 'pdf', 'video'];

        // Flatten the allowed mimes from the selected types
        $this->allowedMimes = collect($types)
            ->flatMap(fn ($type) => $this->allMimes[$type] ?? [])
            ->unique()
            ->toArray();

        $this->maxSizeKB = $maxSizeKB ?? '5120';
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! ($value instanceof UploadedFile)) {
            $fail("The $attribute must be a valid file.");

            return;
        }

        if (! in_array($value->getMimeType(), $this->allowedMimes)) {
            $fail(__('file mime type not supported'));

            return;
        }
        if ($value->getSize() / 1024 > $this->maxSizeKB) {
            // Format the size validaiton message to either KB or MB
            $size = $this->maxSizeKB < 1024 ? $this->maxSizeKB.' KB' : round($this->maxSizeKB / 1024, 2).' MB';

            $fail(__('The max size for :attribute is :size', ['attribute' => __("attributes.$attribute"), 'size' => $size]));
        }
    }
}
