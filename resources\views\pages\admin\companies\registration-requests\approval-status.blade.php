<div class="row mb-3" id="approval-status-wrapper">
    <div class="col-sm-3 text-muted">{{ __('Approval Status') }}</div>
    <div class="col-sm-9 d-flex align-items-center gap-3">
        {!! statusBadge($company->approval_status) !!}

        @if(auth('admin')->user()->hasPermission('update company registration request'))
        @if ($company->approval_status === 'pending')
        <a href="javascript:void(0)"
            class="change-status-direct"
            data-url="{{ route('admin.company-registration-requests.update-approval-status', $company->id) }}"
            data-status="on_review">
            <span class="text-muted">➡</span>
            <button class="btn btn-sm btn-outline-primary position-relative">
                {{ __('On Review') }}
                <span class="spinner-border spinner-border-sm ms-2 d-none" id="onReviewSpinner" role="status" aria-hidden="true"></span>
            </button>
        </a>
        @elseif ($company->approval_status === 'on_review')
        <span class="text-muted">➡</span>

        <a href="javascript:void(0)"
            data-bs-toggle="modal"
            data-bs-target="#statusModal"
            onclick="openStatusModal(this)"
            data-url="{{ route('admin.company-registration-requests.update-approval-status', $company->id) }}"
            data-status="approved">
            <button class="btn btn-sm btn-success me-2">
                {{ __('Approve') }}
            </button>
        </a>

        <a href="javascript:void(0)"
            data-bs-toggle="modal"
            data-bs-target="#statusModal"
            onclick="openStatusModal(this)"
            data-url="{{ route('admin.company-registration-requests.update-approval-status', $company->id) }}"
            data-status="rejected">
            <button class="btn btn-sm btn-danger">
                {{ __('Reject') }}
            </button>
        </a>
        @endif
        @endif
    </div>
</div>

@if(auth('admin')->user()->hasPermission('update company registration request'))
<!-- ✅ Status Modal -->
<div class="modal fade" id="statusModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <form id="statusForm" method="POST">
            @csrf
            <input type="hidden" name="status" id="modalStatusInput">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('Confirm Status Change') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p id="statusModalText" class="mb-0"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('Confirm') }}</button>
                </div>
            </div>
        </form>
    </div>
</div>

@push('js')
<script>
    // ✅ Direct AJAX for On Review with spinner
    $(document).on('click', '.change-status-direct', function(e) {
        e.preventDefault();

        let url = $(this).data('url');
        let token = '{{ csrf_token() }}';
        let status = $(this).data('status');
        let spinner = $('#onReviewSpinner');

        // Show spinner
        spinner.removeClass('d-none');

        $.ajax({
            url: url,
            method: 'POST',
            data: {
                _token: token,
                status: status
            },
            dataType: 'json',
            success: function(response) {
                if (response.approval_status) {
                    $('#approval-status-wrapper').replaceWith(response.approval_status);
                }
            },
            error: function(xhr) {
                console.error(xhr.responseText);
                alert("Something went wrong. Please try again.");
            },
            complete: function() {
                // Hide spinner if still visible
                spinner.addClass('d-none');
            }
        });
    });

    // ✅ Open modal for Approve/Reject
    function openStatusModal(element) {
        let $el = $(element);
        let status = $el.data('status');
        let url = $el.data('url');

        $('#statusForm').attr('action', url);
        $('#modalStatusInput').val(status);

        const statusTexts = {
            'approved': `{{ __('Are you sure you want to approve this company?') }}`,
            'rejected': `{{ __('Are you sure you want to reject this company?') }}`
        };

        $('#statusModalText').text(statusTexts[status] ?? '');
    }

    // ✅ Handle modal form submit
    $('#statusForm').on('submit', function(e) {
        let status = $('#modalStatusInput').val();

        if (status === 'on_review') {
            e.preventDefault(); // handled via AJAX
            return;
        }

        let modalEl = document.getElementById('statusModal');
        let modalInstance = bootstrap.Modal.getInstance(modalEl);
        if (!modalInstance) {
            modalInstance = new bootstrap.Modal(modalEl);
        }
        modalInstance.hide();

        return true; // allow form to submit normally
    });
</script>
@endpush
@endif
