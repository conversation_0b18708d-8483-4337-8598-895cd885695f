<?php

namespace App\Services\Driver;

use App\Contracts\OrderStatusHandlerInterface;
use App\Enum\OrderItemStatus;
use App\Enum\OrderStatus;
use App\Events\OrderStatusUpdated;
use App\Models\Order;
use Illuminate\Database\Eloquent\Model;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class ExpressOrderStatusService implements OrderStatusHandlerInterface
{
    public function update(Order $order): Order
    {
        $nextStatus = match ($order->status) {
            OrderStatus::READY_TO_PICKUP => OrderStatus::HEADING_TO_PICKUP,
            OrderStatus::HEADING_TO_PICKUP => OrderStatus::ARRIVED_AT_PICKUP,
            OrderStatus::ARRIVED_AT_PICKUP => OrderStatus::PICKED_UP,
            OrderStatus::PICKED_UP => OrderStatus::IN_TRANSIT,
            OrderStatus::IN_TRANSIT => $order->shipping_type_id === 3 ? $this->getPostPickupStatus($order) : OrderStatus::ARRIVED_AT_DELIVERY_LOCATION,
            OrderStatus::ARRIVED_AT_DELIVERY_LOCATION => $this->getPostPickupStatus($order),
            default => null,
        };

        if (! $nextStatus instanceof \App\Enum\OrderStatus) {
            throw new BadRequestHttpException(__('Status cannot be updated at this stage.'));
        }

        if ($nextStatus === OrderStatus::PICKED_UP) {
            Model::withoutEvents(function () use ($order): void {
                $order->items->each->update(['status' => OrderItemStatus::PICKED_UP->value]);
            });
        } elseif ($nextStatus === OrderStatus::DELIVERED) {
            Model::withoutEvents(function () use ($order): void {
                $order->items->each->update(['status' => OrderItemStatus::DELIVERED->value]);
            });

            $order->driver()->update(['is_available' => true]);
        } elseif ($nextStatus === OrderStatus::DELIVERED_TO_LOCAL_HUB) {
            Model::withoutEvents(function () use ($order) {
                $order->items->each->update(['status' => OrderItemStatus::DELIVERED_TO_FIRST_HUB->value]);
            });

            $order->driver()->update(['is_available' => true]);
        }

        $order->update(['status' => $nextStatus]);
        event(new OrderStatusUpdated($order));

        return $order;
    }

    protected function getPostPickupStatus(Order $order): OrderStatus
    {
        if ($order->shipping_type_id === 3 && $order->is_express) {
            return OrderStatus::DELIVERED_TO_LOCAL_HUB;
        }

        return OrderStatus::DELIVERED;
    }

    public function cancel(Order $order, ?string $reason = null): Order
    {
        if (! in_array($order->status, [
            OrderStatus::READY_TO_PICKUP,
            OrderStatus::HEADING_TO_PICKUP,
            OrderStatus::ARRIVED_AT_PICKUP,
        ])) {
            throw new BadRequestHttpException(__('Only orders not yet picked up can be cancelled.'));
        }

        $order->update([
            'status' => OrderStatus::CANCELLED,
            'cancel_reason' => $reason,
        ]);

        $order->driver()->update(['is_available' => true]);

        event(new OrderStatusUpdated($order));

        return $order;
    }

    public function markAsDeliveryFailed(Order $order, ?string $reason = null): Order
    {
        if ($order->is_express && ! $order->status === OrderStatus::ARRIVED_AT_DELIVERY_LOCATION) {
            throw new BadRequestHttpException(__('Order must be at delivery location to mark as delivery failed.'));
        }

        $order->update([
            'status' => OrderStatus::DELIVERY_FAILED,
            'cancel_reason' => $reason,
        ]);

        $order->driver()->update(['is_available' => true]);

        event(new OrderStatusUpdated($order));

        return $order;
    }
}
