<?php

namespace App\Repositories;

use App\Models\CompanyAdmin;

class CompanyAdminRepository
{
    public function __construct(private readonly CompanyAdmin $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByEmail(string $email)
    {
        return $this->model->where('email', $email)->first();
    }

    public function getByPhone($country_code, $phone)
    {
        return $this->model->where('country_code', $country_code)->where('phone', $phone)->first();
    }

    public function register(array $data)
    {
        return $this->model->create([
            'name' => $data['admin_name'],
            'company_id' => $data['company_id'],
            'country_code' => $data['admin_country_code'],
            'phone' => $data['admin_phone'],
            'email' => $data['admin_email'],
            'password' => $data['admin_password'],
            'role_id' => $data['admin_role_id'],
            'is_super_admin' => $data['is_super_admin'],
        ]);
    }

    public function create(array $data)
    {
        return $this->model->create([
            'name' => $data['name'],
            'company_id' => $data['company_id'],
            'country_code' => $data['country_code'],
            'phone' => $data['phone'],
            'email' => $data['email'],
            'password' => $data['password'],
            'role_id' => $data['role_id'],
            'is_super_admin' => 0,
        ]);
    }

    public function update(CompanyAdmin $admin, array $data): void
    {
        $admin->update($data);
    }

    public function updatePassword(CompanyAdmin $admin, string $password): void
    {
        $admin->update([
            'password' => bcrypt($password),
        ]);
    }

    public function verifyEmail(string $email): void
    {
        $this->model->where('email', $email)->update(['email_verified_at' => now()]);
    }

    public function invalidateUniqueData(CompanyAdmin $admin): void
    {
        $admin->update([
            'phone' => getInvalidatedValue($admin->phone),
            'email' => getInvalidatedValue($admin->email),
        ]);
    }
}
