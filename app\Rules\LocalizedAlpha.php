<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class LocalizedAlpha implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // This rule is designed to work with individual locale fields, not arrays
        // The validation should be applied to each locale field separately
        // For example: name.en, name.ar, etc.

        // Skip empty values (let required rule handle this)
        if (empty($value)) {
            return;
        }

        // Extract locale from attribute (e.g., "name.en" -> "en")
        $parts = explode('.', $attribute);
        $locale = end($parts);

        // Get configured locales
        $configuredLocales = config('app.locales', ['en', 'ar']);

        // Skip if locale is not configured
        if (! in_array($locale, $configuredLocales)) {
            return;
        }

        // Validate based on locale
        $valid = match ($locale) {
            'ar', 'ur' => preg_match('/^[\p{Arabic}\d\s\-_]+$/u', (string) $value),
            'en' => preg_match('/^[A-Za-z\d\s\-_]+$/', (string) $value),
            default => preg_match('/^[\p{L}\d\s\-_]+$/u', (string) $value),
        };

        if ($valid === 0 || $valid === false) {
            $fail(__("validation.custom.localized_alpha.{$locale}", [
                'attribute' => __("attributes.$attribute"),
            ]));
        }
    }
}
