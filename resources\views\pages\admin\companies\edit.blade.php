<x-layout :title="__('Edit Company')">
    <x-session-message />
    <form action="{{ route('admin.companies.update', $company->id) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        <div class="card">
            <div class="card-body">

                <!-- BASIC INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Basic Info') }}</h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Company Name') }}</b></label>
                            <input type="text" name="name" class="form-control" value="{{ old('name', $company->name) }}">
                            <x-input-error name="name" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Business Registration Number') }}</b></label>
                            <input type="text" name="business_registration_number" class="form-control" value="{{ old('business_registration_number', $company->business_registration_number) }}">
                            <x-input-error name="business_registration_number" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Email') }}</b></label>
                            <input type="text" name="email" class="form-control" value="{{ old('email', $company->email) }}" autocomplete="off">
                            <x-input-error name="email" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Phone') }}</b></label>
                            <x-phone-input :countries="$countries" :country_code="old('country_code', $company->country_code)" :phone="old('phone', $company->phone)" />
                            <x-input-error name="phone" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Address') }}</b></label>
                            <input type="text" name="address" class="form-control" value="{{ old('address', $company->address) }}">
                            <x-input-error name="address" />
                        </div>

                        <!-- Status -->
                        <div class="mb-3 col-lg-4">
                            <label style="margin-bottom:15px" class="form-label"><b>{{ __('Status') }}</b></label><br>
                            <label class="switch">
                                <input type="checkbox" class="switch-input" name="status" @checked(old('status', $company->status) == 'active')>
                                <span class="switch-toggle-slider">
                                    <span class="switch-on"></span>
                                    <span class="switch-off"></span>
                                </span>
                                <span class="switch-label">{{ __("active") }}</span>
                            </label>
                            <x-input-error name="status" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Logo') }}</b></label>
                            <input type="file" name="logo" class="form-control">
                            <x-input-error name="logo" />
                        </div>

                         <!-- Current Logo -->
                        @if($company->logo)
                        <div class="mb-4 col-lg-4">
                            <label class="form-label"><b>{{ __('Current Logo') }}</b></label>
                            <div class="mt-2">
                                <a href="{{ $company->logo->url }}" target="_blank">
                                    <img src="{{ $company->logo->url }}" alt="Current Logo" class="card-image">
                                </a>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- BANK ACCOUNT INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Bank Account Info') }}</h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Bank Name') }}</b></label>
                            <input type="text" name="bank_name" class="form-control" value="{{ old('bank_name', $company->bank_name) }}">
                            <x-input-error name="bank_name" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Bank Account Owner') }}</b></label>
                            <input type="text" name="bank_account_owner" class="form-control" value="{{ old('bank_account_owner', $company->bank_account_owner) }}">
                            <x-input-error name="bank_account_owner" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Bank Account Number') }}</b></label>
                            <input type="text" name="bank_account_number" class="form-control" value="{{ old('bank_account_number', $company->bank_account_number) }}">
                            <x-input-error name="bank_account_number" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('IBAN') }}</b></label>
                            <input type="text" name="iban" class="form-control" value="{{ old('iban', $company->iban) }}">
                            <x-input-error name="iban" />
                        </div>
                    </div>
                </div>

                <!-- ADMIN INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Admin Info') }}</h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Admin Name') }}</b></label>
                            <input type="text" name="admin_name" class="form-control" value="{{ old('admin_name', $company->superAdmin->name) }}">
                            <x-input-error name="admin_name" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Admin Email') }}</b></label>
                            <input type="text" name="admin_email" class="form-control" value="{{ old('admin_email', $company->superAdmin->email) }}">
                            <x-input-error name="admin_email" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Admin Phone') }}</b></label>
                            <x-phone-input :countries="$countries" :country_code="old('admin_country_code', $company->superAdmin->country_code)" :phone="old('admin_phone', $company->superAdmin->phone)" prefix="admin_" />
                            <x-input-error name="admin_phone" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Password') }}</b></label>
                            <input type="password" name="admin_password" class="form-control" autocomplete="new-password">
                            <x-input-error name="admin_password" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Confirm Password') }}</b></label>
                            <input type="password" name="admin_password_confirmation" class="form-control">
                        </div>
                    </div>
                </div>

             <!-- DOCUMENTS -->
            <div class="border rounded p-3 mb-4">
                <h5 class="mb-3">{{ __('Certificates') }}</h5>
                <div class="row">

                    <!-- Commercial Registration Certificate -->
                    <div class="mb-4 col-lg-4">
                        <label><b>{{ __('Commercial Registration Certificate') }}</b></label>
                        <input type="file" name="commercial_registration_certificate" class="form-control">
                        <x-input-error name="commercial_registration_certificate" />

                        @if($company->commercialRegistrationCertificate)
                            <label class="form-label mt-4"><b>{{ __('Current Commercial Registration Certificate') }}</b></label>
                            <div class="mt-1">
                                <a href="{{ $company->commercialRegistrationCertificate->url }}" target="_blank">
                                    <img src="{{ $company->commercialRegistrationCertificate->url }}" alt="Commercial Registration Certificate" class="card-image">
                                </a>
                            </div>
                        @endif
                    </div>

                    <!-- Cargo Insurance Certificate -->
                    <div class="mb-4 col-lg-4">
                        <label><b>{{ __('Cargo Insurance Certificate') }}</b></label>
                        <input type="file" name="cargo_insurance_certificate" class="form-control">
                        <x-input-error name="cargo_insurance_certificate" />

                        @if($company->cargoInsuranceCertificate)
                            <label class="form-label mt-4"><b>{{ __('Current Cargo Insurance Certificate') }}</b></label>
                            <div class="mt-1">
                                <a href="{{ $company->cargoInsuranceCertificate->url }}" target="_blank">
                                    <img src="{{ $company->cargoInsuranceCertificate->url }}" alt="Cargo Insurance Certificate" class="card-image">
                                </a>
                            </div>
                        @endif
                    </div>

                    <!-- Tax Certificate -->
                    <div class="mb-4 col-lg-4">
                        <label><b>{{ __('Tax Certificate') }}</b></label>
                        <input type="file" name="tax_certificate" class="form-control">
                        <x-input-error name="tax_certificate" />

                        @if($company->taxCertificate)
                            <label class="form-label mt-4"><b>{{ __('Current Tax Certificate') }}</b></label>
                            <div class="mt-1">
                                <a href="{{ $company->taxCertificate->url }}" target="_blank">
                                    <img src="{{ $company->taxCertificate->url }}" alt="Tax Certificate" class="card-image">
                                </a>
                            </div>
                        @endif
                    </div>

                </div>
            </div>

                <button type="submit" class="btn btn-outline-primary">{{ __('Update') }}</button>

            </div>
        </div>
    </form>
</x-layout>
