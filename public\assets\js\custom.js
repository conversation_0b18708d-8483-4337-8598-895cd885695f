// Change Delete Model Info
function changeDeleteModalData(e) {
    $("#delete-form").attr('action', $(e).attr('delete-route'));
    $("#model-name").text($(e).attr('delete-name'));
    console.log(e);
}

// datatable filters
function dt_filter() {
    const filtersDiv = document.getElementById('filters');

    if (!filtersDiv) {
        console.error("Missing #filters element. must be with id:filters");
        return;
    }

    // Get the datatable ID
    const tableId = filtersDiv.dataset.datatableId;

    if (!tableId) {
        console.error("Missing 'data-datatable-id' attribute on #filters element.");
        return;
    }

    // Get the datatable
    const dataTable = window.LaravelDataTables?.[tableId];

    if (!dataTable) {
        console.error(`DataTable with ID '${tableId}' not found`);
        return;
    }

    // Ensure inputs will be added to the request
    dataTable.off('preXhr.dt').on('preXhr.dt', function (e, settings, data) {
        filtersDiv.querySelectorAll('input, select').forEach(el => {
            if (el.name) {
                data[el.name] = el.value;
            }
        });
    });

    // Reload the table with new filters
    dataTable.ajax.reload();
}