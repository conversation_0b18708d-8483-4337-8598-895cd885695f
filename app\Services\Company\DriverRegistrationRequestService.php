<?php

namespace App\Services\Company;

use App\Models\Driver;
use App\Repositories\DriverRepository;
use App\Services\MediaService;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class DriverRegistrationRequestService
{
    public function __construct(private readonly DriverRepository $driverRepository, private readonly MediaService $mediaService) {}

    public function getDriver(string $id)
    {
        $driver = $this->driverRepository->getById($id);

        if ($driver->approval_status == 'approved' || $driver->company_id != auth('company')->user()->company_id) {
            abort(404);
        }

        return $driver;
    }

    public function updateApprovalStatus(string $id): void
    {
        $driver = $this->driverRepository->getById($id);

        if ($driver->company_id != auth('company')->user()->company_id) {
            abort(404);
        }

        if ($driver->approval_status == 'approved') {
            throw new BadRequestHttpException('Driver is already approved');
        }

        if (request('status') == 'on_review') {
            $this->updateToOnReview($driver);
        }

        if (request('status') == 'approved') {
            $this->updateToApproved($driver);
        }

        if (request('status') == 'rejected') {
            $this->updateToRejected($driver);
        }
    }

    public function updateToOnReview(Driver $driver): void
    {
        if ($driver->approval_status != 'pending') {
            throw new BadRequestHttpException('Registration request is not pending');
        }

        $this->driverRepository->update($driver, ['approval_status' => 'on_review']);
    }

    public function updateToApproved(Driver $driver): void
    {
        if ($driver->approval_status != 'on_review') {
            throw new BadRequestHttpException('Registration request is not on review');
        }

        $this->driverRepository->update($driver, ['approval_status' => 'approved']);

        // Send Mail To Super Admin
    }

    public function updateToRejected(Driver $driver): void
    {
        if ($driver->approval_status != 'on_review') {
            throw new BadRequestHttpException('Registration request is not on review');
        }

        DB::transaction(function () use ($driver): void {
            $driver->forceDelete();
            $this->mediaService->deleteAllMedia($driver);
        });
    }
}
