<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;
use App\Models\Admin;
use App\Rules\ActiveRole;
use App\Rules\AdminPermission;
use App\Rules\AdminRole;
use App\Rules\UniquePhone;
use App\Rules\ValidMedia;
use App\Rules\ValidPassword;
use App\Rules\ValidPhone;
use Illuminate\Validation\Rule;

class AdminRequest extends BaseFormRequest
{
    public function rules(): array
    {
        $admin = $this->route('admin');

        return [
            'name' => ['required', 'string', 'min:4', 'max:30'],
            'country_code' => ['required', 'string', 'exists:countries,code'],
            'phone' => ['required', 'string', new ValidPhone($this->country_code), new UniquePhone($this->country_code, Admin::class, $admin?->id)],
            'email' => ['required', 'string', 'email:rfc,dns', 'max:255', Rule::unique('admins', 'email')->ignore($admin?->id)],
            'password' => [Rule::requiredIf(! (bool) $admin), 'nullable', 'string',  new ValidPassword, 'confirmed'],
            'role_id' => ['required', 'string', 'exists:roles,id', new ActiveRole, new AdminRole],
            'permissions' => ['required', 'array'],
            'permissions.*' => ['required', 'exists:permissions,id', new AdminPermission],
            'image' => ['nullable', new ValidMedia(['image'])],
        ];
    }

    public function messages()
    {
        return [
            'permissions.required' => __('must select at least one permission'),
        ];
    }
}
