<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('company_shipping_type_size_transportion_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_shipping_type_id')->constrained(indexName: 'transportion_method_company_shipping_type_id')->onDelete('cascade');
            $table->foreignId('company_shipping_type_size_id')->nullable()->constrained(indexName: 'company_shipping_type_size_id')->cascadeOnDelete();
            $table->foreignId('transportion_method_id')->constrained(indexName: 'company_shipping_transportion_method_id')->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('company_shipping_type_size_transportion_methods');
    }
};
