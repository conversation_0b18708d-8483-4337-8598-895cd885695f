@php
    $admin = auth('admin')->user();
@endphp

<x-layout :title="__('Countries')">
    <x-session-message />
    <div class="card">
        <div class="card-heading">
            <p>{{ __('Countries') }}</p>
            <div>
                @if ($admin->hasPermission('create country'))
                    <a class="btn btn-primary waves-effect waves-light"
                        href="{{ route('admin.countries.create') }}">{{ __('Create') }}</a>
                @endif
            </div>
        </div>
        <div class="row" id="filters" data-datatable-id="countries-table">
            <!-- Search -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Search') }}</b></label>
                <input type="text" name="search_param" class="form-control" onchange="dt_filter()">
            </div>
        </div>
        <div class="card-body">
            <div class="card-datatable table-responsive pt-0">
                {{ $dataTable->table(['class' => 'datatables-basic table table-striped']) }}
            </div>
            <x-delete-modal />
        </div>
    </div>

    @push('css')
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}" />
        <link rel="stylesheet"
            href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}" />
    @endpush

    @push('js')
        <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
        {{ $dataTable->scripts(attributes: ['type' => 'module']) }}
    @endpush
</x-layout>
