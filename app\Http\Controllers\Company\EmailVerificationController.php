<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\Company\SendVerificationCodeRequest;
use App\Http\Requests\Company\VerifyEmailRequest;
use App\Mail\EmailVerificationMail;
use App\Models\CompanyAdmin;
use App\Repositories\CompanyAdminRepository;
use App\Repositories\EmailVerificationCodeRepository;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;

class EmailVerificationController extends Controller
{
    public function __construct(
        private readonly CompanyAdminRepository $companyAdminRepository,
        private readonly EmailVerificationCodeRepository $emailVerificationCodeRepository,
    ) {}

    public function sendCode(SendVerificationCodeRequest $request)
    {
        if (RateLimiter::tooManyAttempts("send-verification-code-attempts-company-admin:$request->email", 3)) {

            $seconds = RateLimiter::availableIn("send-verification-code-attempts-company-admin:$request->email");
            $remainingTime = formatSecondsToHoursTime($seconds);

            return errorMessage(__('limit reached: retry after :time hours', ['time' => $remainingTime]));
        }

        RateLimiter::hit("send-verification-code-attempts-company-admin:$request->email", 12 * 60 * 60);

        $code = generatePassCode();

        DB::transaction(function () use ($code, $request): void {
            $this->emailVerificationCodeRepository->create(CompanyAdmin::class, $request->email, $code);
            Mail::to($request->email)->send(new EmailVerificationMail($code));
        });

        return successMessage(__('code sent successfully'));
    }

    public function verify(VerifyEmailRequest $request)
    {
        if (RateLimiter::tooManyAttempts("verify-email-company-admin:$request->email", 6)) {
            throw new ThrottleRequestsException;
        }

        RateLimiter::hit("verify-email-company-admin:$request->email", 60);

        $code = $this->emailVerificationCodeRepository->get(CompanyAdmin::class, $request->email, $request->code);

        if (! $code) {
            return errorMessage(__('invalid code'));
        }

        $latest_code = $this->emailVerificationCodeRepository->getLatest(CompanyAdmin::class, $request->email);

        if ($latest_code->code != $request->code) {
            return errorMessage(__('please enter the latest sent code'));
        }

        // epire code after 1 hour
        if ((time() - strtotime((string) $code->created_at)) > (60 * 60)) {
            return errorMessage(__('expired code'));
        }

        $this->companyAdminRepository->verifyEmail($request->email);

        $this->emailVerificationCodeRepository->deleteAll(CompanyAdmin::class, $request->email);

        return successMessage(__('email verified successfully'));
    }

    public function canSendCode(SendVerificationCodeRequest $request)
    {
        $isAllowed = ! RateLimiter::tooManyAttempts("send-verification-code-attempts-company-admin:$request->email", 3);

        return success(['is_allowed' => $isAllowed]);
    }
}
