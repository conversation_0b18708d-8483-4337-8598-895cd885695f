<x-layout :title="__('Vehicles')">
    <x-session-message />

    <div class="card">
        <div class="card-heading">
            <p class="mb-0">{{ __('Vehicles') }}</p>
            @if(auth('company')->user()->hasPermission('create vehicle'))
                <a class="btn btn-sm btn-outline-primary" href="{{ route('company.vehicles.create') }}">{{ __('Create') }}</a>
            @endif
        </div>

        <!-- Filters -->
        <div class="row" id="filters" data-datatable-id="vehicles-table">
            <!-- Search -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Search') }}</b></label>
                <input type="text" name="search_param" class="form-control" onchange="dt_filter()">
            </div>

            <!-- Approval Status -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Approval Status') }}</b></label>
                <select name="approval_status" class="select2 form-select form-select-lg" data-placeholder="{{ __('Choose') }}..." data-allow-clear="true" onchange="dt_filter()">
                    <option></option>
                    <option value="pending">{{ __('Pending') }}</option>
                    <option value="approved">{{ __('Approved') }}</option>
                    <option value="rejected">{{ __('Rejected') }}</option>
                </select>
            </div>

            <!-- Transportion Method -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Transportion Method') }}</b></label>
                <select name="transportion_method_id" class="select2 form-select form-select-lg"
                    data-placeholder="{{ __('Choose') }}..." data-allow-clear="true" onchange="dt_filter()">
                    <option></option>
                    @foreach($transportionMethods as $method)
                        <option value="{{ $method->id }}">{{ $method->name }}</option>
                    @endforeach
                </select>
            </div>
        </div>

        <div class="card-body">
            <div class="card-datatable table-responsive pt-0">
                {{ $dataTable->table(['class' => 'datatables-basic table table-striped']) }}
            </div>
            <x-delete-modal />
        </div>
    </div>

    @push('css')
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}" />
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}" />
    @endpush

    @push('js')
        <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
        {{ $dataTable->scripts(attributes: ['type' => 'module']) }}
    @endpush
</x-layout>
