name: PHPStan

on:
  push:
    paths:
      - '**.php'
      - 'phpstan.neon'
      - '.github/workflows/phpstan.yml'

jobs:
  phpstan:
    name: phpstan
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd,grpc, exif, iconv, imagick, fileinfo, rdkafka
          php-version: '8.4'
          coverage: none

      - name: Install composer dependencies
        uses: ramsey/composer-install@v3

      - name: Run PHPStan
        run: ./vendor/bin/phpstan --error-format=github
