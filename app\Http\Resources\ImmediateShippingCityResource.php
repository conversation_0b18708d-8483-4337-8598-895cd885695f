<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ImmediateShippingCityResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->city->id,
            'name' => $this->city->name,
            'areas' => AreaResource::collection($this->areas),
        ];
    }
}
