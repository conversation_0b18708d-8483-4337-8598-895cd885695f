<?php

namespace App\Repositories;

use App\Models\Chat;
use App\Models\Order;
use App\Models\OrderItem;

class ChatRepository
{
    public function __construct(private readonly Chat $model) {}

    public function firstOrCreate(Order|OrderItem $chatable)
    {
        return $this->model->firstOrCreate([
            'chatable_id' => $chatable->id,
            'chatable_type' => $chatable::class,
        ]);
    }

    public function create(Order|OrderItem $chatable)
    {
        return $this->model->create([
            'chatable_id' => $chatable->id,
            'chatable_type' => $chatable::class,
        ]);
    }

    public function getMessages(Chat $chat)
    {
        return $chat->messages()->orderBy('id', 'desc')->get();
    }

    public function createMessage(Chat $chat, array $data)
    {
        return $chat->messages()->create($data);
    }
}
