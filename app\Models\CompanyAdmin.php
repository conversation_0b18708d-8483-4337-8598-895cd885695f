<?php

namespace App\Models;

use App\Traits\HasPermissions;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;

class CompanyAdmin extends Authenticatable
{
    use HasPermissions, SoftDeletes;

    protected $fillable = [
        'name',
        'company_id',
        'country_code',
        'phone',
        'email',
        'password',
        'role_id',
        'status',
        'remember_token',
        'is_super_admin',
    ];

    protected function casts(): array
    {
        return [
            'is_super_admin' => 'boolean',
        ];
    }

    public function getPhoneAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getEmailAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    public function image()
    {
        return $this->morphOne(Media::class, 'mediable');
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function getImageUrlAttribute()
    {
        return $this->image ? $this->image->url : asset('user-default.png');
    }
}
