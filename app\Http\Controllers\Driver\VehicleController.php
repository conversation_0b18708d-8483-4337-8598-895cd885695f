<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Http\Resources\Driver\DriverVehicleResource;
use App\Models\Vehicle;
use App\Repositories\DriverVehicleRepository;
use App\Services\Driver\VehicleService;

class VehicleController extends Controller
{
    public function __construct(private readonly VehicleService $vehicleService, private readonly DriverVehicleRepository $driverVehicleRepository) {}

    public function index()
    {
        $vehicles = $this->driverVehicleRepository->getDriverVehicles(auth('driver')->user());

        return success(DriverVehicleResource::collection($vehicles));
    }

    public function accept(Vehicle $vehicle)
    {
        $this->vehicleService->acceptVehicle($vehicle);

        return success(true);
    }

    public function activate(Vehicle $vehicle)
    {
        $this->vehicleService->activateVehicle($vehicle);

        return success(true);
    }
}
