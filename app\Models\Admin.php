<?php

namespace App\Models;

use App\Traits\HasPermissions;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;

class Admin extends Authenticatable
{
    use HasPermissions, SoftDeletes;

    protected $fillable = [
        'name',
        'country_code',
        'phone',
        'email',
        'password',
        'role_id',
        'status',
        'remember_token',
        'email_verified_at',
    ];

    public function getPhoneAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getEmailAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function image(): MorphOne
    {
        return $this->morphOne(Media::class, 'mediable');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    public function permissions(): MorphToMany
    {
        return $this->morphToMany(Permission::class, 'model', 'model_permission', 'model_id', 'permission_id');
    }

    public function getImageUrlAttribute()
    {
        return $this->image ? $this->image->url : asset('user-default.png');
    }
}
