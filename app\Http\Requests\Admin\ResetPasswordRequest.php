<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;
use App\Rules\ValidPassword;
use Illuminate\Contracts\Validation\Validator;

class ResetPasswordRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'password' => ['required', 'string', new ValidPassword, 'confirmed'],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        session()->flash('email', $this->email);
        session()->flash('code', $this->code);

        parent::failedValidation($validator);
    }
}
