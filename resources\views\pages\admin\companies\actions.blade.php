@php
    $canShow = auth('admin')->user()->hasPermission('show company');
    $canEdit = auth('admin')->user()->hasPermission('update company');
    $canDelete = auth('admin')->user()->hasPermission('delete company');
@endphp

<div class="actions">
    @if ($canShow || $canEdit || $canDelete)
        @if ($canShow)
            <a href="{{ route('admin.companies.show', $id) }}"><i class="ti ti-eye"></i></a>
        @endif

        @if ($canEdit)
            <a href="{{ route('admin.companies.edit', $id) }}"><i class="ti ti-edit"></i></a>
        @endif

        @if ($canDelete)
            <a href="javascript:void(0)">
                <i data-bs-toggle="modal" data-bs-target="#delete-modal" onclick="changeDeleteModalData(this)"
                    delete-route="{{ route('admin.companies.destroy', $id) }}"
                    delete-name="{{ __('Company') }} : {{ $name }}" class="ti ti-archive">
                </i>
            </a>
        @endif
    @else
        -
    @endif
</div>
