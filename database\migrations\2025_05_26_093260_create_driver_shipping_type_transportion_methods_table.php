<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('driver_shipping_type_transportion_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('driver_shipping_type_id')->constrained(indexName: 'driver_transportion_method_shipping_type')->cascadeOnDelete();
            $table->foreignId('transportion_method_id')->constrained(indexName: 'driver_transportion_method')->cascadeOnDelete();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('driver_shipping_type_transportion_methods');
    }
};
