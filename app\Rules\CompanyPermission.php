<?php

namespace App\Rules;

use App\Repositories\PermissionRepository;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CompanyPermission implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $permissionRepository = app(PermissionRepository::class);

        $permission = $permissionRepository->getById($value);

        if ($permission && $permission->type != 'company') {
            $fail(__('not company permission'));
        }
    }
}
