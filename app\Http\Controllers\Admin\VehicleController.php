<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\VehiclesDataTable;
use App\DTO\Admin\VehicleDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Vehicle\StoreRequest;
use App\Http\Requests\Admin\Vehicle\UpdateRequest;
use App\Models\Company;
use App\Models\Vehicle;
use App\Repositories\CompanyRepository;
use App\Repositories\TransportionMethodRepository;
use App\Services\Admin\VehicleService;

class VehicleController extends Controller
{
    public function __construct(
        private readonly VehicleService $vehicleService,
        private readonly CompanyRepository $companyRepository,
        private readonly TransportionMethodRepository $transportionMethodRepository
    ) {
        //
    }

    /**
     * Display a listing of the resource.
     */
    public function index(VehiclesDataTable $dataTable)
    {
        return $dataTable->render('pages.admin.vehicles.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $companies = $this->companyRepository->getApprovedCompaniesDdl();

        return view('pages.admin.vehicles.create', ['companies' => $companies]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRequest $request)
    {
        $dto = VehicleDTO::from($request->validated());
        $this->vehicleService->create($dto);

        return to_route('admin.vehicles.index')->with('success', __('Vehicle created successfully'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Vehicle $vehicle)
    {
        $vehicle->load([
            'company',
            'photo',
            'registrationLicense',
            'drivingLicense',
            'insurancePolicy',
            'transportionMethod',
        ]);

        return view('pages.admin.vehicles.show', ['vehicle' => $vehicle]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Vehicle $vehicle)
    {
        if ($vehicle->approval_status === 'rejected') {
            return back()->with('fail', __('Vehicle is rejected and cannot be edited.'));
        }

        $vehicle->load('photo', 'registrationLicense', 'drivingLicense', 'insurancePolicy');

        $companies = $this->companyRepository->getApprovedCompaniesDdl();

        return view('pages.admin.vehicles.edit', ['vehicle' => $vehicle, 'companies' => $companies]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRequest $request, Vehicle $vehicle)
    {
        $dto = VehicleDTO::from($request->validated());
        $this->vehicleService->update($vehicle, $dto);

        return to_route('admin.vehicles.index')->with('success', __('Vehicle updated successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Vehicle $vehicle)
    {
        $this->vehicleService->delete($vehicle);

        return to_route('admin.vehicles.index')->with('success', __('Vehicle deleted successfully'));
    }

    /**
     * Update the approval status of a vehicle.
     */
    public function updateApprovalStatus(Vehicle $vehicle)
    {
        $this->vehicleService->updateApprovalStatus($vehicle, request('status'));

        if (request()->expectsJson()) {
            return response()->json([
                'message' => __('Vehicle approval status updated successfully'),
                'approval_status' => view('pages.admin.vehicles.approval-status', ['vehicle' => $vehicle])->render(),
            ], 200);
        }

        return back()->with('success', __('Vehicle approval status updated successfully'));
    }

    /**
     * Get associated transportion methods for a company
     */
    public function getAssociatedTransportionMethods(Company $company)
    {
        $transportionMethodsIds = $company->shippingTypes
            ->flatMap->sizes
            ->flatMap->transportionMethods
            ->pluck('transportion_method_id')
            ->unique()
            ->toArray();

        return $this->transportionMethodRepository->getByIds($transportionMethodsIds);
    }
}
