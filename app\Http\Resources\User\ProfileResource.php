<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProfileResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'name' => $this->name,
            'email' => $this->email,
            'country_code' => $this->country_code,
            'phone' => $this->phone,
            'app_locale' => $this->app_locale,
            'push_notifications_enabled' => $this->push_notifications_enabled,
            'image' => $this->image?->url,
        ];
    }
}
