<?php

namespace App\Http\Resources\Driver;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VehicleResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'model' => $this->model,
            'plate_number' => $this->plate_number,
            'license_number' => $this->license_number,
            'year' => $this->year,
            'photo' => $this->photo?->url,
        ];
    }
}
