<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\CompanyRegistrationRequestsDataTable;
use App\Http\Controllers\Controller;
use App\Services\Admin\CompanyRegistrationRequestService;

class CompanyRegistrationRequestController extends Controller
{
    public function __construct(private readonly CompanyRegistrationRequestService $companyRegistrationRequestService) {}

    public function index(CompanyRegistrationRequestsDataTable $dataTable)
    {
        return $dataTable->render('pages.admin.companies.registration-requests.index');
    }

    public function show(string $id)
    {
        $company = $this->companyRegistrationRequestService->getCompany($id);

        return view('pages.admin.companies.show', ['company' => $company]);
    }

    public function updateApprovalStatus(string $id)
    {
        $this->companyRegistrationRequestService->updateApprovalStatus($id);

        if (request()->expectsJson()) {
            $company = $this->companyRegistrationRequestService->getCompany($id);

            return response()->json([
                'message' => 'Company registration request updated successfully',
                'approval_status' => view('pages.admin.companies.registration-requests.approval-status', ['company' => $company])->render(),
            ], 200);
        }

        return redirect()->route('admin.company-registration-requests.index')->with('success', 'Company registration request updated successfully');
    }
}
