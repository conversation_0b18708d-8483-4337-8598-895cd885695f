<?php

namespace App\Services\Driver;

use App\Contracts\OrderItemStatusHandlerInterface;
use App\Models\OrderItem;
use App\Repositories\OrderItemRepository;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class OrderItemService
{
    public function __construct(
        private readonly OrderItemRepository $orderItemRepository,
        private readonly OrderItemStatusHandlerInterface $normalService
    ) {}

    protected function getStatusHandler(OrderItem $item): OrderItemStatusHandlerInterface
    {
        if ($item->order->is_express) {
            throw new BadRequestHttpException(__('Cannot update single item in express order.'));
        }

        return $this->normalService;
    }

    public function show(string $id)
    {
        $item = $this->orderItemRepository->findForDriver($id);

        if (! $item) {
            throw new NotFoundHttpException(__('Order item not found.'));
        }

        return $item;
    }

    public function updateStatus(string $id): OrderItem
    {
        return DB::transaction(function () use ($id): \App\Models\OrderItem {
            $item = $this->show($id);

            return $this->getStatusHandler($item)->update($item);
        });
    }

    public function pickupFailed(string $id): OrderItem
    {
        return DB::transaction(function () use ($id): \App\Models\OrderItem {
            $item = $this->show($id);

            return $this->getStatusHandler($item)->markAsPickupFailed($item);
        });
    }

    public function deliveryFailed(string $id): OrderItem
    {
        return DB::transaction(function () use ($id): \App\Models\OrderItem {
            $item = $this->show($id);

            return $this->getStatusHandler($item)->markAsDeliveryFailed($item);
        });
    }
}
