<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\ChatMessageRequest;
use App\Http\Requests\User\GetChatRequest;
use App\Http\Resources\ChatMessageResource;
use App\Services\ChatService;

class ChatController extends Controller
{
    public function __construct(private readonly ChatService $chatService) {}

    public function get(GetChatRequest $request)
    {
        $messages = $this->chatService->getChatMessages();

        return success(ChatMessageResource::collection($messages));
    }

    public function sendMessage(ChatMessageRequest $request)
    {
        $this->chatService->sendMessage();

        return success(true);
    }
}
