let map;
let drawingManager;
let areaPolygon = null;
let cityPolygon = null;
let polygonCoordinates = [];
let searchBox;

// Add event listener for city selection
document.addEventListener('DOMContentLoaded', () => {
    const citySelect = document.getElementById('city_id');
    if (citySelect) {
        citySelect.addEventListener('change', async function () {
            const cityId = this.value;
            let savedAreaCoords = null;

            // Save existing area polygon data if available
            if (areaPolygon) {
                savedAreaCoords = areaPolygon.getPath().getArray().map(p => [p.lng(), p.lat()]);
                areaPolygon.setMap(null);
                areaPolygon = null;
            }

            if (cityPolygon) {
                cityPolygon.setMap(null);
                cityPolygon = null;
            }

            if (cityId) {
                try {
                    const response = await fetch(`/admin-dashboard/cities/${cityId}/coordinates`, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    });
                    if (!response.ok) throw new Error('Failed to fetch city coordinates');
                    const data = await response.json();

                    document.getElementById('city-coordinates-json').value = JSON.stringify(data.coordinates);

                    if (data.coordinates) {
                        drawCityPolygon(data.coordinates);

                        // Redraw area polygon AFTER city is drawn
                        if (savedAreaCoords && savedAreaCoords.length >= 3) {
                            const path = savedAreaCoords.map(coord => ({
                                lng: coord[0],
                                lat: coord[1]
                            }));
                            setTimeout(() => {
                                areaPolygon = new google.maps.Polygon({
                                    paths: path,
                                    fillColor: '#4099FF',
                                    fillOpacity: 0.3,
                                    strokeWeight: 2,
                                    strokeColor: '#4099FF',
                                    editable: true,
                                    zIndex: 999
                                });
                                areaPolygon.setMap(map);
                                attachPolygonListeners(areaPolygon);
                                updatePolygonCoordinates(areaPolygon);
                            }, 200); // ensure city polygon fully rendered first
                        }
                    }
                } catch (error) {
                    console.error('Error fetching city coordinates:', error);
                    alert('Failed to load city boundaries');
                }
            } else {
                document.getElementById('city-coordinates-json').value = '';
            }
        });

        if (citySelect.value) {
            citySelect.dispatchEvent(new Event('change'));
        }
    }
});

function initMap() {
    const center = { lat: 23.4241, lng: 53.8478 };
    map = new google.maps.Map(document.getElementById('area-map'), {
        zoom: 8,
        center: center,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        mapTypeControl: true,
        streetViewControl: false,
        fullscreenControl: true,
        zoomControl: true
    });

    const input = document.getElementById('map-search-input');
    searchBox = new google.maps.places.SearchBox(input);
    map.controls[google.maps.ControlPosition.TOP_CENTER].push(input);
    map.addListener('bounds_changed', () => {
        searchBox.setBounds(map.getBounds());
    });
    searchBox.addListener('places_changed', () => {
        const places = searchBox.getPlaces();
        if (places.length === 0) return;
        const bounds = new google.maps.LatLngBounds();
        places.forEach(place => {
            if (!place.geometry || !place.geometry.location) return;
            map.setCenter(place.geometry.location);
            map.setZoom(14);
            if (place.geometry.viewport) {
                bounds.union(place.geometry.viewport);
            } else {
                bounds.extend(place.geometry.location);
            }
        });
        map.fitBounds(bounds);
    });

    drawingManager = new google.maps.drawing.DrawingManager({
        drawingMode: null,
        drawingControl: false,
        polygonOptions: {
            fillColor: '#4099FF',
            fillOpacity: 0.3,
            strokeWeight: 2,
            strokeColor: '#4099FF',
            clickable: true,
            editable: true,
            zIndex: 2 // ensure area polygon is on top
        }
    });
    drawingManager.setMap(map);

    google.maps.event.addListener(drawingManager, 'polygoncomplete', function (polygon) {
        if (!cityPolygon) {
            alert('Please select a city first');
            polygon.setMap(null);
            return;
        }

        if (isPolygonContained(polygon, cityPolygon)) {
            if (areaPolygon) {
                areaPolygon.setMap(null);
            }
            areaPolygon = polygon;
            updatePolygonCoordinates(polygon);
            attachPolygonListeners(polygon);
            drawingManager.setDrawingMode(null);
        } else {
            alert('The area must be completely inside the city boundaries');
            polygon.setMap(null);
        }
    });

    const savedAreaCoordinates = document.getElementById('area-coordinates-json').value;
    if (savedAreaCoordinates && savedAreaCoordinates.trim() !== '' && savedAreaCoordinates !== '[]') {
        try {
            let coords;
            try {
                coords = typeof savedAreaCoordinates === 'string' ? JSON.parse(savedAreaCoordinates) : savedAreaCoordinates;
            } catch (parseError) {
                const cleanedStr = savedAreaCoordinates.replace(/\\"/g, '"').replace(/^"/, '').replace(/"$/, '');
                try {
                    coords = JSON.parse(cleanedStr);
                } catch (e) {
                    console.error('Failed to parse area coordinates:', e);
                    return;
                }
            }

            if (coords) {
                console.log('Loaded area coordinates:', coords);
                drawSavedAreaPolygon(coords);
            }
        } catch (e) {
            console.error('Invalid saved area coordinates:', e);
        }
    }
}

function isPolygonContained(innerPolygon, outerPolygon) {
    const innerPath = innerPolygon.getPath();
    const containment = google.maps.geometry.poly.containsLocation;

    for (let i = 0; i < innerPath.getLength(); i++) {
        const point = innerPath.getAt(i);
        if (!containment(point, outerPolygon)) {
            return false;
        }
    }
    return true;
}

function drawCityPolygon(coordinates) {
    let coordsArray;
    console.log('Drawing city polygon with coordinates:', coordinates);

    if (coordinates && typeof coordinates === 'object' && coordinates.coordinates) {
        if (Array.isArray(coordinates.coordinates[0]) && coordinates.coordinates[0].length >= 3) {
            coordsArray = coordinates.coordinates[0];
        }
    } else if (Array.isArray(coordinates) && coordinates.length > 0) {
        if (Array.isArray(coordinates[0]) && Array.isArray(coordinates[0][0]) && coordinates[0].length >= 3) {
            coordsArray = coordinates[0];
        } else if (Array.isArray(coordinates[0]) && coordinates[0].length >= 2) {
            coordsArray = coordinates;
        }
    }

    if (!coordsArray) {
        console.error('Invalid city coordinates structure:', coordinates);
        return;
    }

    const path = coordsArray.map(coord => {
        if (Array.isArray(coord) && coord.length >= 2) {
            return { lat: parseFloat(coord[1]), lng: parseFloat(coord[0]) };
        } else if (coord && typeof coord === 'object' && 'lat' in coord && 'lng' in coord) {
            return { lat: parseFloat(coord.lat), lng: parseFloat(coord.lng) };
        }
        return null;
    }).filter(point => point !== null);

    if (path.length < 3) {
        console.error('Not enough valid points for city polygon:', path);
        return;
    }

    if (path.length > 1 &&
        path[0].lat === path[path.length - 1].lat &&
        path[0].lng === path[path.length - 1].lng) {
        path.pop();
    }

    cityPolygon = new google.maps.Polygon({
        paths: path,
        fillColor: '#FF0000',
        fillOpacity: 0.1,
        strokeWeight: 2,
        strokeColor: '#FF0000',
        editable: false,
        zIndex: 1 // ensure city polygon is underneath
    });

    cityPolygon.setMap(map);

    // Fit map to city bounds
    const bounds = new google.maps.LatLngBounds();
    path.forEach(point => bounds.extend(point));
    map.fitBounds(bounds);

    // Ensure area polygon renders on top
    if (areaPolygon) {
        areaPolygon.setMap(null); // remove it temporarily
        setTimeout(() => {
            areaPolygon.setMap(map); // re-add it AFTER city polygon renders
        }, 100); // slight delay to force re-layering
    }
}

function drawSavedAreaPolygon(coordinates) {
    let coordsArray;
    console.log('Drawing saved area polygon with coordinates:', coordinates);

    if (coordinates && typeof coordinates === 'object' && coordinates.coordinates) {
        if (Array.isArray(coordinates.coordinates[0]) && coordinates.coordinates[0].length >= 3) {
            coordsArray = coordinates.coordinates[0];
        }
    } else if (Array.isArray(coordinates) && coordinates.length > 0) {
        if (Array.isArray(coordinates[0]) && Array.isArray(coordinates[0][0]) && coordinates[0].length >= 3) {
            coordsArray = coordinates[0];
        } else if (Array.isArray(coordinates[0]) && coordinates[0].length >= 2) {
            coordsArray = coordinates;
        }
    }

    if (!coordsArray) {
        console.error('Invalid area coordinates structure:', coordinates);
        return;
    }

    const path = coordsArray.map(coord => {
        if (Array.isArray(coord) && coord.length >= 2) {
            return { lat: parseFloat(coord[1]), lng: parseFloat(coord[0]) };
        } else if (coord && typeof coord === 'object' && 'lat' in coord && 'lng' in coord) {
            return { lat: parseFloat(coord.lat), lng: parseFloat(coord.lng) };
        }
        return null;
    }).filter(point => point !== null);

    if (path.length < 3) {
        console.error('Not enough valid points for area polygon:', path);
        return;
    }

    if (path.length > 1 &&
        path[0].lat === path[path.length - 1].lat &&
        path[0].lng === path[path.length - 1].lng) {
        path.pop();
    }

    areaPolygon = new google.maps.Polygon({
        paths: path,
        fillColor: '#4099FF',
        fillOpacity: 0.3,
        strokeWeight: 2,
        strokeColor: '#4099FF',
        editable: true,
        zIndex: 2 // ensure area polygon is on top
    });

    if (cityPolygon && !isPolygonContained(areaPolygon, cityPolygon)) {
        alert('Warning: The saved area is not completely within the city boundaries');
    }

    areaPolygon.setMap(map);
    attachPolygonListeners(areaPolygon);

    const bounds = new google.maps.LatLngBounds();
    path.forEach(point => bounds.extend(point));
    map.fitBounds(bounds);

    updatePolygonCoordinates(areaPolygon);
}

function updatePolygonCoordinates(polygon) {
    const vertices = polygon.getPath();
    polygonCoordinates = [];
    const namedCoordinates = {};
    let sumLat = 0;
    let sumLng = 0;

    for (let i = 0; i < vertices.getLength(); i++) {
        const xy = vertices.getAt(i);
        const point = [xy.lng(), xy.lat()];
        polygonCoordinates.push(point);

        sumLat += xy.lat();
        sumLng += xy.lng();

        namedCoordinates['checkout' + (i + 1)] = {
            lat: xy.lat(),
            lng: xy.lng()
        };
    }

    if (polygonCoordinates.length > 0) {
        polygonCoordinates.push([polygonCoordinates[0][0], polygonCoordinates[0][1]]);
        const lastIndex = polygonCoordinates.length;
        namedCoordinates['checkout' + lastIndex] = {
            lat: polygonCoordinates[0][1],
            lng: polygonCoordinates[0][0]
        };

        const centerLat = sumLat / vertices.getLength();
        const centerLng = sumLng / vertices.getLength();
        document.getElementById('location-lng').value = centerLng;
        document.getElementById('location-lat').value = centerLat;
    }

    document.getElementById('area-coordinates-json').value = JSON.stringify({
        coordinates: [polygonCoordinates],
        points: namedCoordinates
    });

    const container = document.getElementById('coordinates-container');
    container.innerHTML = '';
    polygonCoordinates.forEach((point, i) => {
        const lngInput = document.createElement('input');
        lngInput.type = 'hidden';
        lngInput.name = `area[coordinates][0][${i}][]`;
        lngInput.value = point[0];
        container.appendChild(lngInput);

        const latInput = document.createElement('input');
        latInput.type = 'hidden';
        latInput.name = `area[coordinates][0][${i}][]`;
        latInput.value = point[1];
        container.appendChild(latInput);

        if (i < polygonCoordinates.length - 1) {
            const checkoutName = 'checkout' + (i + 1);
            const nameInput = document.createElement('input');
            nameInput.type = 'hidden';
            nameInput.name = `area[points][${checkoutName}][name]`;
            nameInput.value = checkoutName;
            container.appendChild(nameInput);

            const checkoutLatInput = document.createElement('input');
            checkoutLatInput.type = 'hidden';
            checkoutLatInput.name = `area[points][${checkoutName}][lat]`;
            checkoutLatInput.value = point[1];
            container.appendChild(checkoutLatInput);

            const checkoutLngInput = document.createElement('input');
            checkoutLngInput.type = 'hidden';
            checkoutLngInput.name = `area[points][${checkoutName}][lng]`;
            checkoutLngInput.value = point[0];
            container.appendChild(checkoutLngInput);
        }
    });
}

function attachPolygonListeners(polygon) {
    google.maps.event.addListener(polygon.getPath(), 'set_at', function () {
        if (cityPolygon && !isPolygonContained(polygon, cityPolygon)) {
            alert('The area must remain completely inside the city boundaries');
            return;
        }
        updatePolygonCoordinates(polygon);
    });

    google.maps.event.addListener(polygon.getPath(), 'insert_at', function () {
        if (cityPolygon && !isPolygonContained(polygon, cityPolygon)) {
            alert('The area must remain completely inside the city boundaries');
            return;
        }
        updatePolygonCoordinates(polygon);
    });
}

function initMapControls() {
    document.getElementById('draw-polygon').addEventListener('click', () => {
        if (!cityPolygon) {
            alert('Please select a city first');
            return;
        }

        if (areaPolygon) {
            areaPolygon.setMap(null);
            areaPolygon = null;
        }
        drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
    });

    document.getElementById('clear-polygon').addEventListener('click', () => {
        if (areaPolygon) {
            areaPolygon.setMap(null);
            areaPolygon = null;
        }
        document.getElementById('area-coordinates-json').value = '';
        document.getElementById('coordinates-container').innerHTML = '';
        polygonCoordinates = [];
    });
}

document.addEventListener('DOMContentLoaded', () => {
    initMap();
    initMapControls();
});
