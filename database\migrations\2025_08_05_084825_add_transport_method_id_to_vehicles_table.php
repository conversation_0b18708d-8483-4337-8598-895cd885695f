<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropForeign('vehicles_company_transportion_method_id');
            $table->dropColumn('company_shipping_type_size_transportion_method_id');
            $table->foreignId('transportion_method_id')->nullable()->constrained()->after('company_id');
            $table->string('plate_number')->nullable()->after('transportion_method_id');
            $table->string('license_number')->nullable()->after('plate_number');
            $table->date('license_expiration_date')->nullable()->after('license_number');
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])->default('pending')->after('license_expiration_date');
            $table->enum('status', ['active', 'inactive'])->default('active')->after('approval_status');
            $table->text('rejection_reason')->nullable()->after('status');
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropForeign(['transportion_method_id']);
            $table->dropColumn(['transportion_method_id', 'plate_number', 'license_number', 'license_expiration_date', 'approval_status', 'status', 'rejection_reason', 'deleted_at']);
            $table->foreignId('company_shipping_type_size_transportion_method_id')->nullable()->after('company_id')->constrained(indexName: 'vehicles_company_transportion_method_id')->cascadeOnDelete();
        });
    }
};
