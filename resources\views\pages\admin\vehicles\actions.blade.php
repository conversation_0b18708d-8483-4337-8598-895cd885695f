@php
    $canShow = auth('admin')->user()->hasPermission('show vehicle');
    $canEdit = auth('admin')->user()->hasPermission('update vehicle');
    $canDelete = auth('admin')->user()->hasPermission('delete vehicle');
@endphp

<div class="actions">
    @if ($canShow || $canEdit || $canDelete)
        @if ($canShow)
            <a href="{{ route('admin.vehicles.show', $id) }}"><i class="ti ti-eye"></i></a>
        @endif

        @if ($canEdit)
            @if ($approval_status === 'rejected')
                <a href="javascript:void(0)" style="pointer-events: none;">
                    <i class="ti ti-edit" style="color: #ccc; display: inline-block;"></i>
                </a>
            @else
                <a href="{{ route('admin.vehicles.edit', $id) }}"><i class="ti ti-edit"></i></a>
            @endif
        @endif

        @if ($canDelete)
            <a href="javascript:void(0)">
                <i data-bs-toggle="modal" data-bs-target="#delete-modal" onclick="changeDeleteModalData(this)"
                    delete-route="{{ route('admin.vehicles.destroy', $id) }}"
                    delete-name="{{ __('Vehicle') }} : {{ Arr::get($name, app()->getLocale()) }}" class="ti ti-archive">
                </i>
            </a>
        @endif
    @else
        -
    @endif
</div>
