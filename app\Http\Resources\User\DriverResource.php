<?php

namespace App\Http\Resources\User;

use App\Http\Resources\Driver\VehicleResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DriverResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $order = $this->order;
        $driver = $this->driver;

        $pickupLocation = $order->pickup_address_id
            ? $order->pickupAddress->location
            : $order->pickup_location;

        $estimatedArrivalTime = estimateTimeFromDistance(
            $driver->current_lat,
            $driver->current_lng,
            $pickupLocation->latitude ?? 0,
            $pickupLocation->longitude ?? 0
        );

        return [
            'id' => $driver->id,
            'name' => $driver->name,
            'image' => $driver->profileImage->url ?? null,
            'vehicle' => VehicleResource::make($driver->activeVehicle?->vehicle),
            'location' => [
                'lat' => $driver->current_lat,
                'lng' => $driver->current_lng,
            ],
            'estimated_arrival_time' => $estimatedArrivalTime,
            'delivery_cost' => $this->calcDeliveryCost($order, $driver),
            'accepted_at' => $this->accepted_at?->format('H:i:s') ?? null,
        ];
    }

    private function calcDeliveryCost($order, $driver): float
    {
        $distance = getDistance($driver->current_lat, $driver->current_lng, $order->pickup_location->latitude, $order->pickup_location->longitude);

        return $distance * 20;
    }
}
