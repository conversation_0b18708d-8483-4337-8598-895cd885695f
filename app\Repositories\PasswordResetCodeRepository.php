<?php

namespace App\Repositories;

use App\Models\PasswordResetCode;

class PasswordResetCodeRepository
{
    public function __construct(private readonly PasswordResetCode $model) {}

    public function create($user, $code)
    {
        return $this->model->create([
            'user_type' => $user::class,
            'email' => $user->email,
            'code' => $code,
        ]);
    }

    public function get($user, $code)
    {
        return $this->model->where(['user_type' => $user::class, 'email' => $user->email, 'code' => $code])->first();
    }

    public function getLatest($user)
    {
        return $this->model->where(['user_type' => $user::class, 'email' => $user->email])->latest()->first();
    }

    public function deleteAll($user)
    {
        return $this->model->where(['user_type' => $user::class, 'email' => $user->email])->delete();
    }
}
