<x-layout :title="__('Add Admin')">
    <x-session-message />
    <div class="card">
        <div class="card-header">{{ __('Add Admin') }}</div>
        <div class="card-body">
            <form action="{{ route('admin.admins.store') }}" method="POST" enctype="multipart/form-data">
                @csrf

                <div class="row">
                    <!-- Name -->
                    <div class="mb-3 col-lg-4">
                        <label for="name" class="form-label"><b>{{ __('Name') }}</b></label>
                        <input type="text" name="name" id="name" class="form-control" value="{{ old('name') }}">
                        <x-input-error name="name" />
                    </div>

                    <!-- Email -->
                    <div class="mb-3 col-lg-4">
                        <label for="email" class="form-label"><b>{{ __('Email') }}</b></label>
                        <input type="text" name="email" id="email" class="form-control" value="{{ old('email') }}">
                        <x-input-error name="email" />
                    </div>
                </div>

                <div class="row">
                    <!-- Phone -->
                    <div class="mb-3 col-lg-4">
                        <label class="form-label d-block"><b>{{ __('Phone') }}</b></label>
                        <x-phone-input :countries="$countries" :country_code="old('country_code')" :phone="old('phone')" />
                        <x-input-error name="phone" />
                    </div>
                </div>

                <div class="row">
                    <!-- Password -->
                    <div class="mb-3 col-lg-4">
                        <label for="password" class="form-label"><b>{{ __('Password') }}</b></label>
                        <input type="password" name="password" class="form-control" id="password" autocomplete="new-password">
                        <x-input-error name="password" />
                    </div>

                    <!-- Password Confirmation -->
                    <div class="mb-3 col-lg-4">
                        <label for="password_confirmation" class="form-label"><b>{{ __('Password Confirmation') }}</b></label>
                        <input type="password" name="password_confirmation" class="form-control" id="password_confirmation" autocomplete="new-password">
                        <x-input-error name="password_confirmation" />
                    </div>
                </div>

                <!-- Image -->
                <div class="row">
                    <div class="mb-4 col-lg-4">
                        <label for="formFile" class="form-label"><b>{{ __('Image') }}</b></label>
                        <input name="image" class="form-control" type="file" id="formFile">
                        <x-input-error name="image" />
                    </div>
                </div>

                <!-- Role -->
                <div class="row">
                    <div class="mb-3 col-lg-4">
                        <label for="role_id" class="form-label"><b>{{ __('Role') }}</b></label>
                        <select name="role_id" id="role_id" class="select2 form-select form-select-lg" data-allow-clear="true">
                            <option disabled selected>{{ __("Choose") }}...</option>
                            @foreach($roles as $role)
                            <option value="{{ $role->id }}" @selected(old('role_id')==$role->id)>{{ $role->name }}</option>
                            @endforeach
                        </select>
                        <x-input-error name="role_id" />
                    </div>
                </div>

                <!-- Permissions -->
                <div class="row mb-4">
                    <div class="col-8">
                        <label class="form-label fw-bold mb-3">{{ __('Permissions') }}:</label>

                        @foreach($permissions as $group => $perms)
                        <div class="mb-4">
                            <h6 class="text-primary fw-bold border-bottom pb-2 mb-3" style="font-size: 0.80rem;">
                                {{ __(ucwords($group)) }}
                            </h6>

                            <div class="d-flex flex-wrap gap-3">
                                @foreach($perms as $permission)
                                <div class="form-check">
                                    <input
                                        type="checkbox"
                                        name="permissions[]"
                                        class="form-check-input"
                                        id="permission-{{ $permission->id }}"
                                        value="{{ $permission->id }}"
                                        @checked(old('permissions') && in_array($permission->id, old('permissions')))
                                    >
                                    <label class="form-check-label" for="permission-{{ $permission->id }}">
                                        {{ __(explode(' ', $permission->name, 2)[0]) . ' ' . __(explode(' ', $permission->name, 2)[1]) }}
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endforeach

                        <x-input-error name="permissions" />
                    </div>
                </div>

                <button type="submit" class="btn btn-sm btn-outline-primary">{{ __('Create') }}</button>
            </form>
        </div>
    </div>

    @push('js')
    <script>
        const rolePermissions = @json(
            $roles->mapWithKeys(fn($role) => [
                $role->id => $role->permissions->pluck('id')->toArray(),
            ])
        );

        $(document).ready(function () {
            const permissionCheckboxes = $('input[name="permissions[]"]');

            $('#role_id').on('change', function () {
                const selectedRoleId = $(this).val();
                const selectedPermissions = rolePermissions[selectedRoleId] || [];

                permissionCheckboxes.each(function () {
                    const checkbox = $(this);
                    checkbox.prop('checked', selectedPermissions.includes(parseInt(checkbox.val())));
                });
            });

            // // Trigger change on page load if role is preselected
            // if ($('#role_id').val()) {
            //     $('#role_id').trigger('change');
            // }
        });
    </script>
    @endpush
</x-layout>
