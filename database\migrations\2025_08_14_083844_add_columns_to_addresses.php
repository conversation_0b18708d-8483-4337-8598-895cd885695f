<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            $table->foreignId('country_id')->nullable()->after('user_id')->constrained()->cascadeOnDelete();
            $table->string('type')->after('user_id');
            $table->string('street')->nullable()->after('type');
            $table->string('building')->nullable()->after('street');
            $table->string('floor')->nullable()->after('building');
        });
    }

    public function down(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            $table->dropColumn(['country_id', 'type', 'street', 'building', 'floor']);
        });
    }
};
