<?php

namespace App\Enum;

enum OrderStatus: string
{
    case READY_TO_PICKUP = 'ready_to_pickup'; // Default
    case HEADING_TO_PICKUP = 'heading_to_pickup'; //
    case ARRIVED_AT_PICKUP = 'arrived_at_pickup';
    case PICKED_UP = 'picked_up'; // depends on order items status
    case IN_TRANSIT = 'in_transit'; // if order is express ? the driver is heading to the dropoff location : the driver is heading to the local hub
    case ARRIVED_AT_DELIVERY_LOCATION = 'arrived_at_delivery_location'; // Only for express (non-international)
    case DELIVERED = 'delivered';
    case DELIVERED_TO_LOCAL_HUB = 'delivered_to_local_hub'; // Only for international or standard
    case PARTIALLY_DELIVERED = 'partially_delivered'; // Only for international & intercity (non-express) when some items are delivered and some are not
    case DELIVERY_FAILED = 'delivery_failed'; // when all items failed to be delivered
    case CANCELLED = 'cancelled'; // when user or driver cancel the order

    public static function availableStatuses(?int $shippingTypeId = null, ?bool $isExpress = false): array
    {
        return match (true) {
            $shippingTypeId == 1, // Immediate (always express)
            $shippingTypeId == 2 && $isExpress => [
                self::HEADING_TO_PICKUP,
                self::ARRIVED_AT_PICKUP,
                self::PICKED_UP,
                self::IN_TRANSIT,
                self::ARRIVED_AT_DELIVERY_LOCATION,
                self::DELIVERED,
                self::DELIVERY_FAILED,
                self::CANCELLED,
            ],
            ($shippingTypeId == 3) || ($shippingTypeId == 2 && !$isExpress) => [
                self::HEADING_TO_PICKUP,
                self::ARRIVED_AT_PICKUP,
                self::PICKED_UP,
                self::IN_TRANSIT,
                self::DELIVERED_TO_LOCAL_HUB,
                self::PARTIALLY_DELIVERED,
                self::CANCELLED,
                self::DELIVERY_FAILED,
                self::DELIVERED,
            ],
            default => [],
        };
    }
}
