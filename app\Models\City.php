<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use MatanYadaev\EloquentSpatial\Objects\Point;
use MatanYadaev\EloquentSpatial\Objects\Polygon;
use MatanYadaev\EloquentSpatial\Traits\HasSpatial;
use Spatie\Translatable\HasTranslations;

class City extends Model
{
    use HasSpatial, HasTranslations, SoftDeletes;

    protected $fillable = [
        'country_id',
        'name',
        'status',
        'location',
        'area',
    ];

    public array $translatable = ['name'];

    protected $casts = [
        'location' => Point::class,
        'area' => Polygon::class,
    ];

    public function scopeActive(Builder $query)
    {
        return $query->where('status', 'active');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function areas(): HasMany
    {
        return $this->hasMany(Area::class);
    }

    public function drivers(): BelongsToMany
    {
        return $this->belongsToMany(Driver::class, 'driver_areas');
    }

    public function companies(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'company_intercity_shipping_cities', 'city_id', 'company_id');
    }

    public function addresses(): HasMany
    {
        return $this->hasMany(Address::class);
    }
}
