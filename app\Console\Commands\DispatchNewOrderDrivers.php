<?php

namespace App\Console\Commands;

use App\Enum\OrderStatus;
use App\Events\NewOrder;
use App\Models\Order;
use App\Repositories\DriverRepository;
use Illuminate\Console\Command;
use MatanYadaev\EloquentSpatial\Objects\Point;

class DispatchNewOrderDrivers extends Command
{
    protected $signature = 'orders:dispatch-new-drivers';

    protected $description = 'Progressively search for drivers for orders every minute for up to 5 minutes';

    public function handle(): int
    {
        $orders = Order::with('pickupAddress')
            ->where('status', OrderStatus::READY_TO_PICKUP->value)
            ->where('driver_search_completed', false)
            ->where('driver_search_attempts', '<', Order::MAX_SEARCH_ATTEMPTS)
            ->where(function ($q): void {
                $q->whereNull('last_driver_search_at')
                    ->orWhere('last_driver_search_at', '<=', now()->subMinute());
            })
            // Process orders that are at least one minute old
            ->where('created_at', '<=', now()->subMinute())
            ->get();

        foreach ($orders as $order) {
            if (! $order->driver_search_started_at) {
                $order->driver_search_started_at = now();
            }

            $elapsed = now()->diffInMinutes($order->driver_search_started_at);
            if ($elapsed >= Order::MAX_SEARCH_ATTEMPTS) {
                $order->update([
                    'driver_search_completed' => true,
                    'last_driver_search_at' => now(),
                ]);

                continue;
            }

            // Resolve pickup point
            $location = $this->resolvePickupPoint($order);
            if (! $location instanceof \MatanYadaev\EloquentSpatial\Objects\Point) {
                $this->warn("Skipping order #{$order->id} — missing pickup location.");
                $order->update([
                    'driver_search_attempts' => $order->driver_search_attempts + 1,
                    'driver_search_started_at' => $order->driver_search_started_at ?? now(),
                    'last_driver_search_at' => now(),
                ]);

                continue;
            }

            $radius = 5000 + ($order->driver_search_attempts * Order::SEARCH_RADIUS_STEP);

            $drivers = app(DriverRepository::class)->getNearbyDrivers(
                $order,
                $location,
                $radius
            );

            if ($drivers->isNotEmpty()) {
                foreach ($drivers as $driver) {
                    $order->driverActions()->firstOrCreate(['driver_id' => $driver->id]);
                }

                broadcast(new NewOrder($order));

                $order->update([
                    'driver_search_completed' => true,
                    'last_driver_search_at' => now(),
                ]);

                continue;
            }

            $order->update([
                'driver_search_attempts' => $order->driver_search_attempts + 1,
                'driver_search_started_at' => $order->driver_search_started_at,
                'last_driver_search_at' => now(),
            ]);
        }

        return Command::SUCCESS;
    }

    private function resolvePickupPoint(Order $order): ?Point
    {
        if ($order->pickup_location instanceof Point) {
            return $order->pickup_location;
        }

        if ($order->pickupAddress && $order->pickupAddress->location instanceof Point) {
            return $order->pickupAddress->location;
        }

        return null;
    }
}
