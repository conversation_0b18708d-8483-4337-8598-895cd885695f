<?php

namespace App\Http\Controllers\Driver;

use App\Events\DriverLocationUpdate;
use App\Http\Controllers\Controller;
use App\Http\Requests\Driver\UpdateLocationRequest;

class UpdateLocationController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(UpdateLocationRequest $request)
    {
        $driver = auth('driver')->user();
        $driver->update([
            'current_lat' => $request->validated('lat'),
            'current_lng' => $request->validated('lng'),
        ]);

        event(new DriverLocationUpdate($driver->refresh()));

        return successMessage(__('Location Updated Successfully.'));
    }
}
