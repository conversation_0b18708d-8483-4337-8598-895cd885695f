<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\ConfirmUpdatePhoneOtpRequest;
use App\Http\Requests\User\ProfileRequest;
use App\Http\Requests\User\SendUpdatePhoneOtpRequest;
use App\Http\Requests\User\UpdateAppLocaleRequest;
use App\Http\Requests\User\UpdatePushNotificationStatus;
use App\Http\Resources\User\ProfileResource;
use App\Services\User\ProfileService;

class ProfileController extends Controller
{
    public function __construct(private readonly ProfileService $profileService) {}

    public function get()
    {
        $user = auth('user')->user();

        return success(new ProfileResource($user));
    }

    public function update(ProfileRequest $request)
    {
        $this->profileService->update($request->validated());

        return success(true);
    }

    public function sendUpdatePhoneOtp(SendUpdatePhoneOtpRequest $request)
    {
        $this->profileService->sendUpdatePhoneOtp($request->validated());

        return success(true);
    }

    public function confirmUpdatePhoneOtp(ConfirmUpdatePhoneOtpRequest $request)
    {
        $this->profileService->confirmUpdatePhone($request->validated());

        return success(true);
    }

    public function delete()
    {
        $this->profileService->delete();

        return success(true);
    }

    public function updateAppLocale(UpdateAppLocaleRequest $request)
    {
        $this->profileService->updateAppLocale();

        return success(true);
    }

    public function updatePushNotificationStatus()
    {
        $newStatus = $this->profileService->updatePushNotificationStatus();

        return success([
            'status' => $newStatus
        ]);
    }
}
