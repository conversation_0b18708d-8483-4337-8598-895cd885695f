<?php

namespace App\Rules;

use App\Repositories\AreaRepository;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ActiveAreas implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $areaRepository = app(AreaRepository::class);

        $areas = $areaRepository->getByIds($value);

        foreach ($areas as $area) {
            if ($area->status == 'inactive') {
                $fail('The :attribute contains inactive areas.');
            }
        }
    }
}
