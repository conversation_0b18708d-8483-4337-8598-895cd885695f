<?php

namespace Database\Seeders;

use App\Models\Address;
use Illuminate\Database\Seeder;
use MatanYadaev\EloquentSpatial\Objects\Point;

class AddressSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Address::create([
            'name' => 'Home',
            'user_id' => 1,
            'city_id' => 1,
            'area_id' => 1,
            'location' => new Point(24.45, 54.32),
            'country_code' => '+971',
            'phone' => '0501234567',
        ]);
    }
}
