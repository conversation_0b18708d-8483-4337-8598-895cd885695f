<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;
use App\Models\User;
use App\Rules\UniquePhone;
use App\Rules\ValidBirthDate;
use App\Rules\ValidMedia;
use App\Rules\ValidPassword;
use App\Rules\ValidPhone;
use Illuminate\Validation\Rule;

class UserRequest extends BaseFormRequest
{
    public function rules(): array
    {
        $user = $this->route('user');

        return [
            'name' => ['required', 'string', 'min:4', 'max:30'],
            'country_code' => ['required', 'string', 'exists:countries,code'],
            'phone' => ['required', 'string', new ValidPhone($this->country_code), new UniquePhone($this->country_code, User::class, $user?->id)],
            'email' => ['nullable', 'string', 'email:rfc,dns', 'max:255', Rule::unique('users', 'email')->ignore($user?->id)],
            'password' => [Rule::requiredIf(! (bool) $user), 'nullable', 'string',  new ValidPassword, 'confirmed'],
            'gender' => ['nullable', 'in:male,female'],
            'birth_date' => ['nullable', new ValidBirthDate],
            'image' => ['nullable', new ValidMedia(['image'])],
        ];
    }
}
