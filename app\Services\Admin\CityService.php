<?php

namespace App\Services\Admin;

use App\DTO\Admin\CityDTO;
use App\Models\City;
use App\Repositories\CityRepository;
use Illuminate\Http\RedirectResponse;

class CityService
{
    public function __construct(private readonly CityRepository $cityRepository)
    {
        //
    }

    public function create(CityDTO $dto): City
    {
        return $this->cityRepository->create($dto);
    }

    public function update(City $city, CityDTO $dto): bool
    {
        return $this->cityRepository->update($city, $dto);
    }

    public function delete(City $city): RedirectResponse
    {
        $relations = [
            'areas' => __('City has related areas and cannot be deleted.'),
            'drivers' => __('City has related drivers and cannot be deleted.'),
            'companies' => __('City has related companies and cannot be deleted.'),
            'addresses' => __('City has related addresses and cannot be deleted.'),
        ];

        foreach ($relations as $relation => $message) {
            if ($city->{$relation}()->exists()) {
                return redirect()->back()->with('fail', $message);
            }
        }

        $city->delete();

        return redirect()->back()->with('success', __('City deleted successfully.'));
    }
}
