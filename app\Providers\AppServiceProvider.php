<?php

namespace App\Providers;

use App\Contracts\OrderItemStatusHandlerInterface;
use App\Contracts\OrderStatusHandlerInterface;
use App\Services\Driver\OrderItemStatusService;
use App\Services\Driver\OrderStatusService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        if ($this->app->environment('local') && class_exists(\Laravel\Telescope\TelescopeServiceProvider::class)) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }
        $this->app->bind(
            OrderItemStatusHandlerInterface::class,
            OrderItemStatusService::class
        );

        $this->app->bind(
            OrderStatusHandlerInterface::class,
            OrderStatusService::class
        );
    }

    public function boot(): void
    {
        //
    }
}
