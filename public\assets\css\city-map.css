/**
 * City Map Styles
 */

#map-container {
    position: relative;
    margin-bottom: 1.5rem;
}

#city-map {
    width: 100%;
    height: 500px;
    border-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#area-map {
    width: 100%;
    height: 500px;
    border-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    padding: 0.5rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#map-search-input {
    width: 300px;
    height: 40px;
    padding: 0.5rem 1rem;
    margin: 10px;
    border-radius: 0.5rem;
    border: 1px solid #ccc;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 1rem;
}

#map-search-input:focus {
    outline: none;
    border-color: #4099FF;
    box-shadow: 0 0 0 0.2rem rgba(64, 153, 255, 0.25);
}

.map-button {
    margin-left: 0.5rem;
}

.height-500 {
    height: 500px;
}
