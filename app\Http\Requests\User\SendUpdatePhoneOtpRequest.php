<?php

namespace App\Http\Requests\User;

use App\Models\User;
use App\Rules\UniquePhone;
use App\Rules\ValidPhone;
use Illuminate\Foundation\Http\FormRequest;

class SendUpdatePhoneOtpRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'country_code' => ['required', 'string', 'exists:countries,code'],
            'phone' => ['required', 'string', new ValidPhone($this->country_code), new UniquePhone($this->country_code, User::class)],
        ];
    }
}
