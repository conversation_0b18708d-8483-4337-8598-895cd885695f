@php
    $locales = config('app.locales', ['en', 'ar']);
@endphp

<x-layout :title="__('Country Details')">
    <x-session-message />

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title">{{ __('Country Details') }}</h4>
            <div>
                <a href="{{ route('admin.countries.edit', $country->id) }}" class="btn btn-primary">
                    {{ __('Edit') }}
                </a>
                <a href="{{ route('admin.countries.index') }}" class="btn btn-outline-secondary">
                    {{ __('Back') }}
                </a>
            </div>
        </div>

        <div class="card-body">
            <div class="row">
                @foreach ($locales as $locale)
                    <div class="col-md-4 mb-3">
                        <strong>{{ __('Name') }} ({{ strtoupper($locale) }})</strong>
                        <p>{{ $country->getTranslation('name', $locale) ?? '-' }}</p>
                    </div>
                @endforeach

                <div class="col-md-4 mb-3">
                    <strong>{{ __('Country Code') }}</strong>
                    <p>{{ $country->code }}</p>
                </div>

                <div class="col-md-4 mb-3">
                    <strong>{{ __('Abbreviation') }}</strong>
                    <p>{{ $country->abbv }}</p>
                </div>

                <div class="col-md-4 mb-3">
                    <strong>{{ __('Created At') }}</strong>
                    <p>{{ $country->created_at?->format('Y-m-d H:i') ?? '-' }}</p>
                </div>
            </div>
        </div>
    </div>
</x-layout>
