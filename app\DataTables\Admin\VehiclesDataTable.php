<?php

namespace App\DataTables\Admin;

use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Yajra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class VehiclesDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param  QueryBuilder<Vehicle>  $query  Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->setRowId('id')
            ->addColumn('name', fn ($vehicle) => $vehicle->name)
            ->addColumn('approval_status', fn ($vehicle): ?string => statusBadge($vehicle->approval_status))
            ->addColumn('status', fn ($vehicle): ?string => statusBadge($vehicle->status))
            ->addColumn('actions', 'pages.admin.vehicles.actions')
            ->addColumn('image', fn ($vehicle) => view('pages.admin.vehicles.image', ['vehicle' => $vehicle])->render())
            ->orderColumn('DT_RowIndex', function ($query, $direction): void {
                $query->orderBy('id', $direction);
            })
            ->rawColumns(['actions', 'status', 'image', 'approval_status'])
            ->addIndexColumn();
    }

    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<Vehicle>
     */
    public function query(Vehicle $model): QueryBuilder
    {
        $locales = config('app.locales', ['en', 'ar']);
        $search = request('search_param');

        return $model->newQuery()
            ->with(['company', 'transportionMethod'])
            ->when($search, function ($query) use ($locales, $search): void {
                $query->where(function ($query) use ($locales, $search): void {
                    foreach ($locales as $locale) {
                        $query->orWhereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(name, \"$.{$locale}\"))) like LOWER(?)", ["%$search%"]);
                    }
                });
            })
            ->when(request('status'), fn ($q) => $q->where('status', request('status')));
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('vehicles-table')
            ->columns($this->getColumns())
            ->orderBy(0, 'desc')
            ->minifiedAjax()
            ->searching(false)
            ->parameters([
                'stateSave' => true,
                'stateDuration' => 10,
                'language' => [
                    'url' => asset('assets/json/datatable-translations/'.app()->getLocale().'.json'),
                ],
            ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            Column::make('DT_RowIndex')->title('#')->addClass('text-center')->searchable(false),
            Column::computed('name')->title(__('Name'))->addClass('text-center'),
            Column::computed('image')->title(__('Image'))->addClass('text-center'),
            Column::computed('approval_status')->title(__('Approval Status'))->addClass('text-center'),
            Column::computed('status')->title(__('Status'))->addClass('text-center'),
            Column::computed('actions')->title(__('Actions'))->addClass('text-center'),
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'Vehicles_'.date('YmdHis');
    }
}
