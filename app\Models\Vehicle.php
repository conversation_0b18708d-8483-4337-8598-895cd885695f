<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class Vehicle extends Model
{
    use HasTranslations, SoftDeletes;

    protected $fillable = [
        'company_id',
        'transportion_method_id',
        'name',
        'plate_number',
        'license_number',
        'license_expiration_date',
        'approval_status',
        'status',
        'model',
        'year',
        'rejection_reason',
    ];

    public array $translatable = ['name'];

    public function getImageUrlAttribute()
    {
        return $this->photo ? $this->photo->url : null;
    }

    public function photo()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'photo');
    }

    public function registrationLicense(): MorphOne
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'registration_license');
    }

    public function drivingLicense()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'driving_license');
    }

    public function insurancePolicy()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'insurance_policy');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function transportionMethod(): BelongsTo
    {
        return $this->belongsTo(TransportionMethod::class);
    }

    public function drivers(): BelongsToMany
    {
        return $this->belongsToMany(Driver::class, 'driver_vehicles');
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }
}
