<?php

namespace App\Http\Requests\User;

use App\Enum\ChatMessageTypeEnum;
use App\Rules\ValidMedia;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class ChatMessageRequest extends FormRequest
{
    public function rules(): array
    {
        $rules = [
            'chatable_type' => ['required', 'string', 'in:order,shipment'],
            'chatable_id' => ['required', 'string'],
            'type' => ['required', new Enum(ChatMessageTypeEnum::class)],
        ];

        if (request()->type == ChatMessageTypeEnum::TEXT->value) {
            $rules['content'] = ['required', 'string', 'max:500'];
        }

        if (request()->type == ChatMessageTypeEnum::MEDIA->value) {
            $rules['content'] = ['required', 'file', new ValidMedia(['image', 'pdf'])];
        }

        return $rules;
    }
}
