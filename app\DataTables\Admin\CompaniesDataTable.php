<?php

namespace App\DataTables\Admin;

use App\Models\Company;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Yajra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class CompaniesDataTable extends DataTable
{
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->setRowId('id')
            ->addColumn('status', fn ($company): ?string => statusBadge($company->status))
            ->addColumn('actions', 'pages.admin.companies.actions')
            ->addColumn('phone', fn ($company): string => formatPhone($company->country_code, $company->phone))
            ->addColumn('email', fn ($company) => $company->email ?? '-')
            ->addColumn('admin_name', fn ($company) => $company->superAdmin?->name)
            ->orderColumn('DT_RowIndex', fn ($query, $direction) => $query->orderBy('id', $direction))
            ->rawColumns(['actions', 'phone', 'status'])
            ->addIndexColumn();
    }

    public function query(Company $model): QueryBuilder
    {
        return $model->newQuery()
            ->where('approval_status', 'approved')
            ->with('superAdmin')
            ->when(request('search_param'), function ($query): void {
                $query->where('name', 'LIKE', '%'.request('search_param').'%')
                    ->orWhereRaw('CONCAT(country_code, phone) LIKE ?', ['%'.request('search_param').'%'])
                    ->orWhere('email', 'LIKE', '%'.request('search_param').'%');
            })
            ->when(request('status'), fn ($q) => $q->where('status', request('status')));
    }

    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('companies-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->searching(false)
            ->parameters([
                'stateSave' => true,
                'stateDuration' => 10,
                'language' => [
                    'url' => asset('assets/json/datatable-translations/'.app()->getLocale().'.json'),
                ],
            ]);
    }

    public function getColumns(): array
    {
        return [
            Column::make('DT_RowIndex')->title('#')->addClass('text-center')->searchable(false),
            Column::make('name')->title(__('Name'))->addClass('text-center')->orderable(false),
            Column::computed('phone')->title(__('Phone'))->addClass('text-center'),
            Column::computed('email')->title(__('Email'))->addClass('text-center'),
            Column::computed('admin_name')->title(__('Admin Name'))->addClass('text-center'),
            Column::computed('status')->title(__('Status'))->addClass('text-center'),
            Column::computed('actions')->title(__('Actions'))->addClass('text-center'),
        ];
    }

    protected function filename(): string
    {
        return 'Companies_'.date('YmdHis');
    }
}
