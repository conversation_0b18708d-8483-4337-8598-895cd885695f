<?php

namespace App\Repositories;

use App\DTO\Admin\CountryDTO;
use App\Models\Country;

class CountryRepository
{
    public function __construct(private readonly Country $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByCode(string $code)
    {
        return $this->model->where('code', $code)->first();
    }

    public function getAll()
    {
        return $this->model->get();
    }

    public function getInternatonalShippingCountries()
    {
        return $this->model->where('id', '!=', 1)->get();
    }

    public function create(CountryDTO $dto)
    {
        return $this->model->create($dto->toArray());
    }

    public function update(Country $country, CountryDTO $dto)
    {
        return $country->update($dto->toArray());
    }

    public function getForSelect()
    {
        return $this->model->get();
    }
}
