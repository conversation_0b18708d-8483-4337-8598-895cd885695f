<x-layout :title="__('Drivers')">
    <x-session-message />
    <div class="card">
        <div class="card-heading">
            <p>{{ __('Drivers') }}</p>
            <div>
                @if(auth('admin')->user()->hasPermission('create driver'))
                <a class="btn btn-sm btn-outline-primary" href="{{ route('admin.drivers.create') }}">{{ __('Create') }}</a>
                @endif
            </div>
        </div>

        <div class="row" id="filters" data-datatable-id="drivers-table">
            <!-- Search -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Search') }}</b></label>
                <input type="text" name="search_param" class="form-control" onchange="dt_filter()">
            </div>

            <!-- Status -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Status') }}</b></label>
                <select name="status" class="select2 form-select form-select-lg" data-placeholder="{{ __('Choose') }}..." data-allow-clear="true" onchange="dt_filter()">
                    <option></option>
                    <option value="active">{{ __('Active') }}</option>
                    <option value="inactive">{{ __('Inactive') }}</option>
                </select>
            </div>

            <!-- Company -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Company') }}</b></label>
                <select name="company_id" class="select2 form-select form-select-lg" data-placeholder="{{ __('Choose') }}..." data-allow-clear="true" onchange="dt_filter()">
                    <option></option>
                      @foreach ($companies as $company)
                    <option value="{{ $company->id }}">{{ $company->name }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Transportion Methods -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Transportion Method') }}</b></label>
                <select name="transportion_method_id" class="select2 form-select form-select-lg" data-placeholder="{{ __('Choose') }}..." data-allow-clear="true" onchange="dt_filter()">
                    <option></option>
                    @foreach ($transportionMethods as $transportionMethod)
                    <option value="{{ $transportionMethod->id }}">{{ $transportionMethod->name }}</option>
                    @endforeach
                </select>
            </div>

            <!-- From Date -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('From Date') }}</b></label>
                <input type="text" id="from_date" name="from_date" class="form-control" onchange="dt_filter()">
            </div>

            <!-- To Date -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('To Date') }}</b></label>
                <input type="text" id="to_date" name="to_date" class="form-control" onchange="dt_filter()">
            </div>
        </div>

        <div class="card-body">
            <div class="card-datatable table-responsive pt-0">
                {{ $dataTable->table(['class' => 'datatables-basic table table-striped']) }}
            </div>
            <x-delete-modal />
        </div>
    </div>

    @push('css')
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}" />
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}" />
    @endpush

    @push('js')
        <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
        {{ $dataTable->scripts(attributes: ['type' => 'module']) }}

        <script>
            $("#from_date").flatpickr();
            $("#to_date").flatpickr();
        </script>
    @endpush
</x-layout>
