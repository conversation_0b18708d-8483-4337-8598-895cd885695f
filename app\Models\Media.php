<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Media extends Model
{
    const UPDATED_AT = null;

    protected $fillable = [
        'mediable_type',
        'mediable_id',
        'filename',
        'path',
        'extension',
        'mime',
        'size',
        'type',
        'created_at',
    ];

    public function getUrlAttribute()
    {
        return url('storage/'.$this->path);
    }
}
