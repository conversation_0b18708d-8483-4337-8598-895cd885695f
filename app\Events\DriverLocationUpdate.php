<?php

namespace App\Events;

use App\Models\Driver;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class DriverLocationUpdate implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(private Driver $driver) {}

    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('driver.location.updated.'.$this->driver->id),
        ];
    }

    public function broadcastAs(): string
    {
        return 'DriverLocationUpdate';
    }

    public function broadcastWith(): array
    {
        return [
            'lat' => $this->driver->current_lat,
            'lng' => $this->driver->current_lng,
        ];
    }
}
