@php
    $admin = auth('admin')->user();
@endphp

<div class="actions">
    @if ($admin->hasPermission('show city') || $admin->hasPermission('update city') || $admin->hasPermission('delete city'))
        @if ($admin->hasPermission('show city'))
            <a href="{{ route('admin.cities.show', $id) }}">
                <i class="ti ti-eye"></i>
            </a>
        @endif

        @if ($admin->hasPermission('update city'))
            <a href="{{ route('admin.cities.edit', $id) }}">
                <i class="ti ti-edit"></i>
            </a>
        @endif

        @if ($admin->hasPermission('delete city'))
            <a href="javascript:void(0)">
                <i data-bs-toggle="modal" data-bs-target="#delete-modal" onclick="changeDeleteModalData(this)"
                    delete-route="{{ route('admin.cities.destroy', $id) }}"
                    delete-name="{{ __('City') }} : {{ Arr::get($name, app()->getLocale()) }}" class="ti ti-archive">
                </i>
            </a>
        @endif
    @else
        -
    @endif
</div>
