<?php

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\App;

function successMessage(string $message)
{
    return response()->json([
        'message' => $message,
    ], 200);
}

function errorMessage(string $message, $status = 400)
{
    return response()->json([
        'message' => $message,
    ], $status);
}

function success($data = [], $message = null)
{
    $data = [
        'data' => $data,
    ];

    if ($message) {
        $data['message'] = $message;
    }

    return response()->json($data, 200);
}

function formatDateTime(string $timestamp): ?string
{
    if ($timestamp === '' || $timestamp === '0') {
        return null;
    }

    return Carbon::parse($timestamp)->format('Y-m-d H:i');
}

function formatDate(string $timestamp): ?string
{
    if ($timestamp === '' || $timestamp === '0') {
        return null;
    }

    return Carbon::parse($timestamp)->format('Y-m-d');
}

function normalizeMobileNumber($number): string
{
    return ltrim((string) $number, '0');
}

function getInvalidatedValue(?string $value): ?string
{
    if (is_null($value)) {
        return null;
    }

    return $value . '_del_' . Str::random();
}

function restoreInvalidatedValue($value)
{
    $delimiter = '_del_';
    $pos = strrpos((string) $value, $delimiter);

    return $pos !== false ? substr((string) $value, 0, $pos) : $value;
}

function formatSecondsToMinutesTime($seconds): string
{
    $minutes = floor($seconds / 60);
    $remainingSeconds = $seconds % 60;

    return sprintf('%02d:%02d', $minutes, $remainingSeconds);
}

function formatSecondsToHoursTime($seconds): string
{
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $remainingSeconds = $seconds % 60;

    return sprintf('%02d:%02d:%02d', $hours, $minutes, $remainingSeconds);
}

function generatePassCode()
{
    return App::environment('local') ? '123456' : fake()->randomNumber(6, true);
}

function estimateTimeFromDistance($lat1, $lon1, $lat2, $lon2, $speedKmPerHour = 40): string
{
    $earthRadius = 6371;

    $latDelta = deg2rad($lat2 - $lat1);
    $lonDelta = deg2rad($lon2 - $lon1);

    $a = sin($latDelta / 2) ** 2 +
        cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
        sin($lonDelta / 2) ** 2;

    $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
    $distance = $earthRadius * $c;

    $hours = $distance / $speedKmPerHour;
    $minutes = $hours * 60;
    $seconds = $minutes * 60;
    if ($minutes >= 60) {
        return round($hours, 1) . ' hours';
    }

    if ($minutes >= 1) {
        return round($minutes) . ' minutes';
    }

    return round($seconds) . ' seconds';
}

function getDistance($lat1, $lon1, $lat2, $lon2): float
{
    $earthRadius = 6371;

    $latDelta = deg2rad($lat2 - $lat1);
    $lonDelta = deg2rad($lon2 - $lon1);

    $a = sin($latDelta / 2) * sin($latDelta / 2) +
        cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
        sin($lonDelta / 2) * sin($lonDelta / 2);

    $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

    return $earthRadius * $c;
}

function statusBadge($status): ?string
{
    if ($status == 'active' || $status == 'approved') {
        return "<span class='badge bg-success'>" . __($status) . '</span>';
    }

    if ($status == 'pending') {
        return "<span class='badge bg-warning'>" . __($status) . '</span>';
    }

    if ($status == 'on_review') {
        return "<span class='badge bg-info'>" . __($status) . '</span>';
    }

    if ($status == 'inactive' || $status == 'rejected') {
        return "<span class='badge bg-danger'>" . __($status) . '</span>';
    }

    return null;
}

function normalizePhoneNumber($number): string
{
    return ltrim((string) $number, '0');
}

function formatPhone($country_code, $phone): string
{
    return "<span dir='ltr'>$country_code $phone</span>";
}

function formatMoney($value): string
{
    return number_format($value, 2);
}
