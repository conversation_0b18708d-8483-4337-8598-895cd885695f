<?php

namespace App\Repositories;

use App\Models\Company;
use App\Models\CompanyShippingType;

class CompanyShippingTypeSizeTransportionMethodRepository
{
    public function __construct(private readonly CompanyShippingType $model) {}

    public function create(Company $company, string $shipping_type_id)
    {
        return $this->model->create([
            'company_id' => $company->id,
            'shipping_type_id' => $shipping_type_id,
        ]);
    }

    public function get($company_id, $shipping_type_id)
    {
        return $this->model->where([
            'company_id' => $company_id,
            'shipping_type_id' => $shipping_type_id,
        ])->first();
    }

    public function getAll() {}

    public function shippingTypeHasTransportionMethod(CompanyShippingType $shipping_type, string $transportion_method_id)
    {
        return $shipping_type->transportionMethods()
            ->where('transportion_method_id', $transportion_method_id)
            ->exists();
    }
}
