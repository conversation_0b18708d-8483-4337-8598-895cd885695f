<?php

namespace App\Providers;

use Illuminate\Auth\Middleware\Authenticate;
use Illuminate\Auth\Middleware\RedirectIfAuthenticated;
use Illuminate\Support\ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        //
    }

    public function boot(): void
    {
        Authenticate::redirectUsing(function () {
            if (request()->routeIs('admin.*')) {
                return route('admin.login');
            }

            return route('company.login');
        });

        RedirectIfAuthenticated::redirectUsing(function () {
            if (request()->routeIs('admin.*')) {
                return route('admin.index');
            }

            return route('company.index');
        });
    }
}
