input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}

.card-header {
    font-size: 22px !important;
}

.card-heading {
    display: flex;
    justify-content: space-between;
    font-size: 22px !important;
    font-weight: 500 !important;
    padding: 25px
}

.ti {
    font-size: 1.60rem;
}

td .actions {
    display: flex;
    justify-content: center;
    gap: 20px;
}

table i.ti-eye {
    color: #3ea0e3;
}

table i.ti-edit {
    color: #222222;
}

table i.ti-archive {
    color: #dc3545;
}

.card-image {
    width: 200px;
    height: 150px;
    border: 1px solid #ccc;
    border-radius: 3px
}

.modal-dialog {
    top: 30%;
}

textarea {
    overflow: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

textarea::-webkit-scrollbar {
    display: none;
}

#filters {
    padding: 0 30px;
}

.swal2-container {
    z-index: 99999 !important;
}

.swal2-timer-progress-bar {
    background-color: #fff !important;
}
