<?php

namespace App\Services\Driver;

use App\Enum\OrderItemStatus;
use App\Enum\OrderStatus;
use App\Models\Order;

class OrderStatusService
{
    public function update(Order $order): void
    {
        $newStatus = $this->determineOrderStatus(
            $order->items()->pluck('status')->toArray(),
            $order->shipping_type_id,
            $order->is_express,
            $order->status
        );

        if ($order->status !== $newStatus) {
            $order->update(['status' => $newStatus]);
        }

        $this->handleDriverAvailability($order, $newStatus);
    }

    protected function handleDriverAvailability(Order $order, OrderStatus $newStatus): void
    {
        $finalStates = [
            OrderStatus::DELIVERED,
            OrderStatus::DELIVERED_TO_LOCAL_HUB,
            OrderStatus::CANCELLED,
            OrderStatus::DELIVERY_FAILED,
        ];

        if (in_array($newStatus, $finalStates)) {
            $this->updateDriverAvailability($order);
        }
    }

    protected function determineOrderStatus(array $statuses, int $shippingTypeId, bool $isExpress, OrderStatus $currentStatus): OrderStatus
    {
        // Handle empty cases
        if ($statuses === []) {
            return OrderStatus::READY_TO_PICKUP;
        }

        // Handle final states first - these should always be applied if conditions match
        if ($this->all($statuses, OrderItemStatus::PICKED_UP_FAILED)) {
            return OrderStatus::CANCELLED;
        }

        if ($this->all($statuses, OrderItemStatus::DELIVERY_FAILED)) {
            return OrderStatus::DELIVERY_FAILED;
        }

        if ($this->all($statuses, OrderItemStatus::DELIVERED)) {
            return OrderStatus::DELIVERED;
        }

        // Handle partial delivery
        if ($this->someDeliveredSomeNot($statuses)) {
            return OrderStatus::PARTIALLY_DELIVERED;
        }

        // Handle hub-based shipping
        if (($shippingTypeId === 2 && ! $isExpress || $shippingTypeId === 3) && $this->all($statuses, OrderItemStatus::DELIVERED_TO_FIRST_HUB)) {
            return OrderStatus::DELIVERED_TO_LOCAL_HUB;
        }

        // Handle pickup states
        if (in_array(OrderItemStatus::PICKED_UP->value, $statuses)) {
            return $currentStatus === OrderStatus::IN_TRANSIT ? OrderStatus::IN_TRANSIT : OrderStatus::PICKED_UP;
        }

        // If no conditions match, keep the current status
        return $currentStatus;
    }

    protected function all(array $statuses, OrderItemStatus $target): bool
    {
        return count(array_filter($statuses, fn ($s): bool => $s !== $target->value)) === 0;
    }

    protected function someDeliveredSomeNot(array $statuses): bool
    {
        return in_array(OrderItemStatus::DELIVERED->value, $statuses)
            && ! $this->all($statuses, OrderItemStatus::DELIVERED);
    }

    protected function updateDriverAvailability(Order $order): void
    {
        $order->driver()->update(['is_available' => true]);
    }
}
