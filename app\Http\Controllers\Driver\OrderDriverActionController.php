<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Services\Driver\OrderDriverActionService;

class OrderDriverActionController extends Controller
{
    public function __construct(private readonly OrderDriverActionService $orderDriverActionService)
    {
        //
    }

    public function accept(Order $order)
    {
        return success($this->orderDriverActionService->accept($order, auth('driver')->user()));
    }

    public function reject(Order $order)
    {
        return success($this->orderDriverActionService->reject($order, auth('driver')->user()));
    }

    public function resend(Order $order)
    {
        return success($this->orderDriverActionService->resend($order, auth('driver')->user()));
    }
}
