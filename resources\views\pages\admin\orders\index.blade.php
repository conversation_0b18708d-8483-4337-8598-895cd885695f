<x-layout :title="__('Orders')">
    <x-session-message />
    <div class="card">
        <div class="card-heading">
            <p>{{ __('Orders') }}</p>
        </div>

        <div class="row" id="filters" data-datatable-id="orders-table">
            <!-- Search -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Search') }}</b></label>
                <input type="text" name="search_param" class="form-control" onchange="dt_filter()">
            </div>

            <!-- Shipping Type -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Shipping Type') }}</b></label>
                <select name="shipping_type_id" class="select2 form-select form-select-lg"
                    data-placeholder="{{ __('Choose') }}..." data-allow-clear="true" onchange="dt_filter()">
                    <option></option>
                    @foreach ($shippingTypes as $type)
                        <option value="{{ $type->id }}">{{ $type->name }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Shipping Size -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Shipping Size') }}</b></label>
                <select name="shipping_size_id" class="select2 form-select form-select-lg"
                    data-placeholder="{{ __('Choose') }}..." data-allow-clear="true" onchange="dt_filter()">
                    <option></option>
                    @foreach ($shippingSizes as $size)
                        <option value="{{ $size->id }}">{{ $size->name }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Shipping Method -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Shipping Method') }}</b></label>
                <select name="is_express" class="select2 form-select form-select-lg"
                    data-placeholder="{{ __('Choose') }}..." data-allow-clear="true" onchange="dt_filter()">
                    <option></option>
                    <option value="1">{{ __('Express') }}</option>
                    <option value="0">{{ __('Standard') }}</option>
                </select>
            </div>

            <!-- Status -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Status') }}</b></label>
                <select name="status" class="select2 form-select form-select-lg"
                    data-placeholder="{{ __('Choose') }}..." data-allow-clear="true" onchange="dt_filter()">
                    <option></option>
                    @foreach (\App\Enum\OrderStatus::cases() as $status)
                        <option value="{{ $status->value }}">{{ __(ucfirst(str_replace('_', ' ', $status->value))) }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- From Date -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('From Date') }}</b></label>
                <input type="date" id="from_date" name="from_date" class="form-control" onchange="dt_filter()">
            </div>

            <!-- To Date -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('To Date') }}</b></label>
                <input type="date" id="to_date" name="to_date" class="form-control" onchange="dt_filter()">
            </div>
        </div>

        <div class="card-body">
            <div class="card-datatable table-responsive pt-0">
                {{ $dataTable->table(['class' => 'datatables-basic table table-striped']) }}
            </div>
        </div>
    </div>

    @push('css')
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}" />
        <link rel="stylesheet"
            href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}" />
    @endpush

    @push('js')
        <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
        {{ $dataTable->scripts(attributes: ['type' => 'module']) }}

        <script>
            $("#from_date").flatpickr();
            $("#to_date").flatpickr();
        </script>
    @endpush
</x-layout>
