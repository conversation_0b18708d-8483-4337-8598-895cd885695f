<x-layout :title="__('Assign Vehicles to Driver')">
    <x-session-message />
    <form action="{{ route('company.drivers.assign-vehicles', $driver) }}" method="POST" enctype="multipart/form-data">
        @csrf
        <div class="card">
            <div class="card-body">

                <!-- Currently Assigned Vehicles -->
                <h5 class="mb-3">{{ __('Currently Assigned Vehicles') }}</h5>
                <div class="row g-2 mb-4">
                    @forelse($approvedVehicles as $vehicle)
                        <div class="col-lg-3 col-md-4 col-sm-6">
                            <div class="card h-100 shadow-sm border rounded" style="font-size: 0.85rem;">
                                <div class="position-relative">
                                    <img src="{{ $vehicle->image_url }}" class="card-img-top" style="height: 120px; object-fit: cover;">
                                    <div class="position-absolute top-0 end-0 p-1">
                                        <input type="checkbox" 
                                            name="vehicles[]" 
                                            value="{{ $vehicle->id }}" 
                                            class="form-check-input"
                                            checked>
                                    </div>
                                </div>
                                <div class="card-body p-2 text-center">
                                    <h6 class="card-title mb-1 text-truncate" title="{{ $vehicle->name }}">
                                        {{ $vehicle->name }}
                                    </h6>
                                    <small class="text-muted">{{ $vehicle->model }}</small>
                                </div>
                            </div>
                        </div>
                    @empty
                        <p class="text-muted">- {{ __('No vehicles assigned.') }}</p>
                    @endforelse
                </div>

                <!-- Pending Assigned Vehicles -->
                <h5 class="mb-3">{{ __('Pending Assigned Vehicles') }}</h5>
                <div class="row g-2 mb-4">
                    @forelse($pendingVehicles as $vehicle)
                        <div class="col-lg-3 col-md-4 col-sm-6">
                            <div class="card h-100 shadow-sm border rounded">
                                <img src="{{ $vehicle->image_url }}" class="card-img-top" style="height: 120px; object-fit: cover;">
                                <div class="card-body p-2 text-center">
                                    <h6 class="card-title mb-1 text-truncate" title="{{ $vehicle->name }}">
                                        {{ $vehicle->name }}
                                    </h6>
                                    <small class="text-muted">{{ $vehicle->model }}</small>
                                </div>
                            </div>
                        </div>
                    @empty
                        <p class="text-muted">- {{ __('No pending vehicles.') }}</p>
                    @endforelse
                </div>

                <!-- Available Too Assign Vehicles -->
                <h5 class="mb-3">{{ __('Available Vehicles for Assignment') }}</h5>
                <div class="row g-2">
                    @forelse($availableVehicles as $vehicle)
                        <div class="col-lg-3 col-md-4 col-sm-6">
                            <div class="card h-100 shadow-sm border rounded" style="font-size: 0.85rem;">
                                <div class="position-relative">
                                    <img src="{{ $vehicle->image_url }}" class="card-img-top" style="height: 120px; object-fit: cover;">
                                    <div class="position-absolute top-0 end-0 p-1">
                                        <input type="checkbox" 
                                               name="vehicles[]" 
                                               value="{{ $vehicle->id }}" 
                                               class="form-check-input"
                                               @checked(in_array($vehicle->id, old('vehicles', $assignedVehicleIds ?? [])))>
                                    </div>
                                </div>
                                <div class="card-body p-2 text-center">
                                    <h6 class="card-title mb-1 text-truncate" title="{{ $vehicle->name }}">
                                        {{ $vehicle->name }}
                                    </h6>
                                    <small class="text-muted">{{ $vehicle->model }}</small>
                                </div>
                            </div>
                        </div>
                    @empty
                        <p class="text-muted">{{ __('No vehicles available for assignment.') }}</p>
                    @endforelse
                </div>
                
                @if($approvedVehicles->count() || $availableVehicles->count())
                <div class="mt-3">
                    <button type="submit" class="btn btn-outline-primary btn-sm">
                        {{ __('Update') }}
                    </button>
                </div>
                @endif

            </div>
        </div>
    </form>
</x-layout>
