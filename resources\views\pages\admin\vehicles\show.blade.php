@php
    $locales = config('app.locales', ['en', 'ar']);
@endphp

<x-layout :title="__('Vehicles')">
    <div class="card shadow-sm rounded-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ __('Vehicle') }}: {{ $vehicle->name }}</h5>
            @if (auth('admin')->user()->hasPermission('update vehicle') && $vehicle->approval_status !== 'rejected')
                <a class="btn btn-sm btn-outline-primary" href="{{ route('admin.vehicles.edit', $vehicle->id) }}">
                    <i class="fas fa-edit me-1"></i> {{ __('Edit') }}
                </a>
            @endif
        </div>
        <div class="card-body">
            <!-- Basic Information -->
            <h6 class="text-muted mb-3">{{ __('Basic Information') }}</h6>
            <div class="row mb-4">
                <div class="col-md-6">
                    @foreach ($locales as $locale)
                        <div class="row mb-3">
                            <div class="col-sm-4 text-muted">{{ __('Name') }} ({{ strtoupper($locale) }})</div>
                            <div class="col-sm-8">{{ $vehicle->getTranslation('name', $locale) ?? '-' }}</div>
                        </div>
                    @endforeach
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">{{ __('Model') }}</div>
                        <div class="col-sm-8">{{ $vehicle->model }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">{{ __('Year') }}</div>
                        <div class="col-sm-8">{{ $vehicle->year }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">{{ __('Plate Number') }}</div>
                        <div class="col-sm-8">{{ $vehicle->plate_number }}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">{{ __('Company') }}</div>
                        <div class="col-sm-8">{{ $vehicle->company->name }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">{{ __('Status') }}</div>
                        <div class="col-sm-8">{!! statusBadge($vehicle->status) !!}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">{{ __('License Number') }}</div>
                        <div class="col-sm-8">{{ $vehicle->license_number }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">{{ __('License Expiration') }}</div>
                        <div class="col-sm-8">
                            {{ $vehicle->license_expiration_date ? formatDate($vehicle->license_expiration_date) : '-' }}
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">{{ __('Transportation Method') }}</div>
                        <div class="col-sm-8">{{ $vehicle->transportionMethod?->name ?? '-' }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">{{ __('Created At') }}</div>
                        <div class="col-sm-8" dir="ltr">{{ formatDateTime($vehicle->created_at) }}</div>
                    </div>
                </div>
            </div>

            <!-- Documents -->
            <h6 class="text-muted mb-3">{{ __('Documents') }}</h6>
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">{{ __('Photo') }}</div>
                        <div class="col-sm-8">
                            @if ($vehicle->photo)
                                <a href="{{ $vehicle->photo->url }}" target="_blank"
                                    class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-external-link-alt me-1"></i> {{ __('View') }}
                                </a>
                            @else
                                <span class="text-muted">{{ __('Not uploaded') }}</span>
                            @endif
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">{{ __('Registration License') }}</div>
                        <div class="col-sm-8">
                            @if ($vehicle->registrationLicense)
                                <a href="{{ $vehicle->registrationLicense->url }}" target="_blank"
                                    class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-external-link-alt me-1"></i> {{ __('View') }}
                                </a>
                            @else
                                <span class="text-muted">{{ __('Not uploaded') }}</span>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">{{ __('Driving License') }}</div>
                        <div class="col-sm-8">
                            @if ($vehicle->drivingLicense)
                                <a href="{{ $vehicle->drivingLicense->url }}" target="_blank"
                                    class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-external-link-alt me-1"></i> {{ __('View') }}
                                </a>
                            @else
                                <span class="text-muted">{{ __('Not uploaded') }}</span>
                            @endif
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">{{ __('Insurance Policy') }}</div>
                        <div class="col-sm-8">
                            @if ($vehicle->insurancePolicy)
                                <a href="{{ $vehicle->insurancePolicy->url }}" target="_blank"
                                    class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-external-link-alt me-1"></i> {{ __('View') }}
                                </a>
                            @else
                                <span class="text-muted">{{ __('Not uploaded') }}</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Approval Status -->
            <h6 class="text-muted mb-3">{{ __('Approval Status') }}</h6>
            @if (auth('admin')->user()->hasPermission('update vehicle'))
                <div class="row mb-4" id="approval-status-wrapper">
                    <div class="col-md-6">
                        <div class="row mb-3">
                            <div class="col-sm-4 text-muted">{{ __('Status') }}</div>
                            <div class="col-sm-8 d-flex align-items-center gap-3">
                                {!! statusBadge($vehicle->approval_status) !!}

                                @if ($vehicle->approval_status === 'pending')
                                    <span class="text-muted">➡</span>

                                    <a href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#statusModal"
                                        onclick="openStatusModal(this)"
                                        data-url="{{ route('admin.vehicles.update-approval-status', $vehicle->id) }}"
                                        data-status="approved">
                                        <button class="btn btn-sm btn-success me-2">
                                            {{ __('Approve') }}
                                        </button>
                                    </a>

                                    <a href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#statusModal"
                                        onclick="openStatusModal(this)"
                                        data-url="{{ route('admin.vehicles.update-approval-status', $vehicle->id) }}"
                                        data-status="rejected">
                                        <button class="btn btn-sm btn-danger">
                                            {{ __('Reject') }}
                                        </button>
                                    </a>
                                @endif
                            </div>
                        </div>
                        @if ($vehicle->approval_status === 'rejected' && $vehicle->rejection_reason)
                            <div class="row mt-3">
                                <div class="col-sm-4 text-muted">{{ __('Rejection Reason') }}</div>
                                <div class="col-sm-8">{{ $vehicle->rejection_reason }}</div>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>

    @if (auth('admin')->user()->hasPermission('update vehicle'))
        <!-- Status Modal -->
        <div class="modal fade" id="statusModal" tabindex="-1">
            <div class="modal-dialog">
                <form action="" method="POST" id="statusForm">
                    @csrf
                    <input type="hidden" name="status" value="" id="modalStatusInput">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">{{ __('Confirmation') }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p id="statusModalText"></p>
                            <div id="rejectionReasonWrapper" class="mt-3 d-none">
                                <label for="rejectionReason" class="form-label">{{ __('Rejection Reason') }} <small
                                        class="text-muted">({{ __('Optional') }})</small></label>
                                <textarea class="form-control" id="rejectionReason" name="rejection_reason" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-sm btn-secondary"
                                data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                            <button type="submit" class="btn btn-sm btn-primary">{{ __('Confirm') }}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    @endif

    @push('js')
        <script>
            function openStatusModal(element) {
                let $el = $(element);
                let status = $el.data('status');
                let url = $el.data('url');

                $('#statusForm').attr('action', url);
                $('#modalStatusInput').val(status);

                const statusTexts = {
                    'approved': `{{ __('Are you sure you want to approve this vehicle?') }}`,
                    'rejected': `{{ __('Are you sure you want to reject this vehicle?') }}`
                };

                $('#statusModalText').text(statusTexts[status] ?? '');

                // Show/hide rejection reason based on status
                if (status === 'rejected') {
                    $('#rejectionReasonWrapper').removeClass('d-none');
                } else {
                    $('#rejectionReasonWrapper').addClass('d-none');
                    $('#rejectionReason').val(''); // Clear the textarea
                }
            }

            $('#statusForm').on('submit', function(e) {
                let modalEl = document.getElementById('statusModal');
                let modalInstance = bootstrap.Modal.getInstance(modalEl);
                if (modalInstance) {
                    modalInstance.hide();
                }
                return true;
            });

            // Clear rejection reason when modal is hidden
            $('#statusModal').on('hidden.bs.modal', function() {
                $('#rejectionReason').val('');
                $('#rejectionReasonWrapper').addClass('d-none');
            });
        </script>
    @endpush
</x-layout>
