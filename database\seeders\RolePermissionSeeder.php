<?php

namespace Database\Seeders;

use App\Models\Admin;
use App\Models\CompanyAdmin;
use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;

class RolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        $permissions = [
            'admin' => [
                'admins' => ['show admins', 'show admin', 'create admin', 'update admin', 'delete admin'],
                'companies' => ['show companies', 'show company', 'create company', 'update company', 'delete company'],
                'company registration requests' => ['show company registration requests', 'show company registration request', 'update company registration request'],
                'users' => ['show users', 'show user', 'create user', 'update user', 'delete user'],
                'countries' => ['show countries', 'show country', 'create country', 'update country', 'delete country'],
                'cities' => ['show cities', 'show city', 'create city', 'update city', 'delete city'],
                'areas' => ['show areas', 'show area', 'create area', 'update area', 'delete area'],
                'vehicles' => ['show vehicles', 'show vehicle', 'create vehicle', 'update vehicle', 'delete vehicle'],
                'orders' => ['show orders', 'show order'],
                'vehicles' => ['show vehicles', 'show vehicle', 'create vehicle', 'update vehicle', 'delete vehicle'],
                'drivers' => ['show drivers', 'show driver', 'delete driver', 'assign vehicles to driver'],
            ],
            'company' => [
                'admins' => ['show admins', 'show admin', 'create admin', 'update admin', 'delete admin'],
                'drivers' => ['show drivers', 'show driver', 'create driver', 'update driver', 'delete driver'],
                'driver registration requests' => ['show driver registration requests', 'show driver registration request', 'update driver registration request'],
                'orders' => ['show orders', 'show order'],
                'vehicles' => ['show vehicles', 'show vehicle', 'create vehicle', 'update vehicle', 'delete vehicle'],

            ],
        ];

        foreach ($permissions as $type => $groups) {
            foreach ($groups as $group => $actions) {
                foreach ($actions as $action) {
                    Permission::firstOrCreate([
                        'name' => $action,
                        'type' => $type,
                        'group' => $group,
                    ]);
                }
            }
        }

        // Main Roles

        // Admin Dashboard
        $super_admin = Role::firstOrCreate(['name' => 'super admin', 'type' => 'admin']);
        $admin_supervisor = Role::firstOrCreate(['name' => 'supervisor', 'type' => 'admin']);

        // Company Dashboard
        $super_company_admin = Role::firstOrCreate(['name' => 'super admin', 'type' => 'company']);
        $company_supervisor = Role::firstOrCreate(['name' => 'supervisor', 'type' => 'company']);

        // Assigning Permission To Roles

        // Admin Permissions
        $all_admin_permissions = Permission::where('type', 'admin')->get();
        $admin_supervisor_permissions = Permission::where('name', 'NOT LIKE', '%admin%')->where('type', 'admin')->get();

        $super_admin->permissions()->sync($all_admin_permissions);
        $admin_supervisor->permissions()->sync($admin_supervisor_permissions);

        // Company Admin Permissions
        $all_company_permissions = Permission::where('type', 'company')->get();
        $company_supervisor_permissions = Permission::where('name', 'NOT LIKE', '%admin%')->where('type', 'company')->get();

        $super_company_admin->permissions()->sync($all_company_permissions);
        $company_supervisor->permissions()->sync($company_supervisor_permissions);

        // Sync Current Super Admins Permissions
        Admin::where('role_id', 1)->get()->each(function (Admin $admin) use ($super_admin) {
            $admin->permissions()->sync($super_admin->permissions);
        });

        CompanyAdmin::where('role_id', 3)->get()->each(function (CompanyAdmin $admin) use ($super_company_admin) {
            $admin->permissions()->sync($super_company_admin->permissions);
        });
    }
}
