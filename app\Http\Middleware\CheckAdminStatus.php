<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckAdminStatus
{
    public function handle(Request $request, Closure $next): Response
    {
        $guard = $request->routeIs('admin.*') ? 'admin' : 'company';

        if (auth($guard)->user()->status != 'active') {
            auth($guard)->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route("$guard.login");
        }

        return $next($request);
    }
}
