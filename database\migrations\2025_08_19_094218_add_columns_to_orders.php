<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('payment_method');
            $table->enum('payer', ['sender', 'receiver'])->nullable()->after('status');
            $table->string('payment_method_id')->nullable()->after('payer')->constrained();
            $table->boolean('is_paid')->default(false)->after('payment_method_id');
        });
    }

    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('payment_method');
            $table->dropColumn('payment_method_id');
            $table->dropColumn('payer');
            $table->dropColumn('is_paid');
        });
    }
};
