@php
    $locales = config('app.locales', ['en', 'ar']);
@endphp

<x-layout :title="__('Area Details')">
    <x-session-message />

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title">{{ __('Area Details') }}</h4>
            <div>
                <a href="{{ route('admin.areas.edit', $area->id) }}" class="btn btn-primary">
                    {{ __('Edit') }}
                </a>
                <a href="{{ route('admin.areas.index') }}" class="btn btn-outline-secondary">
                    {{ __('Back') }}
                </a>
            </div>
        </div>

        <div class="card-body">
            <div class="row">
                @foreach ($locales as $locale)
                    <div class="col-md-4 mb-3">
                        <strong>{{ __('Name') }} ({{ strtoupper($locale) }})</strong>
                        <p>{{ $area->getTranslation('name', $locale) ?? '-' }}</p>
                    </div>
                @endforeach

                <div class="col-md-4 mb-3">
                    <strong>{{ __('City') }}</strong>
                    <p>{{ $area->city->name }}</p>
                </div>

                <div class="col-md-4 mb-3">
                    <strong>{{ __('Status') }}</strong>
                    <p>
                        <span class="badge bg-{{ $area->status === 'active' ? 'success' : 'danger' }}">
                            {{ __($area->status) }}
                        </span>
                    </p>
                </div>

                <div class="col-md-4 mb-3">
                    <strong>{{ __('Created At') }}</strong>
                    <p>{{ $area->created_at?->format('Y-m-d H:i') ?? '-' }}</p>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <strong>{{ __('Area Area') }}</strong>
                    <div id="map-container">
                        <input id="map-search-input" type="text" placeholder="{{ __('Search for a location') }}"
                            disabled>
                        <div id="area-map"></div>
                        <div id="map-controls" style="display: none;">
                            <button type="button" id="draw-polygon" class="btn btn-sm btn-primary map-button">
                                <i data-feather="edit-2"></i> {{ __('Draw Area') }}
                            </button>
                            <button type="button" id="clear-polygon" class="btn btn-sm btn-danger map-button">
                                <i data-feather="trash"></i> {{ __('Clear') }}
                            </button>
                        </div>
                        <div id="coordinates-container">
                            <input type="hidden" id="area-coordinates-json"
                                value="{{ json_encode($area->area?->getCoordinates() ?? []) }}">
                        </div>
                    </div>
                </div>
            </div>
            <input type="hidden" id="city-coordinates-json"
                value="{{ json_encode($area->city?->area?->getCoordinates() ?? []) }}">
        </div>
    </div>

    @push('css')
        <link rel="stylesheet" href="{{ asset('assets/css/city-map.css') }}">
    @endpush

    @push('js')
        <script
            src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.api_key') }}&libraries=drawing,places">
        </script>
        <script src="{{ asset('assets/js/area-map.js') }}"></script>
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                initMap();

                // Disable draw tools
                if (drawingManager) {
                    drawingManager.setMap(null);
                }

                // Hide controls
                const mapControls = document.getElementById('map-controls');
                if (mapControls) {
                    mapControls.style.display = 'none';
                }

                // Load city polygon if available
                const cityCoords = document.getElementById('city-coordinates-json').value;
                if (cityCoords && cityCoords !== '[]') {
                    try {
                        const parsed = JSON.parse(cityCoords);
                        drawCityPolygon(parsed);
                    } catch (e) {
                        console.error('Invalid city coordinates JSON:', e);
                    }
                }
            });
        </script>
    @endpush
</x-layout>
