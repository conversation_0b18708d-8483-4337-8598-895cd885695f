<?php

namespace App\Http\Resources;

use App\Enum\ChatMessageTypeEnum;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChatMessageResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'sent_by' => $this->sent_by,
            'content' => $this->type == ChatMessageTypeEnum::TEXT->value ? $this->content : $this->media->url,
            'created_at' => formatDateTime($this->created_at),
        ];
    }
}
