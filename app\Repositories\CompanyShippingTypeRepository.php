<?php

namespace App\Repositories;

use App\Models\City;
use App\Models\Company;
use App\Models\CompanyShippingType;
use App\Models\Country;
use Illuminate\Support\Collection;

class CompanyShippingTypeRepository
{
    private const TYPE_IMMEDIATE = 'immediate';

    private const TYPE_INTERCITY = 'intercity';

    private const TYPE_INTERNATIONAL = 'international';

    public function __construct(private readonly CompanyShippingType $model) {}

    public function create(Company $company, string $shippingTypeId, ?bool $hasExpressDelivery = null): CompanyShippingType
    {
        return $this->model->create([
            'company_id' => $company->id,
            'shipping_type_id' => $shippingTypeId,
            'has_express_delivery' => $hasExpressDelivery,
        ]);
    }

    public function get(Company $company, string $shippingTypeId): ?CompanyShippingType
    {
        return $this->model
            ->where(['company_id' => $company->id, 'shipping_type_id' => $shippingTypeId])
            ->first();
    }

    public function getCompanyShippingTypes(Company $company): Collection
    {
        return $this->model
            ->where('company_id', $company->id)
            ->with([
                'company.immediateShippingCities' => function ($query): void {
                    $query->whereHas('city', fn ($q) => $q->where('status', 'active'))
                        ->with([
                            'city' => fn ($q) => $q->where('status', 'active'),
                            'areas' => fn ($q) => $q->where('status', 'active'),
                        ]);
                },
                'company.intercityShippingPickupCities' => fn ($query) => $query->where('status', 'active'),
                'shippingType',
                'transportionMethods' => fn ($query) => $query->where('transportion_methods.status', 'active')->distinct('transportion_methods.id'),
            ])
            ->get();
    }

    /**
     * @return Collection<int, array{id:int, name:string, has_express_delivery:bool}>
     */
    public function getAvailableShippingTypesForCity(City $city): Collection
    {
        $available = collect();

        $typesWithRelations = [
            self::TYPE_IMMEDIATE => 'company.immediateShippingCities',
            self::TYPE_INTERCITY => 'company.intercityShippingPickupCities',
        ];

        foreach ($typesWithRelations as $type => $relation) {
            $available->push($this->fetchShippingTypeForCity($type, $city, $relation));
        }

        if ($this->isUaeCity($city)) {
            // International shipping is treated differently:
            // available to any company if the city is in UAE.
            // If this is not intended, use 'company.internationalShippingCities' instead.
            $available->push($this->fetchShippingTypeForCity(self::TYPE_INTERNATIONAL, $city));
        }

        // Group by shipping type id and merge express delivery flags
        return $available
            ->filter()
            ->groupBy('id')
            ->map(function ($items) {
                $first = $items->first();
                $first['has_express_delivery'] = $items->contains(
                    fn ($it) => (bool) $it['has_express_delivery']
                );

                return $first;
            })
            ->values();
    }

    /**
     * Fetch shipping type availability for a given city and relation.
     *
     * @return array{id:int, name:string, has_express_delivery:bool}|null
     */
    private function fetchShippingTypeForCity(string $type, City $city, ?string $relation = null): ?array
    {
        $query = $this->model->newQuery()
            ->whereHas('shippingType', fn ($q) => $q->where('name', $type));

        if ($relation) {
            $query->whereHas($relation, fn ($q) => $q->where('city_id', $city->id));
        }

        $shippingTypeModel = $query->with('shippingType:id,name')->first();
        if (! $shippingTypeModel) {
            return null;
        }

        $hasExpress = (bool) $this->model->newQuery()
            ->whereHas('shippingType', fn ($q) => $q->where('name', $type))
            ->when(
                $relation,
                fn ($q) => $q->whereHas($relation, fn ($qq) => $qq->where('city_id', $city->id))
            )
            ->where('has_express_delivery', true)
            ->exists();

        return [
            'id' => $shippingTypeModel->shippingType->id,
            'name' => $shippingTypeModel->shippingType->name,
            'has_express_delivery' => $hasExpress,
        ];
    }

    /**
     * Check if the given city is in the UAE.
     */
    private function isUaeCity(City $city): bool
    {
        if ($city->relationLoaded('country')) {
            return optional($city->country)->abbv === 'AE';
        }

        if (optional($city->country)->abbv === 'AE') {
            return true;
        }

        return (bool) $city->country_id &&
            Country::whereKey($city->country_id)
                ->where('abbv', 'AE')
                ->exists();
    }
}
