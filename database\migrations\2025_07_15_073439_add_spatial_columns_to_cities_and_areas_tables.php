<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cities', function (Blueprint $table) {
            $table->geometry('location', subtype: 'point')->nullable()->after('status');
            $table->geometry('area', subtype: 'polygon')->nullable()->after('location');
        });

        Schema::table('areas', function (Blueprint $table) {
            $table->geometry('area', subtype: 'polygon')->nullable()->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cities', function (Blueprint $table) {
            $table->dropColumn(['location', 'area']);
        });

        Schema::table('areas', function (Blueprint $table) {
            $table->dropColumn('area');
        });
    }
};
