<x-layout :title="__('Order') . ' #' . $order->order_number">
    <div class="card shadow-sm rounded-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ __('Order') }} #{{ $order->order_number }}</h5>
        </div>
        <div class="card-body">
            <!-- Section 1: User Info -->
            <div class="mb-4">
                <h6 class="text-primary">{{ __('User Info') }}</h6>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Name') }}</div>
                    <div class="col-sm-9">{{ $order->user->name }}</div>
                </div>
                <div class="row">
                    <div class="col-sm-3 text-muted">{{ __('Phone Number') }}</div>
                    <div class="col-sm-9">{!! formatPhone($order->user->country_code, $order->user->phone) !!}</div>
                </div>
            </div>
            <hr>
            <!-- Section 2: General Order Summary -->
            <div class="mb-4">
                <h6 class="text-primary">{{ __('General Order Summary') }}</h6>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Order Number') }}</div>
                    <div class="col-sm-9">#{{ $order->order_number }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Date') }}</div>
                    <div class="col-sm-9" dir="ltr">{{ formatDateTime($order->created_at) }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Shipping Type') }}</div>
                    <div class="col-sm-9">{{ $order->shippingType->name }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Shipping Method') }}</div>
                    <div class="col-sm-9">
                        {{ $order->shipping_type_id != 1 ? ($order->is_express ? 'Express' : 'Standard') : '-' }}
                    </div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Shipment Size') }}</div>
                    <div class="col-sm-9">{{ $order->shippingSize->name }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Shipments Count') }}</div>
                    <div class="col-sm-9">{{ $order->items->count() }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Status') }}</div>
                    <div class="col-sm-9">
                        <span id="general-order-status" style="margin-right: 20px">{!! __(ucfirst(str_replace('_', ' ', $order->status->value))) !!}</span>
                        @if (in_array($order->status->value, [
                                \App\Enum\OrderStatus::READY_TO_PICKUP->value,
                                \App\Enum\OrderStatus::HEADING_TO_PICKUP->value,
                            ]))
                            <button type="button" id="cancelOrderBtn" class="btn btn-danger btn-sm cancel-order-btn"
                                data-url="{{ route('admin.orders.cancel', $order->id) }}">
                                {{ __('Cancel') }}
                                <span class="spinner-border spinner-border-sm ms-2 d-none" id="cancelOrderSpinner"
                                    role="status" aria-hidden="true"></span>
                            </button>
                        @endif
                    </div>
                </div>
                <div class="row mb-2 {{ $order->status->value !== \App\Enum\OrderStatus::CANCELLED->value ? 'd-none' : '' }}"
                    id="cancelReasonRow">
                    <div class="col-sm-3 text-muted">{{ __('Cancel Reason') }}</div>
                    <div class="col-sm-9" id="cancelReasonText">
                        {{ $order->status->value === \App\Enum\OrderStatus::CANCELLED->value ? $order->cancel_reason : '' }}
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <!-- Section 3: Pricing -->
            <div class="mb-4">
                <h6 class="text-primary">{{ __('Pricing') }}</h6>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Payer') }}</div>
                    <div class="col-sm-9">{{ __(ucfirst($order->payer ?? '-')) }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Payment Method') }}</div>
                    <div class="col-sm-9">{{ __(ucfirst($order->payment_method?->name ?? '-')) }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Fees') }}</div>
                    <div class="col-sm-9">{{ formatMoney($order->fees) }} {{ __('AED') }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Cost') }}</div>
                    <div class="col-sm-9">{{ formatMoney($order->cost) }} {{ __('AED') }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Total') }}</div>
                    <div class="col-sm-9">{{ formatMoney($order->total) }} {{ __('AED') }}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Payment Status') }}</div>
                    <div class="col-sm-9">
                        <span class="badge bg-label-{{ $order->is_paid ? 'success' : 'danger' }}">
                            {{ __($order->is_paid ? 'Paid' : 'Not Paid') }}
                        </span>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <!-- Section 4: Pickup Address/Location -->
            <div class="mb-4">
                <h6 class="text-primary">{{ __('Pickup Address/Location') }}</h6>
                @if ($order->pickupAddress)
                    <div class="card border-0 shadow-sm rounded-4">
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-sm-3 text-muted">{{ __('Address Name') }}</div>
                                <div class="col-sm-9">{{ $order->pickupAddress->name }}</div>
                            </div>
                            @if ($order->pickupAddress->city)
                                <div class="row mb-2">
                                    <div class="col-sm-3 text-muted">{{ __('City') }}</div>
                                    <div class="col-sm-9">{{ $order->pickupAddress->city->name }}</div>
                                </div>
                            @endif
                            @if ($order->pickupAddress->area)
                                <div class="row mb-2">
                                    <div class="col-sm-3 text-muted">{{ __('Area') }}</div>
                                    <div class="col-sm-9">{{ $order->pickupAddress->area->name }}</div>
                                </div>
                            @endif
                            @if ($order->pickupCountry)
                                <div class="row mb-2">
                                    <div class="col-sm-3 text-muted">{{ __('Country') }}</div>
                                    <div class="col-sm-9">{{ $order->pickupCountry->name }}</div>
                                </div>
                            @endif
                            @if ($order->pickupAddress->phone)
                                <div class="row mb-2">
                                    <div class="col-sm-3 text-muted">{{ __('Phone') }}</div>
                                    <div class="col-sm-9">{!! formatPhone($order->pickupAddress->country_code, $order->pickupAddress->phone) !!}</div>
                                </div>
                            @endif
                            @if ($order->pickup_coordinates['lat'] && $order->pickup_coordinates['lng'])
                                <div class="row">
                                    <div class="col-sm-3 text-muted">{{ __('Coordinates') }}</div>
                                    <div class="col-sm-9">
                                        <span class="badge bg-label-secondary me-2">
                                            {{ __('Lat') }}:
                                            {{ number_format($order->pickup_coordinates['lat'], 6) }},
                                            {{ __('Lng') }}:
                                            {{ number_format($order->pickup_coordinates['lng'], 6) }}
                                        </span>
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                            onclick="showLocationOnMap({{ $order->pickup_coordinates['lat'] }}, {{ $order->pickup_coordinates['lng'] }}, '{{ __('Pickup Location') }}')">
                                            <i class="fas fa-map-marker-alt"></i> {{ __('View on Map') }}
                                        </button>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @elseif($order->pickup_coordinates['lat'] && $order->pickup_coordinates['lng'])
                    <div class="card border-0 shadow-sm rounded-4">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-3 text-muted">{{ __('Pickup Coordinates') }}</div>
                                <div class="col-sm-9">
                                    <span class="badge bg-label-secondary me-2">
                                        {{ __('Lat') }}:
                                        {{ number_format($order->pickup_coordinates['lat'], 6) }},
                                        {{ __('Lng') }}:
                                        {{ number_format($order->pickup_coordinates['lng'], 6) }}
                                    </span>
                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                        onclick="showLocationOnMap({{ $order->pickup_coordinates['lat'] }}, {{ $order->pickup_coordinates['lng'] }}, '{{ __('Pickup Location') }}')">
                                        <i class="fas fa-map-marker-alt"></i> {{ __('View on Map') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="text-muted">{{ __('No pickup address or location specified') }}</div>
                @endif
            </div>
            <hr class="my-4">
            <!-- Section 5: Driver Info -->

            @if ($order->driver)
                <div class="mb-4">
                    <h6 class="text-primary mb-3">{{ __('Driver Information') }}</h6>
                    <div class="card border-0 shadow-sm rounded-4">
                        <div class="card-body">
                            <div class="row align-items-center g-4">
                                <div class="col-auto">
                                    @if ($order->driver?->profileImage)
                                        <img src="{{ $order->driver?->profileImage->url ?: 'https://placehold.co/600x400' }}"
                                            alt="{{ $order->driver?->name }}" class="rounded-circle shadow-sm"
                                            style="width: 80px; height: 80px; object-fit: cover;">
                                    @else
                                        <div class="rounded-circle shadow-sm d-flex align-items-center justify-content-center bg-light text-muted"
                                            style="width: 80px; height: 80px; font-size: 12px;">
                                            {{ __('No Image') }}
                                        </div>
                                    @endif
                                </div>
                                <div class="col">
                                    <div><span class="text-muted">{{ __('Driver Name') }}:</span>
                                        <strong>{{ $order->driver->name }}</strong>
                                    </div>
                                    <div><span class="text-muted">{{ __('Phone') }}:</span>
                                        {!! formatPhone($order->driver->country_code, $order->driver->phone) !!}
                                    </div>
                                    <div><span class="text-muted">{{ __('Vehicle Model') }}:</span>
                                        {{ $order->vehicle?->model }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <hr class="my-4">
            @endif
            <!-- Section 6: Order Items -->
            <div class="mb-5">
                <h5 class="text-primary mb-4">{{ __('Order Items') }}</h5>
                <div class="row g-4">
                    @foreach ($order->items as $item)
                        <div class="col-md-6">
                            <div class="card border-0 shadow rounded-4 h-100">
                                <div class="card-body py-4 px-4">
                                    <div class="mb-3">
                                        <h6 class="fw-semibold mb-2 text-dark">
                                            {{ __('Shipment #:') }} {{ $item->shipment_number }}
                                        </h6>
                                        <p class="text-muted mb-1">{{ $item->description }}</p>
                                    </div>
                                    <div class="border-top pt-2">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">{{ __('Recipient Name') }}</span>
                                            <span class="fw-medium text-dark">{{ $item->recipient_name }}</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">{{ __('Recipient Phone') }}</span>
                                            <span class="fw-medium text-dark">{!! formatPhone($item->country_code, $item->phone) !!}</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">{{ __('Status') }}</span>
                                            <span class="badge bg-label-info">{!! __(str_replace('_', ' ', $item->status)) !!}</span>
                                        </div>
                                    </div>
                                    <!-- Dropoff Address/Location -->
                                    <div class="border-top pt-3 mb-3">
                                        <h6 class="fw-semibold mb-2 text-dark">
                                            {{ __('Dropoff Address/Location') }}
                                        </h6>
                                        @if ($item->dropoffAddress)
                                            <div class="mb-2">
                                                <span class="text-muted">{{ __('Address Name') }}:</span>
                                                <span class="fw-medium">{{ $item->dropoffAddress->name }}</span>
                                            </div>
                                            @if ($item->dropoffAddress->area->city)
                                                <div class="mb-2">
                                                    <span class="text-muted">{{ __('City') }}:</span>
                                                    <span
                                                        class="fw-medium">{{ $item->dropoffAddress->area->city->name }}</span>
                                                </div>
                                            @endif
                                            @if ($item->dropoffAddress->area)
                                                <div class="mb-2">
                                                    <span class="text-muted">{{ __('Area') }}:</span>
                                                    <span
                                                        class="fw-medium">{{ $item->dropoffAddress->area->name }}</span>
                                                </div>
                                            @endif
                                            @if ($item->dropoffCountry)
                                                <div class="mb-2">
                                                    <span class="text-muted">{{ __('Country') }}:</span>
                                                    <span class="fw-medium">{{ $item->dropoffCountry->name }}</span>
                                                </div>
                                            @endif
                                            @if ($item->dropoffAddress->phone)
                                                <div class="mb-2">
                                                    <span class="text-muted">{{ __('Phone') }}:</span>
                                                    <span class="fw-medium">{!! formatPhone($item->dropoffAddress->country_code, $item->dropoffAddress->phone) !!}</span>
                                                </div>
                                            @endif
                                        @endif
                                        @if ($item->dropoff_coordinates['lat'] && $item->dropoff_coordinates['lng'])
                                            <div class="mb-2">
                                                <span class="text-muted">{{ __('Coordinates') }}:</span>
                                                <span class="badge bg-label-secondary me-2">
                                                    {{ __('Lat') }}:
                                                    {{ number_format($item->dropoff_coordinates['lat'], 6) }},
                                                    {{ __('Lng') }}:
                                                    {{ number_format($item->dropoff_coordinates['lng'], 6) }}
                                                </span>
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                    onclick="showLocationOnMap({{ $item->dropoff_coordinates['lat'] }}, {{ $item->dropoff_coordinates['lng'] }}, '{{ __('Dropoff Location') }} - {{ $item->shipment_number }}')">
                                                    <i class="fas fa-map-marker-alt"></i> {{ __('View on Map') }}
                                                </button>
                                            </div>
                                        @endif
                                        @if (!$item->dropoffAddress && (!$item->dropoff_coordinates['lat'] || !$item->dropoff_coordinates['lng']))
                                            <div class="text-muted">
                                                {{ __('No dropoff address or location specified') }}</div>
                                        @endif
                                    </div>
                                    <div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">{{ __('Cost') }}</span>
                                            <span class="fw-medium">{{ formatMoney($item->cost) }}
                                                {{ __('AED') }}
                                            </span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">{{ __('Fees') }}</span>
                                            <span class="fw-medium">{{ formatMoney($item->fees) }}
                                                {{ __('AED') }}
                                            </span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <strong>{{ __('Total') }}</strong>
                                            <strong>{{ formatMoney($item->cost + $item->fees) }}
                                                {{ __('AED') }}
                                            </strong>
                                        </div>
                                    </div>
                                    @if ($item->media->isNotEmpty())
                                        <div class="mt-4">
                                            <label class="form-label text-muted">{{ __('Shipment Images') }}</label>
                                            <div class="d-flex flex-wrap gap-2">
                                                @foreach ($item->media as $media)
                                                    <a href="{{ $media->url }}" target="_blank">
                                                        <img src="{{ $media->url }}" class="img-thumbnail"
                                                            style="width: 80px; height: 80px; object-fit: cover;"
                                                            alt="Image">
                                                    </a>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    @include('pages.admin.orders.partials.map')
    @include('pages.admin.orders.partials.cancel')
    @include('pages.admin.orders.partials.scripts')
</x-layout>
