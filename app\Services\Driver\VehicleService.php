<?php

namespace App\Services\Driver;

use App\Models\Vehicle;
use App\Repositories\DriverRepository;
use App\Repositories\DriverVehicleRepository;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class VehicleService
{
    public function __construct(private readonly DriverVehicleRepository $driverVehicleRepository, private readonly DriverRepository $driverRepository) {}

    public function acceptVehicle(Vehicle $vehicle): void
    {
        $driverVehicle = $this->driverVehicleRepository->get(auth('driver')->id(), $vehicle->id);

        if (! $driverVehicle) {
            throw new BadRequestHttpException('This vehicle is not assigned to this driver');
        }

        if ($driverVehicle->approval_status != 'pending') {
            throw new BadRequestHttpException('This vehicle is not pending for this driver');
        }

        DB::transaction(function () use ($driverVehicle): void {
            $this->driverVehicleRepository->update($driverVehicle, ['approval_status' => 'approved']);

            $hasActiveVehicle = $this->driverVehicleRepository->hasActiveVehicle(auth('driver')->user());

            // set this vehicle as active if he has no active vehicle
            if (! $hasActiveVehicle) {
                $this->driverVehicleRepository->update($driverVehicle, ['is_active' => true]);
            }
        });
    }

    public function activateVehicle(Vehicle $vehicle): void
    {
        $driverVehicle = $this->driverVehicleRepository->get(auth('driver')->id(), $vehicle->id);

        if (! $driverVehicle) {
            throw new BadRequestHttpException('This vehicle is no assigned to this driver');
        }

        if ($driverVehicle->approval_status != 'approved') {
            throw new BadRequestHttpException('This vehicle is not an approved vehicle for this driver');
        }

        $hasActiveOrder = $this->driverRepository->hasActiveOrder($driverVehicle->driver);

        if ($hasActiveOrder) {
            throw new BadRequestHttpException(__("Can't switch vehicle while driver has an active"));
        }

        $assignedVehicles = $this->driverVehicleRepository->getApprovedDriverVehicles(auth('driver')->user());

        DB::transaction(function () use ($assignedVehicles, $driverVehicle): void {

            foreach ($assignedVehicles as $assignedVehicle) {
                $this->driverVehicleRepository->update($assignedVehicle, ['is_active' => false]);
            }

            $this->driverVehicleRepository->update($driverVehicle->refresh(), ['is_active' => true]);
        });
    }
}
