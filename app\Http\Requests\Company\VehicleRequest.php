<?php

namespace App\Http\Requests\Company;

use App\Http\Requests\BaseFormRequest;
use App\Rules\LocalizedAlpha;
use App\Rules\ValidMedia;
use Illuminate\Validation\Rule;

class VehicleRequest extends BaseFormRequest
{
    public function rules(): array
    {
        $vehicle = $this->route('vehicle');

        $rules = [
            'transportion_method_id' => ['required', 'exists:transportion_methods,id'],
            'model' => ['required', 'string', 'min:2', 'max:50', 'regex:/^[\pL\pN\s\-]+$/u'],
            'year' => ['required', 'integer', 'min:1980', 'max:'.now()->year],
            'plate_number' => ['required', 'string', Rule::unique('vehicles')->ignore($vehicle)],
            'license_number' => ['required', 'string', 'max:30', Rule::unique('vehicles')->ignore($vehicle)],
            'license_expiration_date' => ['required', 'date', 'after_or_equal:today'],
            'photo' => [Rule::requiredIf(! (bool) $vehicle), new ValidMedia(['image'])],
            'registration_license' => [Rule::requiredIf(! (bool) $vehicle), new ValidMedia(['image'])],
            'driving_license' => [Rule::requiredIf(! (bool) $vehicle), new ValidMedia(['image'])],
            'insurance_policy' => ['nullable', new ValidMedia(['image'])],
        ];

        foreach (config('app.locales', ['en', 'ar']) as $locale) {
            $rules["name.{$locale}"] = ['required', 'string', 'min:3', 'max:255', new LocalizedAlpha, Rule::unique('vehicles', "name->{$locale}")->ignore($vehicle?->id)];
        }

        return $rules;
    }
}
