<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            // Drop foreign keys first
            $table->dropForeign(['city_id']);
            $table->dropForeign(['country_id']);

            // Then drop the columns
            $table->dropColumn(['city_id', 'country_id']);
        });
    }

    public function down(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            // Re-add the columns
            $table->foreignId('city_id')
                ->nullable()
                ->constrained()
                ->nullOnDelete();

            $table->foreignId('country_id')
                ->nullable()
                ->constrained()
                ->nullOnDelete();
        });
    }
};
