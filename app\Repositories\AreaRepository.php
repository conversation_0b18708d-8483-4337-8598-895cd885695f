<?php

namespace App\Repositories;

use App\DTO\Admin\AreaDTO;
use App\Models\Area;
use App\Models\City;
use MatanYadaev\EloquentSpatial\Objects\Point;

class AreaRepository
{
    public function __construct(private readonly Area $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByIds(array $ids)
    {
        return $this->model->whereIn('id', $ids)->get();
    }

    public function getByCity(City $city)
    {
        return $this->model->where('city_id', $city->id)->where('status', 'active')->get();
    }

    public function isPointInAnyAreaPolygon(float $lat, float $lng)
    {
        // Create a Point object from the given coordinates
        // Note: Point constructor takes (latitude, longitude) in that order
        $point = new Point($lat, $lng);

        // First try using the whereContains scope from HasSpatial trait
        $area = $this->model->where('status', 'active')->whereContains('area', $point)->first();

        if ($area) {
            return $area;
        }

        // If that doesn't work, try the raw SQL approach
        // Note: In MySQL, the POINT function takes longitude first, then latitude
        try {
            $area = $this->model
                ->where('status', 'active')
                ->whereRaw('ST_Contains(area, ST_GeomFromText(?))', ["POINT($lng $lat)"])
                ->first();

            if ($area) {
                return $area;
            }
        } catch (\Exception) {
            // If raw SQL fails, continue to return null
        }

        return null;
    }

    public function create(AreaDTO $dto)
    {
        return $this->model->create($dto->toArray());
    }

    public function update(Area $area, AreaDTO $dto)
    {
        return $area->update($dto->toArray());
    }
}
