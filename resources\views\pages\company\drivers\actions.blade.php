@php
$hasNoVehicles = $driver->vehicles()->count() == 0;
$canShow = auth('company')->user()->hasPermission('show driver');
$canEdit = auth('company')->user()->hasPermission('update driver');
$canDelete = auth('company')->user()->hasPermission('delete driver');
@endphp

<div class="actions">
    @if($canShow || $canEdit || $canDelete || $hasNoVehicles)

        @if($hasNoVehicles)
        <a href="{{ route('company.drivers.assign-vehicles', $driver->id) }}"><i class="ti ti-motorbike"></i></a>
        @endif

        @if($canShow)
        <a href="{{ route('company.drivers.show', $driver->id) }}"><i class="ti ti-eye"></i></a>
        @endif

        @if($canEdit)
        <a href="{{ route('company.drivers.edit', $driver->id) }}"><i class="ti ti-edit"></i></a>
        @endcan

        @if($canDelete)
        <a href="javascript:void(0)">
            <i
                data-bs-toggle="modal"
                data-bs-target="#delete-modal"
                onclick="changeDeleteModalData(this)"
                delete-route="{{ route('company.drivers.destroy', $driver->id) }}"
                delete-name="{{ __('Driver') }} : {{ $driver->name }}"
                class="ti ti-archive">
            </i>
        </a>
        @endif
    @else
    -
    @endif
</div>