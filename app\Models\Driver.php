<?php

namespace App\Models;

use App\Enum\OrderStatus;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Laravel\Sanctum\HasApiTokens;
use MatanYadaev\EloquentSpatial\Objects\Point;

class Driver extends Authenticatable
{
    use HasApiTokens, SoftDeletes;

    protected $fillable = [
        'name',
        'country_code',
        'phone',
        'email',
        'birth_date',
        'gender',
        'company_id',
        'city_id',
        'id_number',
        'bank_name',
        'bank_account_owner',
        'bank_account_number',
        'iban',
        'status',
        'approval_status',
        'is_available',
        'push_notifications_enabled',
        'current_lat',
        'current_lng',
        'app_locale',
    ];

    protected $casts = [
        'is_available' => 'boolean',
        'push_notifications_enabled' => 'boolean',
    ];

    public function scopeActive(Builder $query): void
    {
        $query->where('status', 'active');
    }

    public function scopeAvailable(Builder $query): void
    {
        $query->where('is_available', true);
    }

    public function scopeApproved(Builder $query): void
    {
        $query->where('approval_status', 'approved');
    }

    public function getPhoneAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getEmailAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getIdNumberAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function media()
    {
        return $this->morphMany(Media::class, 'mediable');
    }

    public function profileImage()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'profile');
    }

    public function getProfileImageUrlAttribute()
    {
        return $this->image ? $this->image->url : asset('user-default.png');
    }

    public function idImage()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'id_number');
    }

    public function drivingLiscenceImage()
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'driving_licence');
    }

    public function additionalAttachments()
    {
        return $this->morphMany(Media::class, 'mediable')->where('type', 'additional');
    }

    public function intercityShippingCities()
    {
        return $this->belongsToMany(City::class, 'driver_shipping_cities', 'driver_id', 'city_id')->where('shipping_type', 'intercity');
    }

    public function internationalShippingCities()
    {
        return $this->belongsToMany(City::class, 'driver_shipping_cities', 'driver_id', 'city_id')->where('shipping_type', 'international');
    }

    public function immediateShippingCities()
    {
        return $this->hasMany(DriverImmediateShippingCity::class, 'driver_id');
    }

    public function shippingTypes(): HasMany
    {
        return $this->hasMany(DriverShippingType::class);
    }

    public function vehicles()
    {
        return $this->hasMany(DriverVehicle::class);
    }

    public function activeVehicle(): HasOne
    {
        return $this->hasOne(DriverVehicle::class)->where('is_active', true);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function activeOrder(): HasOne
    {
        return $this->hasOne(Order::class)->whereIn('status', [
            OrderStatus::READY_TO_PICKUP->value,
            OrderStatus::HEADING_TO_PICKUP->value,
            OrderStatus::ARRIVED_AT_PICKUP->value,
            OrderStatus::PICKED_UP->value,
            OrderStatus::IN_TRANSIT->value,
            OrderStatus::ARRIVED_AT_DELIVERY_LOCATION->value,
        ]);
    }

    public function getCompletedOrdersCountAttribute()
    {
        return $this->orders()->whereIn('status', [
            OrderStatus::DELIVERED->value,
            OrderStatus::PARTIALLY_DELIVERED->value,
            OrderStatus::DELIVERED_TO_LOCAL_HUB->value,
        ])->count();
    }

    public function getAmountDueAttribute()
    {
        return $this->orders()->whereIn('status', [
            OrderStatus::DELIVERED->value,
            OrderStatus::PARTIALLY_DELIVERED->value,
            OrderStatus::DELIVERED_TO_LOCAL_HUB->value,
        ])->sum('total');
    }

    /**
     * Scope: Exclude drivers who have an active order
     */
    public function scopeWithoutActiveOrder(Builder $query): void
    {
        $query->whereDoesntHave('activeOrder');
    }

    /**
     * Scope: Drivers whose active vehicle is suitable for the given order
     */
    public function scopeSupportsOrder(Builder $query, Order $order): void
    {
        $query->whereHas(
            'activeVehicle.vehicle.transportionMethod.companyShippingTypeSizeTransportionMethods.companyShippingTypeSize.companyShippingType',
            function ($q) use ($order) {
                $q->where('shipping_type_id', $order->shipping_type_id);
            }
        )
            ->whereHas(
                'activeVehicle.vehicle.transportionMethod.companyShippingTypeSizeTransportionMethods.companyShippingTypeSize',
                function ($q) use ($order) {
                    $q->where('shipping_size_id', $order->shipping_size_id);
                }
            )
            ->whereHas('company', function ($q) use ($order) {
                $q->active()
                    ->whereHas('shippingTypes', function ($q2) use ($order) {
                        $q2->where('shipping_type_id', $order->shipping_type_id);

                        if ($order->shipping_type_id != 1 && $order->is_express) {
                            $q2->where('has_express_delivery', true);
                        }
                    });
            });
    }

    /**
     * Scope: Drivers within a given radius (in meters) from a location
     */
    public function scopeWithinRadius(Builder $query, Point $location, float $radiusInMeters): void
    {
        $query->whereNotNull('current_lat')
            ->whereNotNull('current_lng')
            ->selectRaw(
                'drivers.*, ST_Distance_Sphere(POINT(current_lng, current_lat), POINT(?, ?)) as distance',
                [$location->longitude, $location->latitude]
            )
            ->whereRaw(
                'ST_Distance_Sphere(POINT(current_lng, current_lat), POINT(?, ?)) <= ?',
                [$location->longitude, $location->latitude, $radiusInMeters]
            );
    }
}
