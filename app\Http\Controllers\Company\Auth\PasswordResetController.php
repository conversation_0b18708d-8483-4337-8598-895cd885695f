<?php

namespace App\Http\Controllers\Company\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ResetPasswordRequest;
use App\Http\Requests\Company\ForgotPasswordRequest;
use App\Mail\PasswordResetMail;
use App\Repositories\CompanyAdminRepository;
use App\Repositories\PasswordResetCodeRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class PasswordResetController extends Controller
{
    public function __construct(
        private readonly CompanyAdminRepository $companyAdminRepository,
        private readonly PasswordResetCodeRepository $passwordResetCodeRepository
    ) {}

    public function forgotPassowrd()
    {
        return view('auth.forgot-password');
    }

    public function sendResetCode(ForgotPasswordRequest $request)
    {
        if (RateLimiter::tooManyAttempts("forgot-password-attempts-company:$request->email", 3)) {

            $seconds = RateLimiter::availableIn("forgot-password-attempts-company:$request->email");
            $remainingTime = formatSecondsToHoursTime($seconds);

            return back()->with('fail', __('limit reached: retry after :time hours', ['time' => $remainingTime]));
        }

        RateLimiter::hit("forgot-password-attempts-company:$request->email", 12 * 60 * 60);

        $resetCode = App::environment('local') ? '123456' : fake()->randomNumber(6, true);

        $admin = $this->companyAdminRepository->getByEmail($request->email);

        DB::transaction(function () use ($admin, $resetCode, $request): void {
            $this->passwordResetCodeRepository->create($admin, $resetCode);
            Mail::to($request->email)->send(new PasswordResetMail($resetCode));
        });

        return to_route('company.confirm-reset-code')->with([
            'email' => $request->email,
            'success' => __('reset code has been sent to your email successfully'),
        ]);
    }

    public function confirmResetCodeView()
    {
        if (! session()->has('email')) {
            return redirect()->route('company.forgot-password');
        }

        return view('auth.confirm-reset-code');
    }

    public function confirmResetCode(Request $request)
    {
        if (RateLimiter::tooManyAttempts("confirm-reset-attempts-company:$request->email", 4)) {

            $seconds = RateLimiter::availableIn("confirm-reset-attempts-company:$request->email");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            return back()->with([
                'fail' => __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
                'email' => $request->email,
            ]);
        }

        RateLimiter::hit("confirm-reset-attempts-company:$request->email", 120);

        $admin = $this->companyAdminRepository->getByEmail($request->email);

        $code_record = $this->passwordResetCodeRepository->get($admin, $request->code);

        if (! $code_record) {
            session()->flash('email', $request->email);

            throw ValidationException::withMessages([
                'code' => __('invalid code'),
            ]);
        }

        $codeDate = Carbon::parse($code_record->created_at);
        $currentDate = Carbon::now();

        if ($currentDate->diffInHours($codeDate) > 1) {
            session()->flash('email', $request->email);

            throw ValidationException::withMessages([
                'code' => __('expired code'),
            ]);
        }

        return to_route('company.reset-password')->with([
            'email' => $request->email,
            'code' => $request->code,
        ]);
    }

    public function resetPasswordView()
    {
        if (! session()->has('email') || ! session()->has('code')) {
            return redirect()->route('company.forgot-password');
        }

        return view('auth.reset-password');
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        if (RateLimiter::tooManyAttempts("reset-password-attempts-company:$request->email", 4)) {

            $seconds = RateLimiter::availableIn("reset-password-attempts-company:$request->email");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            return back()->with([
                'fail' => __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
                'email' => $request->email,
                'code' => $request->code,
            ]);
        }

        RateLimiter::hit("reset-password-attempts-company:$request->email", 120);

        session()->flash('email', $request->email);
        session()->flash('code', $request->code);

        $admin = $this->companyAdminRepository->getByEmail($request->email);

        $code_record = $this->passwordResetCodeRepository->get($admin, $request->code);

        if (! $code_record) {
            throw ValidationException::withMessages([
                'code' => __('invalid code'),
            ]);
        }

        $codeDate = Carbon::parse($code_record->created_at);
        $currentDate = Carbon::now();

        if ($currentDate->diffInHours($codeDate) > 1) {
            throw ValidationException::withMessages([
                'code' => __('expired code'),
            ]);
        }

        DB::transaction(function () use ($admin, $request): void {
            $this->companyAdminRepository->updatePassword($admin, $request->password);
            $this->passwordResetCodeRepository->deleteAll($admin);
        });

        return to_route('company.login')->with('success', __('password has been reset successfully'));
    }
}
