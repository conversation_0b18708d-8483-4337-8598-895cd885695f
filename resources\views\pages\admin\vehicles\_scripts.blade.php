@push('js')
    <script>
        $(document).ready(function() {
            // Initialize date picker
            $("#license_expiration_date").flatpickr({
                enableTime: false,
                dateFormat: "Y-m-d",
            });

            const transportationSelect = $('#transportion_method_id');
            const companySelect = $('#company_id');

            // Initialize select2
            transportationSelect.select2({
                placeholder: "{{ __('Choose') }}...",
                allowClear: true
            });

            // Function to load transportation methods
            function loadTransportationMethods(companyId, callback) {
                transportationSelect.empty().append(`<option disabled selected>{{ __('Choose') }}...</option>`);

                if (!companyId) return;

                $.ajax({
                    url: "{{ route('admin.companies.transportation-methods', ['company' => ':id']) }}"
                        .replace(':id', companyId),
                    type: 'GET',
                    success: function(response) {
                        response.forEach(function(method) {
                            transportationSelect.append(new Option(method.name, method.id));
                        });
                        if (typeof callback === 'function') callback();
                    },
                    error: function() {
                        console.error('Failed to fetch transportation methods');
                    }
                });
            }

            // Function to set selected value
            function setSelectedValue() {
                @if (isset($vehicle))
                    const selectedValue = @json(old('transportion_method_id', $vehicle->transportion_method_id ?? null));
                @else
                    const selectedValue = @json(old('transportion_method_id'));
                @endif

                if (selectedValue) {
                    transportationSelect.val(selectedValue).trigger('change');
                }
            }

            // Handle company selection change
            companySelect.on('change', function() {
                loadTransportationMethods($(this).val(), setSelectedValue);
            });

            // Initial load if company is selected
            const initialCompanyId = companySelect.val();
            if (initialCompanyId) {
                loadTransportationMethods(initialCompanyId, setSelectedValue);
            }
        });
    </script>
@endpush
