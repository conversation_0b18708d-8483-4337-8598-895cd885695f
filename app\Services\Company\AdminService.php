<?php

namespace App\Services\Company;

use App\Models\CompanyAdmin;
use App\Repositories\CompanyAdminRepository;
use App\Services\MediaService;
use Illuminate\Support\Facades\DB;

class AdminService
{
    public function __construct(
        private readonly CompanyAdminRepository $companyAdminRepository,
        private readonly MediaService $mediaService
    ) {}

    public function getAdmin(CompanyAdmin $admin): CompanyAdmin
    {
        if (
            $admin->id == $admin->company->superAdmin->id ||
            $admin->id == auth('company')->id() ||
            $admin->company_id != auth('company')->user()->company_id
        ) {
            abort(404);
        }

        return $admin;
    }

    public function create(): void
    {
        $data = request()->all();

        $data['company_id'] = auth('company')->user()->company_id;
        $data['phone'] = normalizePhoneNumber($data['phone']);
        $data['password'] = bcrypt(request('password'));

        DB::transaction(function () use ($data): void {
            $admin = $this->companyAdminRepository->create($data);

            if (request('image')) {
                $this->mediaService->save($admin, request('image'), 'company_admins');
            }

            $admin->permissions()->attach(request('permissions'));
        });
    }

    public function update(CompanyAdmin $admin): void
    {
        if (
            $admin->id == $admin->company->superAdmin->id ||
            $admin->id == auth('company')->id() ||
            $admin->company_id != auth('company')->user()->company_id
        ) {
            abort(404);
        }

        $data = request()->all();

        $data['phone'] = normalizePhoneNumber($data['phone']);
        $data['status'] = request('status') ? 'active' : 'inactive';
        $data['password'] = request('password') ? bcrypt(request('password')) : $admin->password;

        DB::transaction(function () use ($admin, $data): void {
            $this->companyAdminRepository->update($admin, $data);

            if (request('image')) {
                $this->mediaService->delete($admin->image);
                $this->mediaService->save($admin, request('image'), 'company_admins');
            }

            $admin->permissions()->sync(request('permissions'));
        });
    }

    public function delete(CompanyAdmin $admin): void
    {
        if (
            $admin->id == $admin->company->superAdmin->id ||
            $admin->id == auth('company')->id() ||
            $admin->company_id != auth('company')->user()->company_id
        ) {
            abort(404);
        }

        DB::transaction(function () use ($admin): void {
            $admin->delete();
            $this->companyAdminRepository->invalidateUniqueData($admin);
        });
    }

    public function updateProfile(): void
    {
        $admin = auth('company')->user();
        $data = request()->all();

        $data['phone'] = normalizePhoneNumber($data['phone']);
        $data['password'] = request('password') ? bcrypt(request('password')) : $admin->password;

        DB::transaction(function () use ($admin, $data): void {
            $this->companyAdminRepository->update($admin, $data);

            if (request('image')) {
                $this->mediaService->delete($admin->image);
                $this->mediaService->save($admin, request('image'), 'admins');
            }
        });
    }
}
