<?php

use App\Enum\OrderStatus;
use App\Models\Chat;
use App\Services\User\OrderService;
use Illuminate\Support\Facades\Broadcast;

Broadcast::routes([
    'middleware' => ['auth:sanctum'],
]);

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('order.{driverId}', function ($user, $driverId) {
    return $user->id == $driverId;
}, ['guards' => ['web', 'sanctum']]);

Broadcast::channel('order.drivers.list.{orderId}', function ($user, $orderId) {
    return $user->orders->contains($orderId);
}, ['guards' => ['web', 'sanctum']]);

Broadcast::channel('user.order.status.{orderId}', function ($user, $orderId) {
    return $user->orders()->where('id', $orderId)->exists();
}, ['guards' => ['web', 'sanctum']]);

Broadcast::channel('user.order.item.status.{orderItemId}', function ($user, $orderItemId) {
    return $user->orders()->whereHas('items', function ($query) use ($orderItemId) {
        $query->where('id', $orderItemId);
    })->exists();
}, ['guards' => ['web', 'sanctum']]);

Broadcast::channel('chats.{chatId}', function ($user, $chatId) {

    $chatable = Chat::find($chatId)->chatable;

    // check if auth user belongs to this chat of order or order item
    if (auth('user')->check() && ! app(OrderService::class)->userCanActOnOrder($chatable, $user)) {
        return false;
    }

    // check if auth driver belongs to this chat of order or order item
    if (auth('driver')->check() && ! app(OrderService::class)->driverCanActOnOrder($chatable, $user)) {
        return false;
    }

    return true;
}, ['guards' => ['sanctum']]);

Broadcast::channel('driver.location.updated.{driver_id}', function ($user, $driver_id) {
    $user->orders()
        ->where('driver_id', $driver_id)
        ->whereNotIn('status', [
            OrderStatus::DELIVERED->value,
            OrderStatus::CANCELLED->value,
            OrderStatus::DELIVERED_TO_LOCAL_HUB->value,
            OrderStatus::PARTIALLY_DELIVERED->value,
        ])
        ->exists();
});
