<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DriverShippingTypeTransportMethods extends Model
{
    public $timestamps = false;

    protected $fillable = [
        'driver_shipping_type_id',
        'transportion_method_id',
    ];

    public function transportionMethod()
    {
        return $this->belongsTo(TransportionMethod::class, 'transportion_method_id');
    }

    public function driverShippingType()
    {
        return $this->belongsTo(DriverShippingType::class, 'driver_shipping_type_id');
    }
}
