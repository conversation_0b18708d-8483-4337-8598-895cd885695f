<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\AddressRequest;
use App\Http\Resources\User\AddressResource;
use App\Models\City;
use App\Models\Country;
use App\Repositories\AddressRepository;
use App\Services\User\AddressService;

class AddressController extends Controller
{
    public function __construct(protected AddressService $addressService, private AddressRepository $addressRepository) {}

    public function index()
    {
        $addresses = $this->addressRepository->getUserAddresses(auth('user')->user());
        
        return AddressResource::collection($addresses);
    }

    public function store(AddressRequest $request)
    {
        $this->addressService->create($request->validated());

        return success(true);
    }

    public function show(string $id)
    {
        $address = $this->addressService->get($id);

        return success(AddressResource::make($address));
    }

    public function update(AddressRequest $request, string $id)
    {
        $this->addressService->update($id, $request->validated());

        return success(true);
    }

    public function destroy(string $id)
    {
        $this->addressService->delete($id);

        return success(true);
    }

    public function getByCountry(Country $country)
    {
        $addresses = $this->addressRepository->getUserAddressesByCountry(auth('user')->user(), $country);
        
        return AddressResource::collection($addresses);
    }

    public function getByCity(City $city)
    {
        $addresses = $this->addressRepository->getUserAddressesByCity(auth('user')->user(), $city);
        
        return AddressResource::collection($addresses);
    }
}
