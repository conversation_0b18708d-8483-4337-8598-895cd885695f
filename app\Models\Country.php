<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

/**
 * Class Country
 *
 * Represents a country entity with translatable name and metadata.
 *
 * @property int $id
 * @property array|string $name
 * @property string|null $code
 * @property string|null $abbv
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read string $flag
 */
class Country extends Model
{
    use HasFactory, HasTranslations, SoftDeletes;

    /**
     * Attributes that are translatable.
     *
     * @var array<int, string>
     */
    public array $translatable = ['name'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'abbv',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the country flag URL.
     */
    public function getFlagAttribute(): string
    {
        $code = strtolower($this->abbv);

        return url("https://flagcdn.com/w80/$code.png");
    }
}
