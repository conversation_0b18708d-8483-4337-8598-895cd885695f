<?php

namespace App\Services\Driver;

use App\Models\Driver;
use App\Repositories\DriverRepository;
use App\Repositories\OTPRepository;
use Carbon\Carbon;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class ProfileService
{
    private const OTP_REQUEST_KEY = 'otp-request-driver';

    private const CONFIRM_OTP_USER_KEY = 'confirm-otp-user';

    private const RETRY_LIMIT = 3;

    private const OTP_EXPIRY_DURATION = 3600;

    private const OTP_RATE_LIMIT_TTL = 43200;

    private const ERROR_MESSAGE_LIMIT_REACHED = 'limit reached: retry after :time hours';

    private const ERROR_MESSAGE_TOO_MANY_ATTEMPTS = 'too many attempts: retry after :time minutes';

    private const ERROR_MESSAGE_INVALID_CODE = 'invalid code';

    private const ERROR_MESSAGE_LATEST_CODE = 'please enter the latest sent code';

    private const ERROR_MESSAGE_EXPIRED_CODE = 'expired code';

    public function __construct(
        private readonly DriverRepository $driverRepository,
        private readonly OTPRepository $otpRepository
    ) {
        //
    }

    private function getAuthenticatedDriver(): Driver
    {
        return auth('driver')->user();
    }

    public function update(array $data): void
    {
        $driver = $this->getAuthenticatedDriver();
        $this->driverRepository->update($driver, $data);
    }

    public function sendUpdatePhoneOtp(): void
    {
        $countryCode = request('country_code');
        $phone = normalizeMobileNumber(request('phone'));

        $key = sprintf('%s:%s-%s', self::OTP_REQUEST_KEY, $countryCode, $phone);

        if (RateLimiter::tooManyAttempts($key, self::RETRY_LIMIT)) {
            $seconds = RateLimiter::availableIn($key);
            $remainingTime = formatSecondsToHoursTime($seconds);

            throw new ThrottleRequestsException(__(self::ERROR_MESSAGE_LIMIT_REACHED, [
                'time' => $remainingTime,
            ]));
        }

        RateLimiter::hit($key, self::OTP_RATE_LIMIT_TTL);

        $code = generatePassCode();

        $this->otpRepository->create(Driver::class, $countryCode, $phone, $code);
    }

    public function confirmUpdatePhone(array $data): void
    {
        $driver = $this->getAuthenticatedDriver();

        $phone = normalizeMobileNumber(request('phone'));
        $countryCode = request('country_code');
        $code = request('code');

        $key = sprintf('%s:%s-%s', self::CONFIRM_OTP_USER_KEY, $countryCode, $phone);

        if (RateLimiter::tooManyAttempts($key, self::RETRY_LIMIT)) {
            $seconds = RateLimiter::availableIn($key);
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw new ThrottleRequestsException(__(self::ERROR_MESSAGE_TOO_MANY_ATTEMPTS, [
                'time' => $remainingTime,
            ]));
        }

        RateLimiter::hit($key, 60);

        $otp = $this->otpRepository->get(Driver::class, $countryCode, $phone, $code);

        if (! $otp) {
            throw new BadRequestHttpException(__(self::ERROR_MESSAGE_INVALID_CODE));
        }

        $latestOtp = $this->otpRepository->getLatest(Driver::class, $countryCode, $phone);

        if ($latestOtp->code !== $code) {
            throw new BadRequestHttpException(__(self::ERROR_MESSAGE_LATEST_CODE));
        }

        if (Carbon::parse($latestOtp->created_at)->addSeconds(self::OTP_EXPIRY_DURATION)->lt(now())) {
            throw new BadRequestHttpException(__(self::ERROR_MESSAGE_EXPIRED_CODE));
        }

        $this->driverRepository->update($driver, $data);
    }

    public function delete(): void
    {
        $driver = $this->getAuthenticatedDriver();
        $this->driverRepository->delete($driver);
    }

    public function updateAppLocale(): void
    {
        $driver = $this->getAuthenticatedDriver();
        $this->driverRepository->update($driver, [
            'app_locale' => request('app_locale'),
        ]);
    }

    public function updatePushNotificationStatus(): bool
    {
        $driver = $this->getAuthenticatedDriver();
        $this->driverRepository->update($driver, [
            'push_notifications_enabled' => ! $driver->push_notifications_enabled,
        ]);

        return $driver->push_notifications_enabled;
    }
}
