<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class TranslatedTextInput extends Component
{
    public function __construct(public string $name, public string $label, public ?object $model = null) {}

    public function locales(): array
    {
        return config('app.locales', ['en', 'ar']);
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.translated-text-input');
    }
}
