<?php

namespace App\Http\Requests\Company;

use App\Models\Company;
use App\Models\CompanyAdmin;
use App\Rules\UniquePhone;
use App\Rules\ValidPhone;
use Illuminate\Foundation\Http\FormRequest;

class RegistrationStepperRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['nullable', 'string', 'min:5', 'max:320', 'unique:companies,name'],
            'country_code' => ['required_with:phone', 'string', 'exists:countries,code'],
            'phone' => ['required_with:country_code', 'string', new ValidPhone($this->country_code), new UniquePhone($this->country_code, Company::class)],
            'business_registration_number' => ['required', 'string', 'min:8', 'max:20', 'unique:companies,business_registration_number'],
            'admin_email' => ['nullable', 'string', 'email:rfc,dns', 'max:255', 'unique:company_admins,email'],
            'admin_country_code' => ['required_with:admin_phone', 'string', 'exists:countries,code'],
            'admin_phone' => ['required_with:admin_country_code', 'string', new ValidPhone($this->admin_country_code), new UniquePhone($this->admin_country_code, CompanyAdmin::class)],
        ];
    }
}
