<?php

namespace App\Http\Resources\User;

use App\Services\GeometryService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type,
            'country_code' => $this->country_code,
            'phone' => $this->phone,
            'street' => $this->street,
            'building' => $this->building,
            'floor' => $this->floor,
            'country' => $this->area->city->country->name,
            'city' => $this->area->city->name,
            'area' => $this->area->name,
            'location' => $this->when($this->location, fn(): array => [
                'lat' => (string) $this->location->latitude,
                'lng' => (string) $this->location->longitude,
            ]),
            'polygon' => app(GeometryService::class)->getPolygonPoints($this->area->area),

        ];
    }
}
