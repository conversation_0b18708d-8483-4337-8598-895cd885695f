@php
    $localeNames = [
        'en' => __('English'),
        'ar' => __('Arabic'),
        'ur' => __('Urdu'),
    ];
@endphp

@foreach ($locales() as $locale)
    <div class="col-{{ $size ?? 4 }}">
        <div class="form-group">
            <label>{{ $title ?? ($label ?? 'Field') }}
                {{ __('in :language', ['language' => $localeNames[$locale] ?? ucfirst($locale)]) }}</label>
            <input type="text" name="{{ $name }}[{{ $locale }}]"
                value="{{ old($name . '.' . $locale, $model?->getTranslation($name, $locale, false) ?? '') }}"
                class="form-control"
                placeholder="{{ $title ?? ($label ?? 'Field') }} {{ __('in :language', ['language' => $localeNames[$locale] ?? ucfirst($locale)]) }}">
            <p class="help-block"></p>
            @error($name . '.' . $locale)
                <span style="color: red">{{ $message }}</span>
            @enderror
        </div>
    </div>
@endforeach
