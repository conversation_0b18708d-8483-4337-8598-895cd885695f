<?php

namespace App\Repositories;

use App\Models\DriverImmediateShippingCity;
use App\Models\DriverImmediateShippingCityArea;

class DriverImmediateShippingCityAreaRepository
{
    public function __construct(private readonly DriverImmediateShippingCityArea $model) {}

    public function create(DriverImmediateShippingCity $city, string $area_id)
    {
        return $this->model->create([
            'driver_city_id' => $city->id,
            'area_id' => $area_id,
        ]);
    }
}
