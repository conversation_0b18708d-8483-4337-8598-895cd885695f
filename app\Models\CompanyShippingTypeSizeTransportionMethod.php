<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompanyShippingTypeSizeTransportionMethod extends Model
{
    public $timestamps = false;

    protected $fillable = [
        'company_shipping_type_id',
        'company_shipping_type_size_id',
        'transportion_method_id',
    ];

    public function transportionMethod()
    {
        return $this->belongsTo(TransportionMethod::class);
    }

    public function companyShippingTypeSize()
    {
        return $this->belongsTo(CompanyShippingTypeSize::class);
    }
}
