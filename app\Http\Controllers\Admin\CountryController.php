<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\CountriesDataTable;
use App\DTO\Admin\CountryDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Country\StoreRequest;
use App\Models\Country;
use App\Services\Admin\CountryService;

class CountryController extends Controller
{
    public function __construct(private readonly CountryService $countryService)
    {
        //
    }

    public function index(CountriesDataTable $dataTable)
    {
        return $dataTable->render('pages.admin.countries.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('pages.admin.countries.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRequest $request)
    {
        $dto = CountryDTO::from($request->validated());
        $this->countryService->create($dto);

        return to_route('admin.countries.index')->with('success', __('Country created successfully'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Country $country)
    {
        return view('pages.admin.countries.show', ['country' => $country]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Country $country)
    {
        return view('pages.admin.countries.edit', ['country' => $country]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(StoreRequest $request, Country $country)
    {
        $dto = CountryDTO::from($request->validated());
        $this->countryService->update($country, $dto);

        return to_route('admin.countries.index')->with('success', __('Country updated successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Country $country)
    {
        return $this->countryService->delete($country);
    }
}
