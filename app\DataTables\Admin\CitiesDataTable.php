<?php

namespace App\DataTables\Admin;

use App\Models\City;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class CitiesDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param  QueryBuilder<City>  $query  Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->setRowId('id')
            ->addColumn('name', fn ($city) => $city->name)
            ->addColumn('country', fn ($city) => $city->country?->name ?? '-')
            ->addColumn('status', fn ($admin): ?string => statusBadge($admin->status))
            ->addColumn('actions', 'pages.admin.cities.actions')
            ->orderColumn('DT_RowIndex', function ($query, $direction): void {
                $query->orderBy('id', $direction);
            })
            ->addIndexColumn()
            ->rawColumns(['actions', 'status']);
    }

    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<City>
     */
    public function query(City $model): QueryBuilder
    {
        $locales = config('app.locales', ['en', 'ar']);
        $search = request('search_param');

        return $model->newQuery()
            ->with(['country'])
            ->when($search, function ($query) use ($locales, $search): void {
                $query->where(function ($query) use ($locales, $search): void {
                    foreach ($locales as $locale) {
                        $query->orWhereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(name, \"$.{$locale}\"))) like LOWER(?)", ["%$search%"]);
                    }
                });
            })
            ->when(request('status'), fn ($q) => $q->where('status', request('status')));
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('cities-table')
            ->columns($this->getColumns())
            ->orderBy(0, 'desc')
            ->minifiedAjax()
            ->searching(false)
            ->parameters([
                'stateSave' => true,
                'stateDuration' => 10,
                'language' => [
                    'url' => asset('assets/json/datatable-translations/'.app()->getLocale().'.json'),
                ],
            ]);
    }

    /**
     * Get the dataTable columns definition.
     */
    public function getColumns(): array
    {
        return [
            Column::make('id')->visible(false),
            Column::make('DT_RowIndex')->title('#')->addClass('text-center')->searchable(false),
            Column::computed('name')->title(__('Name'))->addClass('text-center'),
            Column::computed('country')->title(__('Country'))->addClass('text-center'),
            Column::computed('status')->title(__('Status'))->addClass('text-center'),
            Column::computed('actions')->title(__('Actions'))->addClass('text-center'),
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'Cities_'.date('YmdHis');
    }
}
