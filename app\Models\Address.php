<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use MatanYadaev\EloquentSpatial\Objects\Point;
use MatanYadaev\EloquentSpatial\Traits\HasSpatial;

/**
 * Class Address
 *
 * Represents a physical address associated with a user.
 *
 * @property int $id
 * @property string|null $name
 * @property int $user_id
 * @property string|null $type
 * @property string|null $street
 * @property string|null $building
 * @property string|null $floor
 * @property int|null $country_id
 * @property int|null $city_id
 * @property int|null $area_id
 * @property Point|null $location
 * @property string|null $country_code
 * @property string|null $phone
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\User $user
 * @property-read \App\Models\Country $country
 * @property-read \App\Models\City $city
 * @property-read \App\Models\Area $area
 */
class Address extends Model
{
    use HasSpatial, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'user_id',
        'type',
        'street',
        'building',
        'floor',
        'area_id',
        'location',
        'country_code',
        'phone',
    ];

    /**
     * The attributes that should be cast to specific types.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'location' => Point::class,
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the user that owns the address.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the area associated with the address.
     */
    public function area(): BelongsTo
    {
        return $this->belongsTo(Area::class)->withTrashed();
    }
}
