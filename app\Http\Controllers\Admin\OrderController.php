<?php

namespace App\Http\Controllers\Admin;

use Mpdf\Mpdf;
use App\Models\Order;
use App\Http\Controllers\Controller;
use App\Services\Admin\OrderService;
use App\DataTables\Admin\OrdersDataTable;
use App\Repositories\ShippingSizeRepository;
use App\Repositories\ShippingTypeRepository;
use App\Http\Requests\Admin\Order\CancelRequest;

class OrderController extends Controller
{
    public function __construct(
        private ShippingTypeRepository $shippingType,
        private ShippingSizeRepository $shippingSizeRepository,
        private OrderService $orderService
    ) {
        //
    }

    /**
     * Display a listing of the resource.
     */
    public function index(OrdersDataTable $dataTable)
    {
        $shippingTypes = $this->shippingType->getAll();
        $shippingSizes = $this->shippingSizeRepository->getAll();

        return $dataTable->render('pages.admin.orders.index', ['shippingTypes' => $shippingTypes, 'shippingSizes' => $shippingSizes]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Order $order)
    {
        $order->load([
            'user',
            'driver',
            'vehicle',
            'shippingType',
            'shippingSize',
            'items',
            'pickupAddress.area.city',
            'pickupAddress.area',
            'pickupCountry',
            'items.dropoffAddress.area.city',
            'items.dropoffAddress.area',
            'items.dropoffCountry',
            'items.media',
        ]);

        return view('pages.admin.orders.show', ['order' => $order]);
    }

    /**
     * Cancel Order.
     */
    public function cancel(CancelRequest $request, Order $order)
    {
        return $this->orderService->cancel($order, $request->validated('cancel_reason'));
    }

    /**
     * Export PDF.
     */
    public function exportPdf(OrdersDataTable $dataTable)
    {
        dd('here');
        // Reuse the same query with applied filters
        $query = $dataTable->query(new Order);

        $orders = $query->get();

        $html = view('pages.admin.orders.pdf', compact('orders'))->render();

        $mpdf = new Mpdf(['mode' => 'utf-8', 'format' => 'A4-L']);
        $mpdf->WriteHTML($html);

        return $mpdf->Output('orders.pdf', 'I');
    }
}
