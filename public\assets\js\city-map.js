let map;
let drawingManager;
let cityPolygon = null;
let polygonCoordinates = [];
let searchBox;

function initMap() {
    const center = { lat: 23.4241, lng: 53.8478 };
    map = new google.maps.Map(document.getElementById('city-map'), {
        zoom: 8,
        center: center,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        mapTypeControl: true,
        streetViewControl: false,
        fullscreenControl: true,
        zoomControl: true
    });

    // Search Box Setup
    const input = document.getElementById('map-search-input');
    searchBox = new google.maps.places.SearchBox(input);
    map.controls[google.maps.ControlPosition.TOP_CENTER].push(input);
    map.addListener('bounds_changed', () => {
        searchBox.setBounds(map.getBounds());
    });
    searchBox.addListener('places_changed', () => {
        const places = searchBox.getPlaces();
        if (places.length === 0) return;
        const bounds = new google.maps.LatLngBounds();
        places.forEach(place => {
            if (!place.geometry || !place.geometry.location) return;
            map.setCenter(place.geometry.location);
            map.setZoom(14);
            if (place.geometry.viewport) {
                bounds.union(place.geometry.viewport);
            } else {
                bounds.extend(place.geometry.location);
            }
        });
        map.fitBounds(bounds);
    });

    // Drawing Manager Setup
    drawingManager = new google.maps.drawing.DrawingManager({
        drawingMode: null,
        drawingControl: false,
        polygonOptions: {
            fillColor: '#4099FF',
            fillOpacity: 0.3,
            strokeWeight: 2,
            strokeColor: '#4099FF',
            clickable: true,
            editable: true,
            zIndex: 1
        }
    });
    drawingManager.setMap(map);

    // Polygon Drawing Handler
    google.maps.event.addListener(drawingManager, 'polygoncomplete', function (polygon) {
        if (cityPolygon) {
            cityPolygon.setMap(null);
        }
        cityPolygon = polygon;
        updatePolygonCoordinates(polygon);
        attachPolygonListeners(polygon);
        drawingManager.setDrawingMode(null);
    });

    // Restore Saved Polygon if Available
    const savedCoordinates = document.getElementById('area-coordinates-json').value;
    // Location will be automatically set as the center of the polygon

    // Restore saved area polygon if available
    if (savedCoordinates && savedCoordinates.trim() !== '' && savedCoordinates !== '[]') {
        try {
            let coords;

            try {
                coords = typeof savedCoordinates === 'string' ? JSON.parse(savedCoordinates) : savedCoordinates;
            } catch (parseError) {
                // Try to clean the string and parse again
                const cleanedStr = savedCoordinates.replace(/\\"/g, '"').replace(/^"/, '').replace(/"$/, '');
                try {
                    coords = JSON.parse(cleanedStr);
                } catch (e) {
                    console.error('Failed to parse cleaned coordinates:', e);
                    return;
                }
            }

            if (coords) {
                console.log('Loaded coordinates:', coords);
                drawSavedPolygon(coords);
            }
        } catch (e) {
            console.error('Invalid saved coordinates:', e);
        }
    }
}

function updatePolygonCoordinates(polygon) {
    const vertices = polygon.getPath();
    polygonCoordinates = [];
    const namedCoordinates = {};
    let sumLat = 0;
    let sumLng = 0;

    for (let i = 0; i < vertices.getLength(); i++) {
        const xy = vertices.getAt(i);
        const point = [xy.lng(), xy.lat()];
        polygonCoordinates.push(point);

        // Sum up coordinates for calculating center
        sumLat += xy.lat();
        sumLng += xy.lng();

        // Add named checkout point
        namedCoordinates['checkout' + (i + 1)] = {
            lat: xy.lat(),
            lng: xy.lng()
        };
    }

    if (polygonCoordinates.length > 0) {
        polygonCoordinates.push([polygonCoordinates[0][0], polygonCoordinates[0][1]]);

        // Add the first point again as the last point to close the polygon
        const lastIndex = polygonCoordinates.length;
        namedCoordinates['checkout' + lastIndex] = {
            lat: polygonCoordinates[0][1],
            lng: polygonCoordinates[0][0]
        };

        // Calculate and set the center point as location
        const centerLat = sumLat / vertices.getLength();
        const centerLng = sumLng / vertices.getLength();

        // Update location inputs with center coordinates
        document.getElementById('location-lng').value = centerLng;
        document.getElementById('location-lat').value = centerLat;
    }

    // Store both formats - array format for backward compatibility and named format for new usage
    document.getElementById('area-coordinates-json').value = JSON.stringify({
        coordinates: [polygonCoordinates],
        points: namedCoordinates
    });

    const container = document.getElementById('coordinates-container');
    container.innerHTML = '';
    polygonCoordinates.forEach((point, i) => {
        const lngInput = document.createElement('input');
        lngInput.type = 'hidden';
        lngInput.name = `area[coordinates][0][${i}][]`;
        lngInput.value = point[0];
        container.appendChild(lngInput);

        const latInput = document.createElement('input');
        latInput.type = 'hidden';
        latInput.name = `area[coordinates][0][${i}][]`;
        latInput.value = point[1];
        container.appendChild(latInput);

        // Add named points inputs
        if (i < polygonCoordinates.length - 1) { // Skip the duplicated closing point
            const checkoutName = 'checkout' + (i + 1);

            const nameInput = document.createElement('input');
            nameInput.type = 'hidden';
            nameInput.name = `area[points][${checkoutName}][name]`;
            nameInput.value = checkoutName;
            container.appendChild(nameInput);

            const checkoutLatInput = document.createElement('input');
            checkoutLatInput.type = 'hidden';
            checkoutLatInput.name = `area[points][${checkoutName}][lat]`;
            checkoutLatInput.value = point[1];
            container.appendChild(checkoutLatInput);

            const checkoutLngInput = document.createElement('input');
            checkoutLngInput.type = 'hidden';
            checkoutLngInput.name = `area[points][${checkoutName}][lng]`;
            checkoutLngInput.value = point[0];
            container.appendChild(checkoutLngInput);
        }
    });
}

function drawSavedPolygon(coordinates) {
    // Handle different coordinate formats
    let coordsArray;
    console.log('Drawing saved polygon with coordinates:', coordinates);

    // Check if we have the new format with points property
    if (coordinates && typeof coordinates === 'object' && coordinates.coordinates) {
        // New format with both coordinates and points
        if (Array.isArray(coordinates.coordinates) && coordinates.coordinates.length > 0) {
            if (Array.isArray(coordinates.coordinates[0]) && coordinates.coordinates[0].length >= 3) {
                coordsArray = coordinates.coordinates[0];
            } else if (Array.isArray(coordinates.coordinates) && coordinates.coordinates.length >= 3) {
                coordsArray = coordinates.coordinates;
            }
        }
    } else if (Array.isArray(coordinates) && coordinates.length > 0) {
        // Handle the format from City model: [[[[lng, lat], [lng, lat], ...]]]
        if (Array.isArray(coordinates[0]) && Array.isArray(coordinates[0][0]) && coordinates[0].length >= 3) {
            coordsArray = coordinates[0];
        }
        // Handle format: [[[lng, lat], [lng, lat], ...]]
        else if (Array.isArray(coordinates[0]) && Array.isArray(coordinates[0][0]) && coordinates[0][0].length >= 2) {
            coordsArray = coordinates[0];
        }
        // Old format: direct array [[lng, lat], [lng, lat], ...]
        else if (Array.isArray(coordinates[0]) && coordinates[0].length >= 2) {
            coordsArray = coordinates;
        }
    }

    // If we couldn't parse the coordinates in any format
    if (!coordsArray) {
        console.error('Invalid coordinates structure or not enough points:', coordinates);
        return;
    }

    console.log('Parsed coordinates array:', coordsArray);

    const path = coordsArray.map(coord => {
        if (Array.isArray(coord) && coord.length >= 2) {
            return { lat: parseFloat(coord[1]), lng: parseFloat(coord[0]) };
        } else if (coord && typeof coord === 'object' && 'lat' in coord && 'lng' in coord) {
            return { lat: parseFloat(coord.lat), lng: parseFloat(coord.lng) };
        } else {
            console.error('Invalid coordinate format:', coord);
            return null;
        }
    }).filter(point => point !== null);

    if (path.length < 3) {
        console.error('Not enough valid points to create a polygon. Path:', path);
        return;
    }

    // Remove closing point if duplicate
    if (path.length > 1 &&
        path[0].lat === path[path.length - 1].lat &&
        path[0].lng === path[path.length - 1].lng) {
        console.log('Removing duplicate closing point');
        path.pop();
    }

    cityPolygon = new google.maps.Polygon({
        paths: path,
        fillColor: '#4099FF',
        fillOpacity: 0.3,
        strokeWeight: 2,
        strokeColor: '#4099FF',
        editable: true
    });

    cityPolygon.setMap(map);
    attachPolygonListeners(cityPolygon);

    const bounds = new google.maps.LatLngBounds();
    path.forEach(point => bounds.extend(point));
    map.fitBounds(bounds);
    console.log('Fitted bounds to polygon with path:', path);

    updatePolygonCoordinates(cityPolygon);
}

function attachPolygonListeners(polygon) {
    google.maps.event.addListener(polygon.getPath(), 'set_at', () => updatePolygonCoordinates(polygon));
    google.maps.event.addListener(polygon.getPath(), 'insert_at', () => updatePolygonCoordinates(polygon));
}

function initMapControls() {
    document.getElementById('draw-polygon').addEventListener('click', () => {
        if (cityPolygon) {
            cityPolygon.setMap(null);
            cityPolygon = null;
        }
        drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
    });

    document.getElementById('clear-polygon').addEventListener('click', () => {
        if (cityPolygon) {
            cityPolygon.setMap(null);
            cityPolygon = null;
        }
        document.getElementById('area-coordinates-json').value = '';
        document.getElementById('coordinates-container').innerHTML = '';
        polygonCoordinates = [];
    });
}

// Helper function to debug coordinate formats
function debugCoordinates() {
    const debugElement = document.getElementById('debug-coordinates');
    if (debugElement) {
        console.log('Debug coordinates from HTML:', debugElement.textContent);
        try {
            const coords = JSON.parse(debugElement.textContent);
            console.log('Parsed debug coordinates:', coords);
        } catch (e) {
            console.error('Failed to parse debug coordinates:', e);
        }
    }
}

document.addEventListener('DOMContentLoaded', () => {
    initMap();
    initMapControls();

    // Debug coordinates after a short delay to ensure DOM is fully loaded
    setTimeout(debugCoordinates, 500);
});
