<?php

namespace App\Console\Commands;

use App\Enum\OrderDriverActionStatus;
use App\Events\NewOrder;
use App\Models\OrderDriverAction;
use Illuminate\Console\Command;

class ResendNewOrderToDrivers extends Command
{
    protected $signature = 'orders:resend-new-order';

    protected $description = 'Resend order to drivers who have not responded within 30 seconds, with a limit of 1 resend.';

    public function handle(): int
    {
        $actions = OrderDriverAction::with('order', 'driver')
            ->whereIn('status', [OrderDriverActionStatus::PENDING->value, OrderDriverActionStatus::ACCEPTED->value])
            ->where('resend_attempts', '<', OrderDriverAction::MAX_RESEND_ATTEMPTS)
            ->where('created_at', '<=', now()->subSeconds(30))
            ->get();

        foreach ($actions as $action) {
            $order = $action->order;

            if (! $order) {
                $this->warn("Skipping: no order found for action ID {$action->id}");

                continue;
            }

            if ($order->driver_id) {
                $this->warn('Skipping: order already assigned to driver');

                continue;
            }

            broadcast(new NewOrder($order));

            // mark a resend attempt; keep status as pending for another chance
            $action->increment('resend_attempts');

            $this->info("Resent order #{$order->id} to driver #{$action->driver_id}");
        }

        return Command::SUCCESS;
    }
}
