<?php

namespace App\DataTables\Company;

use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class VehiclesDataTable extends DataTable
{
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->setRowId('id')
            ->addColumn('name', fn ($vehicle) => $vehicle->name)
            ->addColumn('transportion_method', fn ($vehicle) => $vehicle->transportionMethod->name)
            ->addColumn('approval_status', fn ($vehicle): ?string => statusBadge($vehicle->approval_status))
            ->addColumn('actions', fn ($vehicle) => view('pages.company.vehicles.actions', ['vehicle' => $vehicle])->render())
            ->addColumn('photo', fn ($vehicle): string => '<img src="'.$vehicle->photo?->url.'"width="60" height="60" style="border-radius: 8px;">')
            ->orderColumn('DT_RowIndex', function ($query, $direction): void {
                $query->orderBy('id', $direction);
            })
            ->rawColumns(['photo', 'approval_status', 'status', 'actions'])
            ->addIndexColumn();
    }

    public function query(Vehicle $model): QueryBuilder
    {
        return $model->newQuery()
            ->where('company_id', auth('company')->user()->company_id)
            ->when(request('search_param'), function ($query): void {
                $query->whereAny(['model', 'plate_number', 'license_number'], 'LIKE', '%'.request('search_param').'%');
            })
            ->when(request('approval_status'), fn ($q) => $q->where('approval_status', request('approval_status')))
            ->when(request('transportion_method_id'), function ($query): void {
                $query->where('transportion_method_id', request('transportion_method_id'));
            });
    }

    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('vehicles-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->searching(false)
            ->parameters([
                'language' => [
                    'url' => asset('assets/json/datatable-translations/'.app()->getLocale().'.json'),
                ],
            ]);
    }

    public function getColumns(): array
    {
        return [
            Column::make('DT_RowIndex')->title('#')->addClass('text-center')->searchable(false),
            Column::make('name')->title(__('Name'))->addClass('text-center')->orderable(false),
            Column::make('model')->title(__('Model'))->addClass('text-center')->orderable(false),
            Column::make('transportion_method')->title(__('Transportion Method'))->addClass('text-center')->orderable(false),
            Column::computed('photo')->title(__('Photo'))->addClass('text-center'),
            Column::computed('approval_status')->title(__('Approval Status'))->addClass('text-center'),
            Column::computed('actions')->title(__('Actions'))->addClass('text-center'),
        ];
    }

    protected function filename(): string
    {
        return 'Drivers_'.date('YmdHis');
    }
}
