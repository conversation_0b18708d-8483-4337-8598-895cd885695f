<?php

namespace App\Services\Driver;

use App\Enum\OrderItemStatus;
use App\Enum\OrderStatus;
use App\Events\OrderItemStatusUpdated;
use App\Events\OrderStatusUpdated;
use App\Models\Driver;
use App\Repositories\OrderRepository;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class OrderService
{
    public function __construct(
        private readonly OrderRepository $orderRepository,
        private readonly ExpressOrderStatusService $expressService
    ) {
        //
    }

    public function index(array $filters)
    {
        return $this->orderRepository->getDriverOrders($filters);
    }

    public function show(string $id)
    {
        $order = $this->orderRepository->findOrderForDriver($id);

        if (! $order) {
            throw new NotFoundHttpException(__('Order not found.'));
        }

        return $order;
    }

    /**
     * Update the status of an order
     *
     * @return mixed
     */
    public function updateStatus(string $id)
    {
        $order = $this->show($id);

        if ($order->is_express) {
            return $this->expressService->update($order);
        }

        $nextStatus = $this->determineNextStatus($order);
        $this->validateOrderForStatusUpdate($order, $nextStatus);

        $order->update(['status' => $nextStatus]);

        if ($nextStatus === OrderStatus::DELIVERED_TO_LOCAL_HUB) {
            $this->changeDriverAvailability($order->driver);
            $this->updateItemsToDeliveredStatus($order);
        }

        event(new OrderStatusUpdated($order));

        return $order;
    }

    /**
     * Validate if the order can be updated to next status
     *
     * @param  mixed  $order
     *
     * @throws BadRequestHttpException
     */
    private function validateOrderForStatusUpdate($order, OrderStatus $nextStatus): void
    {
        if ($nextStatus === OrderStatus::IN_TRANSIT && $order->items->contains(fn ($item): bool => $item->status === OrderItemStatus::NOT_YET_PICKED_UP->value)) {
            throw new BadRequestHttpException(__('One or more items have not been picked up yet. Please complete the pickup before proceeding.'));
        }
    }

    /**
     * Determine the next status based on current order status
     *
     * @param  mixed  $order
     *
     * @throws BadRequestHttpException
     */
    private function determineNextStatus($order): OrderStatus
    {
        $nextStatus = match ($order->status) {
            OrderStatus::READY_TO_PICKUP => OrderStatus::HEADING_TO_PICKUP,
            OrderStatus::HEADING_TO_PICKUP => OrderStatus::ARRIVED_AT_PICKUP,
            OrderStatus::PICKED_UP => OrderStatus::IN_TRANSIT,
            OrderStatus::IN_TRANSIT => OrderStatus::DELIVERED_TO_LOCAL_HUB,
            default => null,
        };

        if (! $nextStatus) {
            throw new BadRequestHttpException(__('Status cannot be updated at this stage.'));
        }

        return $nextStatus;
    }

    /**
     * Update items status to delivered when order reaches local hub
     *
     * @param  mixed  $order
     */
    private function updateItemsToDeliveredStatus($order): void
    {
        $order->items
            ->where('status', OrderItemStatus::PICKED_UP->value)
            ->each(function ($item): void {
                $item->update(['status' => OrderItemStatus::DELIVERED_TO_FIRST_HUB->value]);
                event(new OrderItemStatusUpdated($item));
            });
    }

    public function cancel(string $id, ?string $reason = null): \App\Models\Order
    {
        $order = $this->show($id);

        return $this->expressService->cancel($order, $reason);
    }

    public function deliveryFailed(string $id, ?string $reason = null): \App\Models\Order
    {
        $order = $this->show($id);

        if ($order->is_express) {
            return $this->expressService->markAsDeliveryFailed($order, $reason);
        }

        throw new BadRequestHttpException(__('Failed delivery is not supported for this order type.'));
    }

    private function changeDriverAvailability(Driver $driver): void
    {
        $driver->update(['is_available' => true]);
    }
}
