<?php

namespace App\Models;

use App\Enum\OrderDriverActionStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderDriverAction extends Model
{
    const MAX_RESEND_ATTEMPTS = 1;

    protected $fillable = [
        'order_id',
        'driver_id',
        'status',
        'accepted_at',
        'resend_attempts',
    ];

    protected $casts = [
        'status' => OrderDriverActionStatus::class,
        'accepted_at' => 'datetime',
        'resend_attempts' => 'integer',
    ];

    public function canResend(): bool
    {
        return $this->resend_attempts < self::MAX_RESEND_ATTEMPTS;
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }
}
