<?php

namespace App\Rules;

use App\Repositories\TransportionMethodRepository;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ActiveTransportionMethods implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $transportionMethodRepository = app(TransportionMethodRepository::class);

        $transportion_methods = $transportionMethodRepository->getByIds($value);

        foreach ($transportion_methods as $transportion_method) {
            if ($transportion_method->status == 'inactive') {
                $fail('The :attribute contains inactive transportion methods.');
            }
        }
    }
}
