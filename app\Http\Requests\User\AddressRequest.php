<?php

namespace App\Http\Requests\User;

use App\Rules\ValidPhone;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AddressRequest extends FormRequest
{
    public function rules(): array
    {
        $address = $this->route('address');

        return [
            'type' => ['required', 'in:company,house,office'],
            'country_code' => ['required', 'string', 'exists:countries,code'],
            'phone' => ['required', 'string', new ValidPhone($this->country_code)],
            'name' => ['required', 'string', 'min:3', 'max:255', Rule::unique('addresses', 'name')->ignore($address)],
            'street' => ['required', 'string', 'min:3', 'max:255'],
            'building' => ['required', 'string', 'max:50'],
            'floor' => ['nullable', 'string', 'min:1', 'max:50'],
            'lat' => ['required', 'numeric', 'between:-90,90', 'regex:/^-?\d+(\.\d+)?$/'],
            'lng' => ['required', 'numeric', 'between:-180,180', 'regex:/^-?\d+(\.\d+)?$/'],
            'area_id' => ['required', Rule::exists('areas', 'id')],
        ];
    }
}
