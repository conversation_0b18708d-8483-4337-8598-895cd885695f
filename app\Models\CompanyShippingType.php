<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CompanyShippingType extends Model
{
    protected $fillable = [
        'company_id',
        'shipping_type_id',
        'has_express_delivery',
    ];

    protected function casts(): array
    {
        return [
            'has_express_delivery' => 'boolean',
        ];
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function shippingType(): BelongsTo
    {
        return $this->belongsTo(ShippingType::class, 'shipping_type_id');
    }

    public function sizes()
    {
        return $this->hasMany(CompanyShippingTypeSize::class, 'company_shipping_type_id');
    }

    public function transportionMethods()
    {
        return $this->belongsToMany(TransportionMethod::class, 'company_shipping_type_size_transportion_methods', 'company_shipping_type_id', 'transportion_method_id');
    }
}
