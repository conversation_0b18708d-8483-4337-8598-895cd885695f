<?php

namespace App\Http\Controllers\Company;

use App\DataTables\Company\OrdersDataTable;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Repositories\ShippingSizeRepository;
use App\Repositories\ShippingTypeRepository;
use App\Services\Company\OrderService;

class OrderController extends Controller
{
    public function __construct(
        private readonly OrderService $orderService,
        private readonly ShippingTypeRepository $shippingType,
        private readonly ShippingSizeRepository $shippingSizeRepository
    ) {}

    public function index(OrdersDataTable $dataTable)
    {
        $shippingTypes = $this->shippingType->getAll();
        $shippingSizes = $this->shippingSizeRepository->getAll();

        return $dataTable->render('pages.company.orders.index', ['shippingTypes' => $shippingTypes, 'shippingSizes' => $shippingSizes]);
    }

    public function show(Order $order)
    {
        $order = $this->orderService->getOrder($order);

        return view('pages.company.orders.show', ['order' => $order]);
    }

    public function cancel(Order $order)
    {
        return success($this->orderService->cancel($order));
    }
}
