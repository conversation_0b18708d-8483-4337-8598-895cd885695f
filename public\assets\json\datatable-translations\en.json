{"loadingRecords": "Loading...", "lengthMenu": "Show _MENU_ entries", "zeroRecords": "No matching records found", "info": "Showing _START_ to _END_ of _TOTAL_ entries", "search": "Search:", "paginate": {"first": "First", "previous": "Previous", "next": "Next", "last": "Last"}, "aria": {"sortAscending": ": activate to sort column ascending", "sortDescending": ": activate to sort column descending"}, "select": {"rows": {"1": "1 row selected", "_": "%d rows selected"}, "cells": {"1": "1 cell selected", "_": "%d cells selected"}, "columns": {"1": "1 column selected", "_": "%d columns selected"}}, "buttons": {"print": "Print", "copyKeys": "Press <i>ctrl</i> or <i>⌘</i> + <i>C</i> to copy the table data<br><br>To cancel, click this message or press escape.", "pageLength": {"1": "Show 1 row", "-1": "Show all", "_": "Show %d rows"}, "collection": "Collection", "copy": "Copy", "copyTitle": "Copy to clipboard", "csv": "CSV", "excel": "Excel", "pdf": "PDF", "colvis": "Column visibility", "colvisRestore": "Restore visibility", "copySuccess": {"1": "Copied 1 row to clipboard", "_": "Copied %ds rows to clipboard"}, "createState": "Create state", "removeAllStates": "Remove all states", "removeState": "Remove", "renameState": "<PERSON><PERSON>", "savedStates": "Saved states", "stateRestore": "Restore state", "updateState": "Update"}, "searchBuilder": {"add": "Add condition", "clearAll": "Clear all", "condition": "Condition", "data": "Field", "logicAnd": "And", "logicOr": "Or", "value": "Value", "conditions": {"date": {"after": "After", "before": "Before", "between": "Between", "empty": "Empty", "equals": "Equals", "notBetween": "Not between", "notEmpty": "Not empty", "not": "Not"}, "number": {"between": "Between", "empty": "Empty", "equals": "Equals", "gt": "Greater than", "lt": "Less than", "not": "Not", "notBetween": "Not between", "notEmpty": "Not empty", "gte": "Greater than or equal", "lte": "Less than or equal"}, "string": {"not": "Not", "notEmpty": "Not empty", "startsWith": "Starts with", "contains": "Contains", "empty": "Empty", "endsWith": "Ends with", "equals": "Equals", "notContains": "Does not contain", "notStartsWith": "Does not start with", "notEndsWith": "Does not end with"}, "array": {"equals": "Equals", "empty": "Empty", "contains": "Contains", "not": "Not", "notEmpty": "Not empty", "without": "Without"}}, "button": {"0": "Search Filters", "_": "Search Filters (%d)"}, "deleteTitle": "Delete filters", "leftTitle": "Group conditions", "rightTitle": "Ungroup conditions", "title": {"0": "Advanced Search", "_": "Advanced Search (active)"}}, "searchPanes": {"clearMessage": "Clear all", "collapse": {"0": "Search", "_": "Search (%d)"}, "count": "Count", "countFiltered": "Filtered count", "loadMessage": "Loading...", "title": "Active filters", "showMessage": "Show all", "collapseMessage": "Collapse all", "emptyPanes": "No search panes available"}, "infoThousands": ",", "datetime": {"previous": "Previous", "next": "Next", "hours": "Hour", "minutes": "Minute", "seconds": "Second", "unknown": "-", "amPm": ["AM", "PM"], "weekdays": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"], "months": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]}, "editor": {"close": "Close", "create": {"button": "Add", "title": "Add New Record", "submit": "Submit"}, "edit": {"button": "Edit", "title": "Edit Record", "submit": "Update"}, "remove": {"button": "Delete", "title": "Delete", "submit": "Delete", "confirm": {"1": "Are you sure you want to delete this record?", "_": "Are you sure you want to delete %d records?"}}, "error": {"system": "A system error has occurred"}, "multi": {"title": "Multiple values", "restore": "Undo changes", "info": "Selected fields contain different values. To edit and set all fields to the same value, click or tap here, otherwise they will remain unchanged.", "noMulti": "This field can be edited individually, not in bulk"}}, "processing": "Processing...", "emptyTable": "No data available in table", "infoEmpty": "Showing 0 to 0 of 0 entries", "thousands": ",", "stateRestore": {"creationModal": {"columns": {"search": "Column search", "visible": "Column visibility"}, "toggleLabel": "Include", "button": "Create", "name": "State name", "order": "Sort", "paging": "Pagination", "scroller": "Scroll position", "search": "Search", "searchBuilder": "Search builder", "select": "Selection", "title": "Create new state"}, "duplicateError": "A state with this name already exists", "emptyError": "State name cannot be empty", "emptyStates": "No saved states", "removeConfirm": "Are you sure you want to remove %s?", "removeError": "Failed to remove state", "removeJoiner": "and", "removeSubmit": "Remove", "removeTitle": "Remove state", "renameButton": "<PERSON><PERSON>", "renameLabel": "New name for %s:", "renameTitle": "Rename state"}, "autoFill": {"cancel": "Cancel", "fill": "Fill all cells with <i>%d</i>", "fillHorizontal": "Fill cells horizontally", "fillVertical": "Fill cells vertically", "info": "AutoFill"}, "decimal": ".", "infoFiltered": "(filtered from _MAX_ total entries)", "searchPlaceholder": "Example search"}