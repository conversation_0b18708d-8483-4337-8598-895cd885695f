name: test-chatgpt-code-reviewer
run-name: test-chatgpt-code-reviewer
on: [ pull_request ]
permissions:
  contents: read
  pull-requests: write
  issues: write

jobs:
  chatgpt-code-reviewer:
    runs-on: ubuntu-latest
    steps:
      - name: ChatGPT Review
        uses: abdullahessam/chatgpt-code-reviewer@v1.0.0
        with:
          model: gpt-5-mini
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          CUSTOM_PROMPT: >-
            Act as a senior <PERSON><PERSON> engineer and review ONLY the PR diff.

            Report concise findings grouped by priority:

            🛑 CRITICAL (must fix): security (SQLi/XSS/CSRF/auth), validation/sanitization, authorization/policies, secret exposure.
            ⚠️ HIGH (should fix): logic bugs, N+1/slow queries, memory/resource issues, error/exception handling.

            If—and only if—no Critical/High exist, add up to 3:
            💡 MEDIUM: Lara<PERSON> best practices, structure, Eloquent/DB design, API consistency.

            Skip LOW/nits (style/naming/PSR) unless they clearly harm readability.

            Format:
            - Use section headings with emojis.
            - Max 5 bullets per section, 1–2 lines each.
            - Include tiny code excerpts only when essential (≤5 lines).
            - End with a brief ✅ Summary and next steps.

            Laravel focus hints: validation rules & FormRequests, mass assignment/hidden/casts, policies/gates/middleware, CSRF, rate limiting, eager loading, caching, queues/jobs, config/env usage.
