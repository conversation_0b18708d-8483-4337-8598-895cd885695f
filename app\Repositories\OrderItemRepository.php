<?php

namespace App\Repositories;

use App\Models\OrderItem;

class OrderItemRepository
{
    public function __construct(private readonly OrderItem $model)
    {
        //
    }

    public function findById(string $id)
    {
        return $this->model->find($id);
    }

    public function shipmentNumberExists(string $shipment_number)
    {
        return $this->model->where('shipment_number', $shipment_number)->exists();
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function findForDriver(string $id)
    {
        return $this->model->with(['order', 'dropoffAddress'])
            ->whereHas('order', fn ($q) => $q->forDriver())
            ->find($id);
    }
}
