<?php

namespace App\Repositories;

use App\Models\Address;
use App\Models\City;
use App\Models\Country;
use App\Models\User;

class AddressRepository
{
    public function __construct(private Address $model) {}

    public function getUserAddresses(User $user)
    {
        return $user->addresses()->with('area.city.country')->get();
    }

    public function getUserAddressesByCountry(User $user, Country $country)
    {
        return $user->addresses()
            ->whereHas('area.city.country', function ($q) use ($country) {
                $q->where('id', $country->id);
            })
            ->with('area.city.country')
            ->get();
    }

    public function getUserAddressesByCity(User $user, City $city)
    {
        return $user->addresses()
            ->whereHas('area.city', function ($q) use ($city) {
                $q->where('id', $city->id);
            })
            ->with('area.city.country')
            ->get();
    }

    public function find($id)
    {
        return $this->model->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update($address, array $data)
    {
        return $address->update($data);
    }
}
