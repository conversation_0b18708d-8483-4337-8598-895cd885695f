<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\UsersDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UserRequest;
use App\Models\User;
use App\Repositories\CountryRepository;
use App\Services\Admin\UserService;

class UserController extends Controller
{
    public function __construct(
        private readonly UserService $userService,
        private readonly CountryRepository $countryRepository,
    ) {}

    public function index(UsersDataTable $dataTable)
    {
        return $dataTable->render('pages.admin.users.index');
    }

    public function create()
    {
        $countries = $this->countryRepository->getAll();

        return view('pages.admin.users.create', ['countries' => $countries]);
    }

    public function store(UserRequest $request)
    {
        $this->userService->create();

        return to_route('admin.users.index')->with('success', __('Created successfully'));
    }

    public function show(User $user)
    {
        return view('pages.admin.users.show', ['user' => $user]);
    }

    public function edit(User $user)
    {
        $countries = $this->countryRepository->getAll();

        return view('pages.admin.users.edit', ['user' => $user, 'countries' => $countries]);
    }

    public function update(UserRequest $request, User $user)
    {
        $this->userService->update($user);

        return back()->with('success', __('Updated successfully'));
    }

    public function destroy(User $user)
    {
        $this->userService->delete($user);

        return to_route('admin.users.index')->with('success', __('Deleted successfully'));
    }
}
