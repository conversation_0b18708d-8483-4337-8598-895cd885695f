<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\CompaniesDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CompanyRequest;
use App\Models\Company;
use App\Repositories\CityRepository;
use App\Repositories\CountryRepository;
use App\Repositories\ShippingSizeRepository;
use App\Repositories\ShippingTypeRepository;
use App\Repositories\TransportionMethodRepository;
use App\Services\Admin\CompanyService;

class CompanyController extends Controller
{
    public function __construct(
        private readonly CompanyService $companyService,
        private readonly ShippingTypeRepository $shippingTypeRepository,
        private readonly ShippingSizeRepository $shippingSizeRepository,
        private readonly TransportionMethodRepository $transportionMethodRepository,
        private readonly CountryRepository $countryRepository,
        private readonly CityRepository $cityRepository
    ) {}

    public function index(CompaniesDataTable $dataTable)
    {
        return $dataTable->render('pages.admin.companies.index');
    }

    public function create()
    {
        $countries = $this->countryRepository->getAll();
        $cities = $this->cityRepository->getActiveCities(true);
        $shippingTypes = $this->shippingTypeRepository->getAll();
        $shippingSizes = $this->shippingSizeRepository->getAll();
        $transportionMethods = $this->transportionMethodRepository->getActiveMethods();

        return view('pages.admin.companies.create', ['countries' => $countries, 'cities' => $cities, 'shippingTypes' => $shippingTypes, 'shippingSizes' => $shippingSizes, 'transportionMethods' => $transportionMethods]);
    }

    public function store(CompanyRequest $request)
    {
        $this->companyService->create($request->all());

        return to_route('admin.companies.index')->with('success', __('Created successfully'));
    }

    public function show(Company $company)
    {
        $company = $this->companyService->getCompany($company);

        return view('pages.admin.companies.show', ['company' => $company]);
    }

    public function edit(Company $company)
    {
        $countries = $this->countryRepository->getAll();

        return view('pages.admin.companies.edit', ['company' => $company, 'countries' => $countries]);
    }

    public function update(CompanyRequest $request, Company $company)
    {
        $this->companyService->update($company);

        return back()->with('success', __('Updated successfully'));
    }

    public function destroy(Company $company)
    {
        $this->companyService->delete($company);

        return to_route('admin.companies.index')->with('success', __('Deleted successfully'));
    }

    /**
     * Get transportation methods associated with a company
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAssociatedTransportionMethods(Company $company)
    {
        $transportionMethodsIds = $company->shippingTypes
            ->flatMap->sizes
            ->flatMap->transportionMethods
            ->pluck('transportion_method_id')
            ->unique()
            ->toArray();

        $methods = $this->transportionMethodRepository->getByIds($transportionMethodsIds);

        return response()->json($methods);
    }
}
