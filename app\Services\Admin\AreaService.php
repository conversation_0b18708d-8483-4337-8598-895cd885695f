<?php

namespace App\Services\Admin;

use App\DTO\Admin\AreaDTO;
use App\Models\Area;
use App\Repositories\AreaRepository;
use Illuminate\Http\RedirectResponse;

class AreaService
{
    public function __construct(private readonly AreaRepository $areaRepository)
    {
        //
    }

    public function create(AreaDTO $dto): Area
    {
        return $this->areaRepository->create($dto);
    }

    public function update(Area $area, AreaDTO $dto): bool
    {
        return $this->areaRepository->update($area, $dto);
    }

    public function delete(Area $area): RedirectResponse
    {
        $relations = [
            'drivers' => __('Area has related drivers and cannot be deleted.'),
            'addresses' => __('Area has related addresses and cannot be deleted.'),
        ];

        foreach ($relations as $relation => $message) {
            if ($area->{$relation}()->exists()) {
                return redirect()->back()->with('fail', $message);
            }
        }

        $area->delete();

        return redirect()->back()->with('success', __('Area deleted successfully.'));
    }
}
