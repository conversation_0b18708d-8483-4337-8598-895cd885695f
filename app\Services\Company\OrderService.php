<?php

namespace App\Services\Company;

use App\Enum\OrderStatus;
use App\Models\Order;
use App\Repositories\DriverRepository;
use App\Repositories\OrderRepository;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class OrderService
{
    public function __construct(private readonly OrderRepository $orderRepository, private readonly DriverRepository $driverRepository) {}

    public function getOrder(Order $order): Order
    {
        if ($order->driver?->company_id != auth('company')->user()->company_id) {
            abort(404);
        }

        if (! $order->driver) {
            abort(404);
        }

        return $order;
    }

    public function cancel(Order $order): bool
    {
        if ($order->driver?->company_id != auth('company')->user()->company_id) {
            abort(404);
        }

        if (! $order->driver) {
            abort(404);
        }

        if (! in_array($order->status->value, [OrderStatus::READY_TO_PICKUP->value, OrderStatus::HEADING_TO_PICKUP->value])) {
            throw new BadRequestHttpException('Order cannot be cancelled');
        }

        DB::transaction(function () use ($order): void {
            $this->orderRepository->update($order, ['status' => OrderStatus::CANCELLED->value]);

            $this->driverRepository->update($order->driver, ['is_available' => true]);
        });

        return true;
    }
}
