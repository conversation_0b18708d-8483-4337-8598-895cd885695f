<?php

namespace App\Repositories;

use App\Models\OTP;

class OTPRepository
{
    public function __construct(private readonly OTP $model) {}

    public function create($user_type, $country_code, $phone, $code)
    {
        return $this->model->create([
            'user_type' => $user_type,
            'country_code' => $country_code,
            'phone' => $phone,
            'code' => $code,
        ]);
    }

    public function get($user_type, $country_code, $phone, $code)
    {
        return $this->model->where(['user_type' => $user_type, 'country_code' => $country_code, 'phone' => $phone, 'code' => $code])->first();
    }

    public function getLatest($user_type, $country_code, $phone)
    {
        return $this->model->where(['user_type' => $user_type, 'country_code' => $country_code, 'phone' => $phone])->latest()->first();
    }

    public function deleteAll($user_type, $country_code, $phone)
    {
        return $this->model->where(['user_type' => $user_type, 'country_code' => $country_code, 'phone' => $phone])->delete();
    }
}
