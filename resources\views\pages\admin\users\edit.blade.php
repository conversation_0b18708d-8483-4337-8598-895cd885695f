<x-layout :title="__('Edit User')">
    <x-session-message />
    <div class="card">
        <div class="card-header">{{ __('Edit User') }}: {{ $user->name }}</div>
        <div class="card-body">
            <form action="{{ route('admin.users.update', $user->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <div class="row">
                    <!-- Name -->
                    <div class="mb-3 col-lg-4">
                        <label for="name" class="form-label"><b>{{ __('Name') }}</b></label>
                        <input type="text" name="name" id="name" class="form-control" value="{{ old('name', $user->name) }}">
                        <x-input-error name="name" />
                    </div>
                    
                    <!-- Email -->
                    <div class="mb-3 col-lg-4">
                        <label for="email" class="form-label"><b>{{ __('Email') }}</b></label>
                        <input type="text" name="email" id="email" class="form-control" value="{{ old('email', $user->email) }}">
                        <x-input-error name="email" />
                    </div>
                </div>

                <div class="row">
                    <!-- Phone -->
                    <div class="mb-3 col-lg-4">
                        <label class="form-label d-block"><b>{{ __('Phone') }}</b></label>
                        <x-phone-input :countries="$countries" :country_code="old('country_code', $user->country_code)" :phone="old('phone', $user->phone)" />
                        <x-input-error name="phone" />
                    </div>
                </div>

                <div class="row">
                    <!-- Birth Date -->
                    <div class="mb-3 col-lg-4">
                        <label for="birth_date" class="form-label"><b>{{ __('Birth Date') }}</b></label>
                        <input type="date" name="birth_date" id="birth_date" class="form-control flatpickr-input" value="{{ $user->birth_date }}" readonly="readonly">
                        <x-input-error name="birth_date" />
                    </div>

                    <!-- Gender -->
                    <div class="mb-3 col-lg-4">
                        <label for="gender" class="form-label"><b>{{ __('Gender') }}</b></label>
                        <select name="gender" id="gender" class="select2 form-select form-select-lg" data-allow-clear="true">
                            <option disabled selected>{{ __("Choose") }}...</option>
                            <option value="male" @selected(old('gender', $user->gender) == 'male')>{{ __('Male') }}</option>
                            <option value="female" @selected(old('gender', $user->gender) == 'female')>{{ __('Female') }}</option>
                        </select>
                        <x-input-error name="gender" />
                    </div>
                </div>

                <div class="row">
                    <!-- Password -->
                    <div class="mb-3 col-lg-4">
                        <label for="password" class="form-label"><b>{{ __('Password') }}</b></label>
                        <input type="password" name="password" class="form-control" id="password" autocomplete="new-password">
                        <x-input-error name="password" />
                    </div>
                    <!-- Password Confirmation -->
                    <div class="mb-3 col-lg-4">
                        <label for="password_confirmation" class="form-label"><b>{{ __('Password Confirmation') }}</b></label>
                        <input type="password" name="password_confirmation" class="form-control" id="password_confirmation" autocomplete="new-password">
                        <x-input-error name="password_confirmation" />
                    </div>
                </div>

                <!-- Status -->
                <div class="row">
                    <div class="mb-3 col-lg-4">
                        <label style="margin-bottom:15px" class="form-label"><b>{{ __('Status') }}</b></label><br>
                        <label class="switch">
                            <input type="checkbox" class="switch-input" name="status" @checked($user->status == 'active')>
                            <span class="switch-toggle-slider">
                                <span class="switch-on"></span>
                                <span class="switch-off"></span>
                            </span>
                            <span class="switch-label">{{ __("active") }}</span>
                        </label>
                        <x-input-error name="status" />
                    </div>
                </div>
                
                <!-- Image -->
                <div class="row">
                    <div class="mb-4 col-lg-4">
                        <label for="formFile" class="form-label"><b>{{ __('Image') }}</b></label>
                        <input name="image" class="form-control" type="file" id="formFile">
                        <x-input-error name="image" />
                    </div>
                </div>
                
                <!-- Current Image -->
                @if($user->image)
                <div class="row">
                    <div class="mb-4 col-lg-4">
                        <label for="formFile" class="form-label"><b>{{ __('Current Image') }}</b></label>
                        <div class="mt-2">
                            <a href="{{ $user->image->url }}" target="_blank">
                                <img src="{{ $user->image->url }}" alt="Current Image" class="card-image">
                            </a>
                        </div>
                    </div>
                </div>
                @endif

                <button type="submit" class="btn btn-sm btn-outline-primary">{{ __('Update') }}</button>
            </form>
        </div>
    </div>

    @push('js')
    <script>
        $("#birth_date").flatpickr();
    </script>
    @endpush

</x-layout>