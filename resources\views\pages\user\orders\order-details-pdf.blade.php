<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: DejaVu <PERSON>s, Arial, sans-serif;
            color: #333;
            font-size: 14px;
            margin: 0;
            padding: 25px;
            background-color: #fff;
        }

        h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
            color: #222;
        }

        h2 {
            margin: 0 0 10px 0;
            font-size: 18px;
            color: #444;
            border-bottom: 2px solid #eee;
            padding-bottom: 5px;
        }

        p {
            margin: 4px 0;
        }

        .section {
            margin-bottom: 25px;
            padding: 12px 15px;
            border: 1px solid #e5e5e5;
            border-radius: 6px;
            background-color: #fafafa;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 12px;
            font-size: 13px;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px 10px;
            vertical-align: top;
        }

        th {
            background-color: #f7f7f7;
            font-weight: bold;
            color: #222;
            text-align: left;
        }

        tbody tr:nth-child(even) {
            background-color: #fcfcfc;
        }

        tbody tr:hover {
            background-color: #f1f1f1;
        }

        .summary-table td {
            font-size: 15px;
            font-weight: bold;
            padding: 10px;
        }

        .summary-table tr td {
            background-color: #fdfdfd;
        }

        .total-highlight {
            background-color: #f3f9ff;
            color: #000;
        }

        .label {
            font-weight: bold;
            color: #555;
        }
    </style>
</head>

<body>
    <!-- Order Header -->
    <h1>Order #{{ $order->order_number }}</h1>
    <p><span class="label">Status:</span> {{ ucfirst($order->status->value) }}</p>
    <p><span class="label">Created:</span> {{ formatDateTime($order->created_at) }}</p>

    <!-- Shipping & Payment -->
    <div class="section">
        <h2>Shipping & Payment</h2>
        <p><span class="label">Payment Method:</span> {{ $order->payment_method }}</p>
        <p><span class="label">Shipping Type:</span> {{ $order->shippingType->name ?? '-' }}</p>
        <p><span class="label">Shipping Size:</span> {{ $order->shippingSize->name ?? '-' }}</p>
        <p><span class="label">Express:</span> {{ $order->is_express ? 'Yes' : 'No' }}</p>
    </div>

    <!-- Pickup Address -->
    <div class="section">
        <h2>Pickup Details</h2>
        <p><span class="label">Address:</span>
            @if($order->pickupAddress)
            {{ $order->pickupAddress->name }},
            {{ $order->pickupAddress->area->name }},
            {{ $order->pickupAddress->city->name }}
            @else
            -
            @endif
        </p>
    </div>

    <!-- Items -->
    <div class="section">
        <h2>Items</h2>
        <table>
            <thead>
                <tr>
                    <th>Shipment #</th>
                    <th>Description</th>
                    <th>Recipient</th>
                    <th>Phone</th>
                    <th>Dropoff Address</th>
                    <th>Status</th>
                    <th>Cost</th>
                    <th>Fees</th>
                </tr>
            </thead>
            <tbody>
                @foreach($order->items as $item)
                <tr>
                    <td>{{ $item->shipment_number }}</td>
                    <td>{{ $item->description }}</td>
                    <td>{{ $item->recipient_name }}</td>
                    <td>{!! formatPhone($item->country_code, $item->phone) !!}</td>
                    <td>
                        @if($item->dropoffAddress)
                        {{ $item->dropoffAddress->name }},
                        {{ $item->dropoffAddress->area->name }},
                        {{ $item->dropoffAddress->city->name }}
                        @else
                        -
                        @endif
                    </td>
                    <td>{{ ucfirst($item->status) }}</td>
                    <td>{{ number_format($item->cost, 2) }}</td>
                    <td>{{ number_format($item->fees, 2) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Price Summary -->
    <div class="section">
        <h2>Price Summary</h2>
        <table class="summary-table">
            <tbody>
                <tr>
                    <td><span class="label">Delivery Cost:</span> {{ number_format($order->cost, 2) }}</td>
                    <td><span class="label">Fees:</span> {{ number_format($order->fees, 2) }}</td>
                    <td class="total-highlight"><span class="label">Total:</span> {{ number_format($order->total, 2) }}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Driver -->
    @if($order->driver)
    <div class="section">
        <h2>Driver Details</h2>
        <p><span class="label">Name:</span> {{ $order->driver->name }}</p>
    </div>
    @endif

    <!-- Vehicle -->
    @if($order->vehicle)
    <div class="section">
        <h2>Vehicle Details</h2>
        <p><span class="label">Model:</span> {{ $order->vehicle->name }}</p>
        <p><span class="label">Year:</span> {{ $order->vehicle->year }}</p>
    </div>
    @endif
</body>

</html>