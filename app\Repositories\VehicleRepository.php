<?php

namespace App\Repositories;

use App\Enum\OrderStatus;
use App\Models\Driver;
use App\Models\DriverVehicle;
use App\Models\Vehicle;

class VehicleRepository
{
    public function __construct(private readonly Vehicle $model)
    {
        //
    }

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update(Vehicle $vehicle, array $data)
    {
        return $vehicle->update($data);
    }

    public function hasActiveOrder(Vehicle $vehicle)
    {
        return $vehicle->orders()
            ->whereNotNull('driver_id')
            ->whereNotIn('status', [
                OrderStatus::DELIVERED->value,
                OrderStatus::PARTIALLY_DELIVERED->value,
                OrderStatus::CANCELLED->value,
                OrderStatus::DELIVERY_FAILED->value,
            ])->exists();
    }

    public function getAvailableVehiclesForDriver(Driver $driver)
    {
        $usedVehicleIds = DriverVehicle::query()
            ->select('vehicle_id')
            ->whereIn('approval_status', ['pending', 'approved'])
            ->distinct() // distinct vehicle_id
            ->pluck('vehicle_id');

        return $this->model->whereNotIn('id', $usedVehicleIds)
            ->whereIn('transportion_method_id', $driver->shippingTypes->flatMap->transportionMethods->pluck('id')->toArray())
            ->where('company_id', $driver->company_id)
            ->where('approval_status', 'approved')
            ->where('status', 'active')
            ->get();
    }
}
