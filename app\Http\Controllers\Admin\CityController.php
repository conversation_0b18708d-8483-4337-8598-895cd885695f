<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\CitiesDataTable;
use App\DTO\Admin\CityDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\City\StoreRequest;
use App\Models\City;
use App\Repositories\CountryRepository;
use App\Services\Admin\CityService;

class CityController extends Controller
{
    public function __construct(private readonly CityService $cityService, private readonly CountryRepository $countryRepository)
    {
        //
    }

    /**
     * Display a listing of the resource.
     */
    public function index(CitiesDataTable $dataTable)
    {
        return $dataTable->render('pages.admin.cities.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $countries = $this->countryRepository->getForSelect();

        return view('pages.admin.cities.create', ['countries' => $countries]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRequest $request)
    {
        $dto = CityDTO::from($request->validated());
        $this->cityService->create($dto);

        return to_route('admin.cities.index')->with('success', __('City created successfully'));
    }

    /**
     * Display the specified resource.
     */
    public function show(City $city)
    {
        $city->load('country');

        return view('pages.admin.cities.show', ['city' => $city]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(City $city)
    {
        $countries = $this->countryRepository->getForSelect();

        return view('pages.admin.cities.edit', ['city' => $city, 'countries' => $countries]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(StoreRequest $request, City $city)
    {
        $dto = CityDTO::from($request->validated());
        $this->cityService->update($city, $dto);

        return to_route('admin.cities.index')->with('success', __('City updated successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(City $city): \Illuminate\Http\RedirectResponse
    {
        return $this->cityService->delete($city);
    }
}
