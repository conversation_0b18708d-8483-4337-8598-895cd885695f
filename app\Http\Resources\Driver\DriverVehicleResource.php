<?php

namespace App\Http\Resources\Driver;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DriverVehicleResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->vehicle->id,
            'name' => $this->vehicle->name,
            'model' => $this->vehicle->model,
            'plate_number' => $this->vehicle->plate_number,
            'license_number' => $this->vehicle->license_number,
            'year' => $this->vehicle->year,
            'photo' => $this->vehicle->photo?->url,
            'is_active' => $this->when($this->approval_status == 'approved', $this->is_active),
        ];
    }
}
