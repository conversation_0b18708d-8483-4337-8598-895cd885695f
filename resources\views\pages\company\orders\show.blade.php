<x-layout :title="__('Order') . ' #' . $order->order_number">
    <div class="card shadow-sm rounded-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ __('Order') }} #{{ $order->order_number }}</h5>
        </div>

        <div class="card-body">
            <!-- Section 1: User Info -->
            <div class="mb-4">
                <h6 class="text-primary">{{ __('User Info') }}</h6>
                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Name') }}</div>
                    <div class="col-sm-9">{{ $order->user->name }}</div>
                </div>
                <div class="row">
                    <div class="col-sm-3 text-muted">{{ __('Phone Number') }}</div>
                    <div class="col-sm-9">{!! formatPhone($order->user->country_code, $order->user->phone) !!}</div>
                </div>
            </div>

            <hr>

            <!-- Section 2: General Order Summary -->
            <div class="mb-4">
                <h6 class="text-primary">{{ __('General Order Summary') }}</h6>

                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Order Number') }}</div>
                    <div class="col-sm-9">#{{ $order->order_number }}</div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Date') }}</div>
                    <div class="col-sm-9" dir="ltr">{{ formatDateTime($order->created_at) }}</div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Shipping Type') }}</div>
                    <div class="col-sm-9">{{ $order->shippingType->name }}</div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Shipping Method') }}</div>
                    <div class="col-sm-9">{{ $order->shipping_type_id != 1 ? ($order->is_express ?  "Express" : "Standard") : '-' }}</div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Shipment Size') }}</div>
                    <div class="col-sm-9">{{ $order->shippingSize->name }}</div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Shipments Count') }}</div>
                    <div class="col-sm-9">{{ $order->items->count() }}</div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Payment Method') }}</div>
                    <div class="col-sm-9">{{ __(ucfirst($order->payment_method?->value ?? '-')) }}</div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Fees') }}</div>
                    <div class="col-sm-9">{{ formatMoney($order->fees) }} AED</div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Cost') }}</div>
                    <div class="col-sm-9">{{ formatMoney($order->cost) }} AED</div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-3 text-muted">{{ __('Total') }}</div>
                    <div class="col-sm-9">{{ formatMoney($order->total) }} AED</div>
                </div>

                <div class="row">
                    <div class="col-sm-3 text-muted">{{ __('Status') }}</div>
                    <div class="col-sm-9">
                        <span id="general-order-status" style="margin-right: 20px">{!! __(str_replace('_', ' ', $order->status->value)) !!}</span>
                        @if(in_array($order->status->value, [\App\Enum\OrderStatus::READY_TO_PICKUP->value, \App\Enum\OrderStatus::HEADING_TO_PICKUP->value]))
                        <button type="button" id="cancelOrderBtn" class="btn btn-danger btn-sm cancel-order-btn" data-url="{{ route('company.orders.cancel', $order->id) }}">
                            {{ __('Cancel') }}
                            <span class="spinner-border spinner-border-sm ms-2 d-none" id="cancelOrderSpinner" role="status" aria-hidden="true"></span>
                        </button>
                        @endif
                    </div>
                </div>
            </div>

            <hr class="my-4">

            <!-- Section 3: Driver Info -->
            <div class="mb-4">
                <h6 class="text-primary mb-3">{{ __('Driver Information') }}</h6>
                <div class="card border-0 shadow-sm rounded-4">
                    <div class="card-body">
                        <div class="row align-items-center g-4">
                            <div class="col-auto">
                                <img src="{{ $order->driver->profile_image_url }}"
                                     alt="{{ $order->driver->name }}"
                                     class="rounded-circle shadow-sm"
                                     style="width: 80px; height: 80px; object-fit: cover;">
                            </div>
                            <div class="col">
                                <div><span class="text-muted">{{ __('Driver Name') }}:</span> <strong>{{ $order->driver->name }}</strong></div>
                                <div><span class="text-muted">{{ __('Phone') }}:</span> {!! formatPhone($order->driver->country_code, $order->driver->phone) !!}</div>
                                <div><span class="text-muted">{{ __('Vehicle Model') }}:</span> {{ $order->vehicle?->model }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <hr class="my-4">

            <!-- Section 4: Order Items -->
            <div class="mb-5">
                <h5 class="text-primary mb-4">{{ __('Order Items') }}</h5>

                <div class="row g-4">
                    @foreach($order->items as $item)
                        <div class="col-md-6">
                            <div class="card border-0 shadow rounded-4 h-100">
                                <div class="card-body py-4 px-4">
                                    <div class="mb-3">
                                        <h6 class="fw-semibold mb-2 text-dark">
                                            {{ __('Shipment #:') }} {{ $item->shipment_number }}
                                        </h6>
                                        <p class="text-muted mb-1">{{ $item->description }}</p>
                                    </div>

                                    <div class="border-top pt-2">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">{{ __('Recipient Name') }}</span>
                                            <span class="fw-medium text-dark">{{ $item->recipient_name }}</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">{{ __('Recipient Phone') }}</span>
                                            <span class="fw-medium text-dark">{!! formatPhone($item->country_code, $item->phone) !!}</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">{{ __('Status') }}</span>
                                            <span class="badge bg-label-info">{!! __(str_replace('_', ' ', $item->status)) !!}</span>
                                        </div>
                                    </div>

                                    <div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">{{ __('Cost') }}</span>
                                            <span class="fw-medium">{{ formatMoney($item->cost) }} AED</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="text-muted">{{ __('Fees') }}</span>
                                            <span class="fw-medium">{{ formatMoney($item->fees) }} AED</span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <strong>{{ __('Total') }}</strong>
                                            <strong>{{ formatMoney($item->cost + $item->fees) }} AED</strong>
                                        </div>
                                    </div>

                                    @if($item->media->isNotEmpty())
                                        <div class="mt-4">
                                            <label class="form-label text-muted">{{ __('Shipment Images') }}</label>
                                            <div class="d-flex flex-wrap gap-2">
                                                @foreach($item->media as $media)
                                                    <a href="{{ $media->url }}" target="_blank">
                                                        <img src="{{ $media->url }}"
                                                            class="img-thumbnail"
                                                            style="width: 80px; height: 80px; object-fit: cover;"
                                                            alt="Image">
                                                    </a>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif

                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    @push('js')
        <script>
        $(document).on('click', '.cancel-order-btn', function (e) {
            e.preventDefault();

            let url = $(this).data('url');
            let token = '{{ csrf_token() }}';
            let spinner = $('#cancelOrderSpinner');

            spinner.removeClass('d-none');

            $.ajax({
                url: url,
                method: 'POST',
                data: {
                    _token: token,
                },
                dataType: 'json',
                success: function (response) {
                    $("#general-order-status").text("{{ __("Cancelled") }}");
                    $("#cancelOrderBtn").addClass("d-none");

                },
                error: function (xhr) {
                   try {
                        // Try to parse the JSON response
                        let response = JSON.parse(xhr.responseText);
                        alert(response.message);
                    } catch (e) {
                        console.error('Failed to parse error response', e);
                    }
                },
            });
        });
        </script>
    @endpush
</x-layout>
