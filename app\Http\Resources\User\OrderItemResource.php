<?php

namespace App\Http\Resources\User;

use App\Http\Resources\CountryResource;
use App\Repositories\ChatRepository;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'shipment_number' => $this->shipment_number,
            'status' => $this->status,
            'cost' => $this->cost,
            'fees' => $this->fees,
            'dropoff_country' => $this->when($this->dropoff_country_id, fn (): \App\Http\Resources\CountryResource => new CountryResource($this->whenLoaded('dropoffCountry'))),
            'dropoff_address' => $this->when($this->dropoff_address_id, fn (): \App\Http\Resources\User\AddressResource => new AddressResource($this->whenLoaded('dropoffAddress'))),
            'dropoff_location' => $this->when($this->dropoff_location, fn (): array => [
                'lat' => (string) $this->dropoff_location->latitude,
                'lng' => (string) $this->dropoff_location->longitude,
            ]),
            'recipient_name' => $this->recipient_name,
            'country_code' => $this->country_code,
            'phone' => $this->phone,
            'description' => $this->description,
            'chat_id' => app(ChatRepository::class)->firstOrCreate($this->resource)->id,
            'medias' => MediaResource::collection($this->whenLoaded('media')),
        ];
    }
}
