<x-layout :title="__('Edit Driver')">
    <x-session-message />
    @if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
            <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif
    <form action="{{ route('company.drivers.update', $driver) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        <div class="card">
            <div class="card-body">

                <!-- BASIC INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Basic Info') }}</h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Driver Name') }}</b></label>
                            <input type="text" name="name" class="form-control" value="{{ old('name', $driver->name) }}">
                            <x-input-error name="name" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Email') }}</b></label>
                            <input type="email" name="email" class="form-control" value="{{ old('email', $driver->email) }}" autocomplete="off">
                            <x-input-error name="email" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Phone') }}</b></label>
                            <x-phone-input :countries="$countries" :country_code="old('country_code', $driver->country_code)" :phone="old('phone', $driver->phone)" />
                            <x-input-error name="phone" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Gender') }}</b></label>
                            <div class="d-flex gap-3 mt-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="gender" id="gender_male" value="male" {{ old('gender', $driver->gender) == 'male' ? 'checked' : '' }}>
                                    <label class="form-check-label" for="gender_male">{{ __('Male') }}</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="gender" id="gender_female" value="female" {{ old('gender', $driver->gender) == 'female' ? 'checked' : '' }}>
                                    <label class="form-check-label" for="gender_female">{{ __('Female') }}</label>
                                </div>
                            </div>
                            <x-input-error name="gender" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label for="birth_date" class="form-label"><b>{{ __('Birth Date') }}</b></label>
                            <input type="date" name="birth_date" id="birth_date" class="form-control flatpickr-input" value="{{ old('birth_date', $driver->birth_date ? formatDate($driver->birth_date) : '') }}" readonly="readonly">
                            <x-input-error name="birth_date" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('ID Number') }}</b></label>
                            <input type="text" name="id_number" class="form-control" value="{{ old('id_number', $driver->id_number) }}">
                            <x-input-error name="id_number" />
                        </div>
                    </div>
                    <div class="row">
                        <!-- Profile Image -->
                        <div class="mb-4 col-lg-4">
                            <label class="form-label"><b>{{ __('Profile Image') }}</b></label>
                            <input type="file" name="profile_image" class="form-control" accept="image/*">
                            @if($driver->profileImage)
                                <div class="mt-2">
                                    <a href="{{ $driver->profileImage->url }}" target="_blank">
                                        <img src="{{ $driver->profileImage->url }}" alt="Profile Image" class="card-image">
                                    </a>
                                </div>
                            @endif
                            <x-input-error name="profile_image" />
                        </div>

                        <!-- ID Number Image -->
                        <div class="mb-4 col-lg-4">
                            <label class="form-label"><b>{{ __('ID Number Image') }}</b></label>
                            <input type="file" name="id_number_image" class="form-control" accept="image/*,application/pdf">
                            @if($driver->idImage)
                                <div class="mt-2">
                                    <a href="{{ $driver->idImage->url }}" target="_blank">
                                        <img src="{{ $driver->idImage->url }}" alt="ID Number Image" class="card-image">
                                    </a>
                                </div>
                            @endif
                            <x-input-error name="id_number_image" />
                        </div>

                        <!-- Driving Licence Image -->
                        <div class="mb-4 col-lg-4">
                            <label class="form-label"><b>{{ __('Driving Licence Image') }}</b></label>
                            <input type="file" name="driving_licence_image" class="form-control" accept="image/*,application/pdf">
                            @if($driver->drivingLiscenceImage)
                                <div class="mt-2">
                                    <a href="{{ $driver->drivingLiscenceImage->url }}" target="_blank">
                                        <img src="{{ $driver->drivingLiscenceImage->url }}" alt="Driving Licence Image" class="card-image">
                                    </a>
                                </div>
                            @endif
                            <x-input-error name="driving_licence_image" />
                        </div>

                        <!-- Additional Attachments -->
                        <div class="mb-4 col-lg-4">
                            <label class="form-label"><b>{{ __('Additional Attachments') }} (supports multi file)</b></label>
                            <input type="file" name="additional_attachments[]" class="form-control" accept="image/*,application/pdf" multiple>
                            @if($driver->additionalAttachments->isNotEmpty())
                                <div class="mt-2 d-flex gap-2">
                                    @foreach($driver->additionalAttachments as $attachment)
                                        <a href="{{ $attachment->url }}" target="_blank">
                                            <img src="{{ $attachment->url }}" alt="Attachment" class="card-image">
                                        </a>
                                    @endforeach
                                </div>
                            @endif
                            <x-input-error name="additional_attachments.*" />
                        </div>
                    </div>
                </div>

                <!-- BANK ACCOUNT INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Bank Account Info') }}</h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Bank Name') }}</b></label>
                            <input type="text" name="bank_name" class="form-control" value="{{ old('bank_name', $driver->bank_name) }}">
                            <x-input-error name="bank_name" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Bank Account Owner') }}</b></label>
                            <input type="text" name="bank_account_owner" class="form-control" value="{{ old('bank_account_owner', $driver->bank_account_owner) }}">
                            <x-input-error name="bank_account_owner" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Bank Account Number') }}</b></label>
                            <input type="text" name="bank_account_number" class="form-control" value="{{ old('bank_account_number', $driver->bank_account_number) }}">
                            <x-input-error name="bank_account_number" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('IBAN') }}</b></label>
                            <input type="text" name="iban" class="form-control" value="{{ old('iban', $driver->iban) }}">
                            <x-input-error name="iban" />
                        </div>
                    </div>
                </div>

                <!-- SHIPPING OPTIONS -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Shipping Options') }}</h5>

                    @foreach($shippingTypes as $index => $type)
                    <div class="border rounded p-3 mb-3">
                        <input type="hidden" name="shipping_options[{{ $index }}][shipping_type_id]" value="{{ $type->shippingType->id }}">

                        <div class="form-check mb-2">
                            <input
                                class="form-check-input shipping-type-toggle"
                                type="checkbox"
                                name="shipping_options[{{ $index }}][enabled]"
                                value="1"
                                id="shipping_type_{{ $type->shippingType->id }}"
                                {{ old("shipping_options.$index.enabled", (bool) $driver->shippingTypes->where('shipping_type_id', $type->shippingType->id)->first()) ? 'checked' : '' }}
                                data-target="#shipping-type-details-{{ $type->shippingType->id }}">
                            <label class="form-check-label fw-bold" for="shipping_type_{{ $type->shippingType->id }}">
                                {{ $type->shippingType->name }}
                            </label>
                        </div>

                        <div class="ps-3 shipping-type-details"
                            id="shipping-type-details-{{ $type->shippingType->id }}"
                            style="{{ old("shipping_options.$index.enabled", $driver->shippingTypes->where('shipping_type_id', $type->shippingType->id)->first()->enabled ?? false) ? '' : 'display: none;' }}">

                            <!-- Transportation Methods -->
                            <div class="mb-3">
                                <label class="fw-bold">{{ __('Transportation Methods') }}</label>
                                <div class="row">
                                    @foreach($type->transportionMethods->unique('id') as $method)
                                    <div class="col-md-1">
                                        <div class="form-check">
                                            <input
                                                class="form-check-input"
                                                type="checkbox"
                                                name="shipping_options[{{ $index }}][transportion_methods][]"
                                                value="{{ $method->id }}"
                                                id="method_{{ $type->shippingType->id }}_{{ $method->id }}"
                                                {{ in_array($method->id, old(
                                                    "shipping_options.$index.transportation_methods",
                                                    \App\Models\DriverShippingType::where([
                                                        'driver_id' => $driver->id,
                                                        'shipping_type_id' => $type->shippingType->id
                                                    ])->first()?->transportionMethods->pluck('id')->toArray() ?? []
                                                )) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="method_{{ $type->shippingType->id }}_{{ $method->id }}">
                                                {{ $method->name }}
                                            </label>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                                <x-input-error :name="'shipping_options.' . $index . '.transportation_methods'" />
                            </div>

                            <!-- Cities & Areas -->
                            @if($type->shippingType->id === 1)
                            <div class="mb-3">
                                <label class="fw-bold">{{ __('Cities & Areas') }}</label>
                                <div class="shipping-city-area-wrapper" data-index="{{ $index }}">
                                    <!-- Dynamically added cities go here -->
                                </div>

                                <x-input-error :name="'shipping_options.' . $index . '.cities'" />

                                @if(old("shipping_options.$index.cities", $driver->immediateShippingCities))
                                @foreach(old("shipping_options.$index.cities", $driver->immediateShippingCities) as $cityIndex => $cityData)
                                <x-input-error :name="'shipping_options.' . $index . '.cities.' . $cityIndex . '.areas'" />
                                @endforeach
                                @endif

                                <button type="button" class="btn btn-sm btn-outline-primary mt-2 add-city-button" data-index="{{ $index }}">
                                    + {{ __('Add City') }}
                                </button>
                            </div>

                            @elseif($type->shippingType->id === 2)
                            <!-- Cities for Intercity Shipping -->
                            <div class="mb-3">
                                <label class="fw-bold">{{ __('Cities') }}</label>
                                <div class="row">
                                    @foreach($intercityShippingPickupCities as $city)
                                    <div class="col-md-2">
                                        <div class="form-check">
                                            @php
                                                $oldCities = collect(old(
                                                    "shipping_options.$index.cities",
                                                    $driver->intercityShippingCities->map(fn($c) => ['id' => $c->id])->toArray()
                                                ))->pluck('id')->toArray();
                                            @endphp
                                            <input
                                                class="form-check-input"
                                                type="checkbox"
                                                name="shipping_options[{{ $index }}][cities][][id]"
                                                value="{{ $city->id }}"
                                                id="city_{{ $type->shippingType->id }}_{{ $city->id }}"
                                                {{ in_array($city->id, $oldCities) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="city_{{ $type->shippingType->id }}_{{ $city->id }}">
                                                {{ $city->name }}
                                            </label>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                                <x-input-error :name="'shipping_options.' . $index . '.cities'" />
                            </div>

                            @elseif($type->shippingType->id === 3)
                            <!-- Cities for International Shipping -->
                            <div class="mb-3">
                                <label class="fw-bold">{{ __('Cities') }}</label>
                                <div class="row">
                                    @foreach($internationalShippingCities as $city)
                                    <div class="col-md-2">
                                        <div class="form-check">
                                            @php
                                                $oldCities = collect(old(
                                                    "shipping_options.$index.cities",
                                                    $driver->internationalShippingCities->map(fn($c) => ['id' => $c->id])->toArray()
                                                ))->pluck('id')->toArray();
                                            @endphp
                                            <input
                                                class="form-check-input"
                                                type="checkbox"
                                                name="shipping_options[{{ $index }}][cities][][id]"
                                                value="{{ $city->id }}"
                                                id="city_{{ $type->shippingType->id }}_{{ $city->id }}"
                                                {{ in_array($city->id, $oldCities) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="city_{{ $type->shippingType->id }}_{{ $city->id }}">
                                                {{ $city->name }}
                                            </label>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                                <x-input-error :name="'shipping_options.' . $index . '.cities'" />
                            </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>


                <button type="submit" class="btn btn-outline-primary">{{ __('Update') }}</button>
            </div>
        </div>
    </form>

    @push('css')
    <style>
        #shipping-options-list .border {
            background: #f9f9f9;
        }
    </style>
    @endpush

    @push('js')
    <script>
        $("#birth_date").flatpickr();
    </script>

    @php
    $oldOptions = old('shipping_options', $driver->shippingTypes->map(function($option) use ($driver) {
        $cities = [];

        if ($option->shipping_type_id == 1) {
            $cities = $driver->immediateShippingCities->map(function($city) {
                return [
                    'id' => $city->city_id, // use city_id column from DriverImmediateShippingCity
                    'areas' => $city->areas->pluck('id')->map(fn($id) => (int)$id)->toArray()
                ];
            })->toArray();
        } elseif ($option->shipping_type_id == 2) {
            $cities = $driver->intercityShippingCities->pluck('id')->toArray();
        } else {
            $cities = $driver->internationalShippingCities->pluck('id')->toArray();
        }

        return [
            'shipping_type_id' => $option->shipping_type_id,
            'enabled' => $option->enabled,
            'transportation_methods' => $driver->shippingTypes->flatMap->transportionMethods
                ->where('driver_shipping_types.shipping_type_id', $option->shipping_type_id)
                ->pluck('id')
                ->toArray(),
            'cities' => $cities
        ];
    })->toArray());
    @endphp

    <script>
        const allCities = @json($immediateCities); // Includes areas
        const oldShippingOptions = @json($oldOptions);

        function toggleShippingDetails(checkbox) {
            const targetId = checkbox.dataset.target;
            const details = document.querySelector(targetId);
            if (!details) return;

            const inputs = details.querySelectorAll('input, select, textarea');

            if (checkbox.checked) {
                details.style.display = '';
                inputs.forEach(input => input.disabled = false);
            } else {
                details.style.display = 'none';
                inputs.forEach(input => {
                    if (input.type === 'checkbox' || input.type === 'radio') {
                        input.checked = false;
                    } else if (input.type !== 'hidden') {
                        input.value = '';
                    }
                    input.disabled = true;
                });
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');

            // Initialize toggle for all shipping-type checkboxes
            document.querySelectorAll('.shipping-type-toggle').forEach(function(checkbox) {
                toggleShippingDetails(checkbox); // Set initial visibility
                checkbox.addEventListener('change', function() {
                    toggleShippingDetails(this);
                });
            });

            // Handle dynamic "Add City" buttons for immediate shipping (type_id = 1)
            document.querySelectorAll('.add-city-button').forEach(btn => {
                btn.addEventListener('click', function() {
                    const shippingIndex = this.dataset.index;
                    const container = this.previousElementSibling;
                    const cityCount = container.querySelectorAll('.city-area-group').length;
                    const newCityIndex = cityCount;

                    const group = document.createElement('div');
                    group.className = 'border rounded p-2 mb-2 city-area-group';

                    const citySelectId = `city_select_${shippingIndex}_${newCityIndex}`;
                    const citySelectName = `shipping_options[${shippingIndex}][cities][${newCityIndex}][id]`;

                    let html = `
                        <div class="mb-2">
                            <label class="fw-bold">{{ __('City') }}</label>
                            <select class="form-select city-select" name="${citySelectName}" data-shipping-index="${shippingIndex}" data-city-index="${newCityIndex}" id="${citySelectId}">
                                <option value="">{{ __('Select City') }}</option>
                                ${allCities.map(city => `<option value="${city.id}">${city.name}</option>`).join('')}
                            </select>
                        </div>
                        <div class="area-checkboxes mt-2" id="area_box_${shippingIndex}_${newCityIndex}">
                            <!-- Area checkboxes will appear here -->
                        </div>
                    `;

                    group.innerHTML = html;
                    container.appendChild(group);
                });
            });

            // When city is selected, load its areas for immediate shipping (type_id = 1)
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('city-select')) {
                    const shippingIndex = e.target.dataset.shippingIndex;
                    const cityIndex = e.target.dataset.cityIndex;
                    const selectedCityId = e.target.value;
                    const areaBox = document.getElementById(`area_box_${shippingIndex}_${cityIndex}`);

                    areaBox.innerHTML = '';

                    if (!selectedCityId) return;

                    const city = allCities.find(c => c.id == selectedCityId);
                    if (!city) return;

                    const checkboxes = city.areas.map(area => `
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" 
                                name="shipping_options[${shippingIndex}][cities][${cityIndex}][areas][]" 
                                value="${area.id}" id="area_${shippingIndex}_${cityIndex}_${area.id}">
                            <label class="form-check-label" for="area_${shippingIndex}_${cityIndex}_${area.id}">
                                ${area.name.{{ app()->getLocale() }}}
                            </label>
                        </div>
                    `).join('');

                    areaBox.innerHTML = checkboxes;
                }
            });

            // Safety net for form submit: disable all unchecked shipping type inputs
            form.addEventListener('submit', function() {
                document.querySelectorAll('.shipping-type-toggle').forEach(function(checkbox) {
                    if (!checkbox.checked) {
                        const targetId = checkbox.dataset.target;
                        const details = document.querySelector(targetId);
                        if (!details) return;

                        const inputs = details.querySelectorAll('input, select, textarea');
                        inputs.forEach(input => input.disabled = true);
                    }
                });
            });

            // Repopulate old shipping options
            if (oldShippingOptions) {
                Object.keys(oldShippingOptions).forEach(index => {
                    const option = oldShippingOptions[index];
                    const shippingTypeId = parseInt(option.shipping_type_id);
                    const container = document.querySelector(`.shipping-city-area-wrapper[data-index="${index}"]`);
                    if (!container) return;

                    if (shippingTypeId === 1 && option.cities) {
                        option.cities.forEach((cityObj, cityIndex) => {
                            const group = document.createElement('div');
                            group.className = 'border rounded p-2 mb-2 city-area-group';

                            const citySelectId = `city_select_${index}_${cityIndex}`;
                            const citySelectName = `shipping_options[${index}][cities][${cityIndex}][id]`;

                            const cityOptions = allCities.map(city => {
                                const selected = city.id == cityObj.id ? 'selected' : '';
                                return `<option value="${city.id}" ${selected}>${city.name}</option>`;
                            }).join('');

                            let html = `
                                <div class="mb-2">
                                    <label class="fw-bold">City</label>
                                    <select class="form-select city-select" name="${citySelectName}" data-shipping-index="${index}" data-city-index="${cityIndex}" id="${citySelectId}">
                                        <option value="">Select City</option>
                                        ${cityOptions}
                                    </select>
                                </div>
                                <div class="area-checkboxes mt-2" id="area_box_${index}_${cityIndex}">
                                    ${(() => {
                                        const selectedCity = allCities.find(c => c.id == cityObj.id);
                                        if (!selectedCity) return '';

                                        return selectedCity.areas.map(area => {
                                            const checked = cityObj.areas && cityObj.areas.map(Number).includes(Number(area.id)) ? 'checked' : '';
                                            return `
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                        name="shipping_options[${index}][cities][${cityIndex}][areas][]" 
                                                        value="${area.id}" id="area_${index}_${cityIndex}_${area.id}" ${checked}>
                                                    <label class="form-check-label" for="area_${index}_${cityIndex}_${area.id}">
                                                        ${area.name.{{ app()->getLocale() }}}
                                                    </label>
                                                </div>
                                            `;
                                        }).join('');
                                    })()}
                                </div>
                            `;

                            group.innerHTML = html;
                            container.appendChild(group);
                        });
                    }
                });
            }
        });
    </script>
    @endpush
</x-layout>