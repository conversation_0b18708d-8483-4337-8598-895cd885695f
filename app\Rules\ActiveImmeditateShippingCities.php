<?php

namespace App\Rules;

use App\Repositories\CityRepository;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ActiveImmeditateShippingCities implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $cityRepository = app(CityRepository::class);

        $cityIds = array_column($value, 'id');

        $cities = $cityRepository->getByIds($cityIds);

        foreach ($cities as $city) {
            if ($city->status == 'inactive') {
                $fail('The :attribute contains inactive cities.');
            }
        }
    }
}
