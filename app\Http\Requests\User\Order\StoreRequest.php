<?php

namespace App\Http\Requests\User\Order;

use App\Rules\ValidMedia;
use App\Rules\ValidPhone;
use App\Rules\ValidShippingDistance;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $userId = auth('user')->id();

        return [
            'shipping_type_id' => 'required|exists:shipping_types,id',
            'shipping_size_id' => 'required|exists:shipping_sizes,id',
            'pickup_country_id' => [
                'required_if:shipping_type_id,3',
                Rule::exists('countries', 'id')->where(function ($query): void {
                    $query->where('abbv', 'AE');
                }),
            ],
            'pickup_address_id' => ['nullable', Rule::exists('addresses', 'id')->where('user_id', $userId)],
            'pickup_location' => 'nullable|array',
            'pickup_location.lat' => ['required_with:pickup_location', 'numeric', 'between:-90,90', 'regex:/^-?\d+(\.\d+)?$/'],
            'pickup_location.lng' => ['required_with:pickup_location', 'numeric', 'between:-180,180', 'regex:/^-?\d+(\.\d+)?$/'],
            'is_express' => [
                'required',
                'boolean',
                Rule::when(
                    $this->shipping_type_id == 1,
                    fn () => Rule::in([1]),
                    fn () => Rule::in([0, 1])
                ),
            ],
            'description' => 'nullable|string|min:4|max:320',
            'items' => ['required', 'array', 'min:1', Rule::when($this->is_express || $this->shipping_type_id == 1, 'max:1')],
            'items.*.dropoff_country_id' => 'required_if:shipping_type_id,3|exists:countries,id|different:pickup_country_id',
            'items.*.dropoff_address_id' => ['nullable', Rule::exists('addresses', 'id')->where('user_id', $userId)],
            'items.*.dropoff_location' => 'nullable|array',
            'items.*.dropoff_location.lat' => 'required_with:items.*.dropoff_location|numeric|between:-90,90',
            'items.*.dropoff_location.lng' => 'required_with:items.*.dropoff_location|numeric|between:-180,180',
            'items.*.recipient_name' => 'required|string|min:4|max:255',
            'items.*.country_code' => 'required|string|max:10',
            'items.*.phone' => 'required|string|max:20',
            'items.*.description' => 'nullable|string|max:320',
            'items.*.images' => 'required|array|min:1|max:5',
            'items.*.images.*' => ['required', new ValidMedia(['image'])],
        ];
    }

    public function withValidator($validator): void
    {
        $validator->after(function ($validator): void {
            if ($validator->errors()->isNotEmpty()) {
                return;
            }

            if ($this->filled('pickup_address_id') && $this->filled('pickup_location.lat') && $this->filled('pickup_location.lng')) {
                $validator->errors()->add('pickup_address_id', __('validation.pickup_exclusive_fields'));
                $validator->errors()->add('pickup_location', __('validation.pickup_exclusive_fields'));
            }

            foreach ($this->items as $index => $item) {
                if (! empty($item['dropoff_address_id']) && ! empty($item['dropoff_location']['lat']) && ! empty($item['dropoff_location']['lng'])) {
                    $validator->errors()->add("items.$index.dropoff_address_id", __('validation.dropoff_exclusive_fields', ['item' => $index + 1]));
                    $validator->errors()->add("items.$index.dropoff_location", __('validation.dropoff_exclusive_fields', ['item' => $index + 1]));
                }
            }

            $this->validatePickupLocation($validator);
            $this->validateItems($validator);
            $this->validateShippingDistance($validator);
        });
    }

    protected function isExpressType(): bool
    {
        return $this->is_express || $this->shipping_type_id == 1;
    }

    protected function validatePickupLocation($validator): void
    {
        if (! $this->pickup_address_id && ! $this->pickup_location) {
            $validator->errors()->add('pickup_address_id', __('validation.pickup_location_required'));
        }
    }

    protected function validateItems($validator): void
    {
        foreach ($this->input('items', []) as $index => $item) {
            $this->validateDropoff($item, $index, $validator);
            $this->validatePhone($item, $index, $validator);
        }
    }

    protected function validateDropoff(array $item, int $index, $validator): void
    {
        if (empty($item['dropoff_address_id']) && empty($item['dropoff_location'])) {
            $validator->errors()->add("items.{$index}.dropoff_address_id", __('validation.dropoff_location_required', ['item' => $index + 1]));
        }
    }

    protected function validatePhone(array $item, int $index, $validator): void
    {
        if (! isset($item['phone']) || ! isset($item['country_code'])) {
            return;
        }

        $rule = new ValidPhone($item['country_code']);
        $rule->validate("items.{$index}.phone", $item['phone'], function ($message) use ($validator, $index): void {
            $validator->errors()->add("items.{$index}.phone", $message);
        });
    }

    protected function validateShippingDistance($validator): void
    {
        if (! $this->shipping_type_id || ! $this->items) {
            return;
        }

        $rule = new ValidShippingDistance(
            $this->shipping_type_id,
            $this->pickup_address_id,
            $this->pickup_location,
            $this->items
        );

        $rule->validate('shipping_distance', null, function ($message) use ($validator): void {
            $validator->errors()->add('shipping_distance', $message);
        });
    }
}
