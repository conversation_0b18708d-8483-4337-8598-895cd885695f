<?php

namespace App\DTO\User;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Optional;

class OrderDTO extends Data
{
    public function __construct(
        public int $shipping_type_id,
        public int $shipping_size_id,
        public ?int $pickup_country_id,
        public ?int $pickup_address_id,
        public ?array $pickup_location,
        public string|Optional $status,
        public bool $is_express,
    ) {
        //
    }
}
