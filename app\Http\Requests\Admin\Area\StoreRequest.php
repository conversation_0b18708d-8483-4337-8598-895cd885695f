<?php

namespace App\Http\Requests\Admin\Area;

use App\Rules\LocalizedAlpha;
use App\Rules\ValidPolygon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->route('area')->id ?? null;

        $rules = [
            'name' => ['required', 'array'],
            'city_id' => ['required', 'exists:cities,id'],
            'area' => ['required', 'array', new ValidPolygon],
            'status' => ['required', 'string', 'in:active,inactive'],
        ];

        // Add validation for each locale
        foreach (config('app.locales', ['en', 'ar']) as $locale) {
            $rules["name.{$locale}"] = ['required', 'string', 'min:3', 'max:255', new LocalizedAlpha, Rule::unique('areas', "name->{$locale}")->ignore($id)];
        }

        return $rules;
    }
}
