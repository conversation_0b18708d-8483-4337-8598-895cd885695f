<x-layout :title="__('Company') . ': ' . $company->name">
    <div class="card shadow-sm rounded-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ __('Company') }}: {{ $company->name }}</h5>
        </div>

        <div class="card-body">

            <!-- Basic Info -->
            <div class="mb-4 p-3 border rounded">
                <h6 class="text-primary mb-3"><strong>{{ __('Basic Info') }}</strong></h6>
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('ID') }}</div>
                    <div class="col-sm-9">{{ $company->id }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Name') }}</div>
                    <div class="col-sm-9">{{ $company->name }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Business Registration Number') }}</div>
                    <div class="col-sm-9">{{ $company->business_registration_number }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Phone') }}</div>
                    <div class="col-sm-9">{!! formatPhone($company->country_code, $company->phone) !!}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Email') }}</div>
                    <div class="col-sm-9">{{ $company->email ?? '-' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Address') }}</div>
                    <div class="col-sm-9">{{ $company->address ?? '-' }}</div>
                </div>
                @if ($company->status == 'approved')
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Status') }}</div>
                    <div class="col-sm-9">{!! statusBadge($company->status) !!}</div>
                </div>
                @endif

                @if ($company->approval_status != 'approved')
                @include('pages.admin.companies.registration-requests.approval-status')
                @endif
    
            </div>

            <!-- Bank Account Info -->
            <div class="mb-4 p-3 border rounded">
                <h6 class="text-primary mb-3"><strong>{{ __('Bank Account Details') }}</strong></h6>
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Bank Name') }}</div>
                    <div class="col-sm-9">{{ $company->bank_name ?? '-' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Bank Account Owner') }}</div>
                    <div class="col-sm-9">{{ $company->bank_account_owner ?? '-' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Bank Account Number') }}</div>
                    <div class="col-sm-9">{{ $company->bank_account_number ?? '-' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('IBAN') }}</div>
                    <div class="col-sm-9">{{ $company->iban ?? '-' }}</div>
                </div>
            </div>

            <!-- Shipping Options -->
            <div class="mb-4 p-3 border rounded">
                <h6 class="text-primary mb-3"><strong>{{ __('Shipping Options') }}</strong></h6>
                <div class="row mb-3">
                    <div class="col-sm-12">
                        @include('pages.admin.companies.shipping-types', ['company' => $company])
                    </div>
                </div>
            </div>

            <!-- Primary Admin Contact Info -->
            <div class="mb-4 p-3 border rounded">
                <h6 class="text-primary mb-3"><strong>{{ __('Primary Admin Contact Info') }}</strong></h6>
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Name') }}</div>
                    <div class="col-sm-9">{{ $company->superAdmin->name }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Email') }}</div>
                    <div class="col-sm-9">{{ $company->superAdmin->email }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Phone') }}</div>
                    <div class="col-sm-9">{!! formatPhone($company->superAdmin->country_code, $company->superAdmin->phone) !!}</div>
                </div>
            </div>

            <!-- Attachments -->
            <div class="mb-4 p-3 border rounded">
                <h6 class="text-primary mb-3"><strong>{{ __('Attachments') }}</strong></h6>

                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Logo') }}</div>
                    <div class="col-sm-9">
                        @if ($company->logo)
                            <a href="{{ $company->logo->url }}" target="_blank">
                                <img src="{{ $company->logo->url }}" alt="Logo" class="img-thumbnail" style="max-height: 150px;">
                            </a>
                        @else
                            <span class="text-muted">-</span>
                        @endif
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Commercial Registration Certificate') }}</div>
                    <div class="col-sm-9">
                        @if ($company->commercialRegistrationCertificate)
                            <a href="{{ $company->commercialRegistrationCertificate->url }}" target="_blank">
                                <img src="{{ $company->commercialRegistrationCertificate->url }}" alt="Certificate" class="img-thumbnail" style="max-height: 150px;">
                            </a>
                        @else
                            <span class="text-muted">-</span>
                        @endif
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Tax Certificate') }}</div>
                    <div class="col-sm-9">
                        @if ($company->taxCertificate)
                            <a href="{{ $company->taxCertificate->url }}" target="_blank">
                                <img src="{{ $company->taxCertificate->url }}" alt="Tax Certificate" class="img-thumbnail" style="max-height: 150px;">
                            </a>
                        @else
                            <span class="text-muted">-</span>
                        @endif
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('Cargo Insurance Certificate') }}</div>
                    <div class="col-sm-9">
                        @if ($company->cargoInsuranceCertificate)
                            <a href="{{ $company->cargoInsuranceCertificate->url }}" target="_blank">
                                <img src="{{ $company->cargoInsuranceCertificate->url }}" alt="Insurance" class="img-thumbnail" style="max-height: 150px;">
                            </a>
                        @else
                            <span class="text-muted">-</span>
                        @endif
                    </div>
                </div>
            </div>

        </div>
    </div>
    
</x-layout>
