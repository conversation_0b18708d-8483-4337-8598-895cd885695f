<x-layout :title="__('Companies')">
    <x-session-message />
    <div class="card">
        <div class="card-heading">
            <p>{{ __('Companies') }}</p>
            <div>
                @if(auth('admin')->user()->hasPermission('create company'))
                <a class="btn btn-sm btn-outline-primary" href="{{ route('admin.companies.create') }}">{{ __('Create') }}</a>
                @endif
            </div>
        </div>
        <div class="row" id="filters" data-datatable-id="companies-table">
            <!-- Search -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Search') }}</b></label>
                <input type="text" name="search_param" class="form-control" onchange="dt_filter()">
            </div>

            <!-- Status -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Status') }}</b></label>
                <select name="status" class="select2 form-select form-select-lg" data-placeholder="{{ __('Choose') }}..." data-allow-clear="true" onchange="dt_filter()">
                    <option></option>
                    <option value="active">{{ __('Active') }}</option>
                    <option value="inactive">{{ __('Inactive') }}</option>
                </select>
            </div>
        </div>
        <div class="card-body">
            <div class="card-datatable table-responsive pt-0">
                {{ $dataTable->table(['class' => 'datatables-basic table table-striped']) }}
            </div>
            <x-delete-modal />
        </div>
    </div>

    @push('css')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}" />
    @endpush

    @push('js')
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    {{ $dataTable->scripts(attributes: ['type' => 'module']) }}
    @endpush
</x-layout>
