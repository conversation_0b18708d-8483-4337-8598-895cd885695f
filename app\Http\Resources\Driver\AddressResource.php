<?php

namespace App\Http\Resources\Driver;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'city' => $this->area?->city?->name ?? null,
            'area' => $this->area?->name ?? null,
            'location' => $this->when($this->location, fn (): array => [
                'lat' => $this->location->latitude,
                'lng' => $this->location->longitude,
            ]),
        ];
    }
}
