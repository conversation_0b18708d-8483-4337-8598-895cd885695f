<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->unsignedTinyInteger('driver_search_attempts')->default(0);
            $table->timestamp('driver_search_started_at')->nullable();
            $table->timestamp('last_driver_search_at')->nullable();
            $table->boolean('driver_search_completed')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['driver_search_attempts', 'driver_search_started_at', 'last_driver_search_at', 'driver_search_completed']);
        });
    }
};
