<?php

namespace App\Repositories;

use App\Models\Role;

class RoleRepository
{
    public function __construct(private readonly Role $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getActiveRoles(string $type)
    {
        return $this->model->where('type', $type)
            ->where('status', 1)
            ->get();
    }

    public function getAll(string $type)
    {
        return $this->model->where('type', $type)->get();
    }

    public function create(array $data)
    {
        return $this->model->create([
            'name' => $data['name'],
        ]);
    }

    public function update(Role $role, array $data): void
    {
        $role->update($data);
    }

    public function getCompanySuperAdmin()
    {
        return $this->model->find(3);
    }
}
