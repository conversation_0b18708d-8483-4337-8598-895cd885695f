<?php

namespace App\Http\Resources\Driver;

use App\Http\Resources\CountryResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'shipment_number' => $this->shipment_number,
            'cost' => $this->cost,
            'fees' => $this->fees,
            'dropoff_country' => $this->when($this->dropoff_country_id, fn (): \App\Http\Resources\CountryResource => new CountryResource($this->whenLoaded('dropoffCountry'))),
            'dropoff_address' => $this->when($this->dropoff_address_id, fn (): \App\Http\Resources\Driver\AddressResource => new AddressResource($this->whenLoaded('dropoffAddress'))),
            'dropoff_location' => $this->when($this->dropoff_location, fn (): array => [
                'lat' => (string) $this->dropoff_location->latitude,
                'lng' => (string) $this->dropoff_location->longitude,
            ]),
            'recipient_name' => $this->recipient_name,
            'country_code' => $this->country_code,
            'phone' => $this->phone,
            'description' => $this->description,
            'status' => $this->status,
            'media' => MediaResource::collection($this->whenLoaded('media')),
        ];
    }
}
