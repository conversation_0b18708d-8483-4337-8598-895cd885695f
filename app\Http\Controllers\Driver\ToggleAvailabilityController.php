<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;

class ToggleAvailabilityController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke()
    {
        $driver = auth('driver')->user();

        $driver->update([
            'is_available' => ! $driver->is_available,
        ]);

        return success($driver->is_available);
    }
}
