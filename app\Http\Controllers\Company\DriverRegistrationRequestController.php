<?php

namespace App\Http\Controllers\Company;

use App\DataTables\Company\DriverRegistrationRequestsDataTable;
use App\Http\Controllers\Controller;
use App\Services\Company\DriverRegistrationRequestService;

class DriverRegistrationRequestController extends Controller
{
    public function __construct(private readonly DriverRegistrationRequestService $driverRegistrationRequestService) {}

    public function index(DriverRegistrationRequestsDataTable $dataTable)
    {
        return $dataTable->render('pages.company.drivers.registration-requests.index');
    }

    public function show(string $id)
    {
        $driver = $this->driverRegistrationRequestService->getDriver($id);

        return view('pages.company.drivers.show', ['driver' => $driver]);
    }

    public function updateApprovalStatus(string $id)
    {
        $this->driverRegistrationRequestService->updateApprovalStatus($id);

        if (request('status') == 'on_review') {
            $driver = $this->driverRegistrationRequestService->getDriver($id);

            return response()->json([
                'message' => 'Driver registration request updated successfully',
                'approval_status' => view('pages.company.drivers.registration-requests.approval-status', ['driver' => $driver])->render(),
            ], 200);
        }

        if (request('status') == 'approved') {
            return to_route('company.drivers.assign-vehicles', $id)->with('success', 'Driver registration request updated successfully');
        }

        return to_route('company.driver-registration-requests.index')->with('success', 'Driver registration request updated successfully');
    }
}
