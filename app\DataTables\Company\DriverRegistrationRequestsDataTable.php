<?php

namespace App\DataTables\Company;

use App\Models\Driver;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class DriverRegistrationRequestsDataTable extends DataTable
{
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->setRowId('id')
            ->addColumn('approval_status', fn ($company): ?string => statusBadge($company->approval_status))
            ->addColumn('actions', fn ($driver) => view('pages.company.drivers.registration-requests.actions', ['driver' => $driver])->render())
            ->addColumn('email', fn ($driver) => $driver->email ?? '-')
            ->addColumn('created_at', fn ($driver): ?string => formatDateTime($driver->created_at))
            ->orderColumn('DT_RowIndex', function ($query, $direction): void {
                $query->orderBy('id', $direction);
            })
            ->rawColumns(['actions', 'approval_status'])
            ->addIndexColumn();
    }

    public function query(Driver $model): QueryBuilder
    {
        return $model->newQuery()
            ->where('company_id', auth('company')->user()->company_id)
            ->where('approval_status', '!=', 'approved')
            ->when(request('search_param'), function ($query): void {
                $query->where('name', 'LIKE', '%'.request('search_param').'%')
                    ->orWhereRaw('CONCAT(country_code, phone) LIKE ?', ['%'.request('search_param').'%']);
            })
            ->when(request('approval_status'), fn ($q) => $q->where('approval_status', request('approval_status')))
            ->when(request('transportion_method_id'), function ($query): void {
                $query->whereHas('shippingTypes', function ($q): void {
                    $q->whereHas('transportionMethods', fn ($q) => $q->where('transportion_methods.id', request('transportion_method_id')));
                });
            })
            ->when(request('from_date'), fn ($q) => $q->whereDate('created_at', '>=', request('from_date')))
            ->when(request('to_date'), fn ($q) => $q->whereDate('created_at', '<=', request('to_date')));
    }

    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('driver-registration-requests-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->searching(false)
            ->parameters([
                'language' => [
                    'url' => asset('assets/json/datatable-translations/'.app()->getLocale().'.json'),
                ],
            ]);
    }

    public function getColumns(): array
    {
        return [
            Column::make('DT_RowIndex')->title('#')->addClass('text-center')->searchable(false),
            Column::make('name')->title(__('Name'))->addClass('text-center')->orderable(false),
            Column::computed('email')->title(__('Email'))->addClass('text-center'),
            Column::computed('approval_status')->title(__('Status'))->addClass('text-center'),
            Column::computed('created_at')->title(__('Created At'))->addClass('text-center'),
            Column::computed('actions')->title(__('Actions'))->addClass('text-center'),
        ];
    }

    protected function filename(): string
    {
        return 'Drivers_'.date('YmdHis');
    }
}
