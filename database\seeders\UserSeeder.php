<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        for ($i = 1; $i <= 5; $i++) {
            User::Create([
                'name' => "User $i",
                'country_code' => '+971',
                'phone' => "52123456$i",
                'email' => "user$<EMAIL>",
                'birth_date' => fake()->date(),
                'gender' => fake()->randomElement(['male', 'female']),
            ]);
        }
    }
}
