<?php

namespace App\Rules;

use App\Models\Country;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberType;
use libphonenumber\PhoneNumberUtil;

class ValidPhone implements ValidationRule
{
    public function __construct(private readonly ?string $country_code = null) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $phoneUtil = PhoneNumberUtil::getInstance();

        try {
            $country = Country::where('code', $this->country_code)->first();

            if (! $country) {
                $fail(__('invalid country code'));

                return;
            }

            $numberProto = $phoneUtil->parse($value, $country->abbv);

            // Check if valid phone number
            if (! $phoneUtil->isValidNumber($numberProto)) {
                $fail(__('The phone must be a valid mobile phone number (no landlines allowed).'));

                return;
            }

            // Allow only mobile numbers
            $type = $phoneUtil->getNumberType($numberProto);

            if (! in_array($type, [PhoneNumberType::MOBILE, PhoneNumberType::FIXED_LINE_OR_MOBILE], true)) {
                $fail(__('The phone must be a valid mobile phone number (no landlines allowed).'));
            }
        } catch (NumberParseException) {
            $fail(__('The phone must be a valid mobile phone number (no landlines allowed).'));
        }
    }
}
