@php
    $startYear = 1980;
    $endYear = now()->year;
    $selectedYear = old('year', $vehicle->year ?? '');
@endphp

<div class="row">
    <x-translated-text-input name="name" label="Name" :model="$vehicle ?? null" />

    <!-- Model -->
    <div class="mb-3 col-lg-4">
        <label for="model" class="form-label"><b>{{ __('Model') }}</b></label>
        <input type="text" name="model" id="model" class="form-control"
            value="{{ old('model', $vehicle->model ?? '') }}">
        <x-input-error name="model" />
    </div>

    <!-- Year -->
    <div class="mb-3 col-lg-4">
        <label for="year" class="form-label"><b>{{ __('Year') }}</b></label>
        <select name="year" id="year" class="form-select">
            <option value="">{{ __('Select Year') }}</option>
            @for ($year = $endYear; $year >= $startYear; $year--)
                <option value="{{ $year }}" {{ $selectedYear == $year ? 'selected' : '' }}>{{ $year }}
                </option>
            @endfor
        </select>
        <x-input-error name="year" />
    </div>

    <!-- Company -->
    <div class="mb-3 col-lg-4">
        <label for="company_id" class="form-label"><b>{{ __('Company') }}</b></label>
        <select name="company_id" id="company_id" class="select2 form-select form-select-lg" data-allow-clear="true">
            <option disabled selected>{{ __('Choose') }}...</option>
            @foreach ($companies as $company)
                <option value="{{ $company->id }}" @selected(old('company_id', $vehicle->company_id ?? '') == $company->id)>
                    {{ $company->name }}
                </option>
            @endforeach
        </select>
        <x-input-error name="company_id" />
    </div>

    <!-- Transportation Method -->
    <div class="mb-3 col-lg-4">
        <label for="transportion_method_id" class="form-label"><b>{{ __('Transportation Method') }}</b></label>
        <select name="transportion_method_id" id="transportion_method_id" class="select2 form-select form-select-lg"
            data-allow-clear="true">
            <option disabled selected>{{ __('Choose') }}...</option>
        </select>
        <x-input-error name="transportion_method_id" />
    </div>

    <!-- Plate Number -->
    <div class="mb-3 col-lg-4">
        <label for="plate_number" class="form-label"><b>{{ __('Plate Number') }}</b></label>
        <input type="text" name="plate_number" id="plate_number" class="form-control"
            value="{{ old('plate_number', $vehicle->plate_number ?? '') }}">
        <x-input-error name="plate_number" />
    </div>

    <!-- License Number -->
    <div class="mb-3 col-lg-4">
        <label for="license_number" class="form-label"><b>{{ __('License Number') }}</b></label>
        <input type="text" name="license_number" id="license_number" class="form-control"
            value="{{ old('license_number', $vehicle->license_number ?? '') }}">
        <x-input-error name="license_number" />
    </div>

    <!-- License Expiration Date -->
    <div class="mb-3 col-lg-4">
        <label for="license_expiration_date" class="form-label"><b>{{ __('License Expiration Date') }}</b></label>
        <input type="date" name="license_expiration_date" id="license_expiration_date"
            class="form-control flatpickr-input"
            value="{{ old('license_expiration_date', $vehicle->license_expiration_date ?? '') }}" readonly="readonly">
        <x-input-error name="license_expiration_date" />
    </div>

    <div class="mb-4 col-lg-4">
        <label for="formFile" class="form-label"><b>{{ __('Image') }}</b></label>
        <input name="image" class="form-control" type="file" id="formFile">
        <x-input-error name="image" />

        @if (isset($vehicle) && $vehicle->photo)
            <label class="form-label mt-4"><b>{{ __('Current Image') }}</b></label>
            <div class="mt-1">
                <a href="{{ $vehicle->photo->url }}" target="_blank">
                    <img src="{{ $vehicle->photo->url }}" alt="Vehicle Image" class="card-image">
                </a>
            </div>
        @endif
    </div>

    <div class="mb-4 col-lg-4">
        <label for="registration_license" class="form-label"><b>{{ __('Registration License') }}</b></label>
        <input name="registration_license" class="form-control" type="file" id="registration_license">
        <x-input-error name="registration_license" />

        @if (isset($vehicle) && $vehicle->registrationLicense)
            <label class="form-label mt-4"><b>{{ __('Current Registration License') }}</b></label>
            <div class="mt-1">
                <a href="{{ $vehicle->registrationLicense->url }}" target="_blank">
                    <img src="{{ $vehicle->registrationLicense->url }}" alt="Registration License" class="card-image">
                </a>
            </div>
        @endif
    </div>

    <div class="mb-4 col-lg-4">
        <label for="driving_license" class="form-label"><b>{{ __('Driving License') }}</b></label>
        <input name="driving_license" class="form-control" type="file" id="driving_license">
        <x-input-error name="driving_license" />

        @if (isset($vehicle) && $vehicle->drivingLicense)
            <label class="form-label mt-4"><b>{{ __('Current Driving License') }}</b></label>
            <div class="mt-1">
                <a href="{{ $vehicle->drivingLicense->url }}" target="_blank">
                    <img src="{{ $vehicle->drivingLicense->url }}" alt="Driving License" class="card-image">
                </a>
            </div>
        @endif
    </div>

    <div class="mb-4 col-lg-4">
        <label for="insurance_policy" class="form-label"><b>{{ __('Insurance Policy') }}</b></label>
        <input name="insurance_policy" class="form-control" type="file" id="insurance_policy">
        <x-input-error name="insurance_policy" />

        @if (isset($vehicle) && $vehicle->insurancePolicy)
            <label class="form-label mt-4"><b>{{ __('Current Insurance Policy') }}</b></label>
            <div class="mt-1">
                <a href="{{ $vehicle->insurancePolicy->url }}" target="_blank">
                    <img src="{{ $vehicle->insurancePolicy->url }}" alt="Insurance Policy" class="card-image">
                </a>
            </div>
        @endif
    </div>

    <div class="mb-3 col-lg-4">
        <label style="margin-bottom:15px" class="form-label"><b>{{ __('Status') }}</b></label><br>
        <label class="switch">
            <input type="hidden" name="status" value="inactive">
            <input type="checkbox" class="switch-input" name="status" value="active" @checked(old('status', $vehicle->status ?? 'active') == 'active')
                @disabled(isset($vehicle) && $vehicle->approval_status === 'pending')>
            <span class="switch-toggle-slider">
                <span class="switch-on"></span>
                <span class="switch-off"></span>
            </span>
            <span class="switch-label">{{ __('active') }}</span>
        </label>
        <x-input-error name="status" />
    </div>
</div>
