<?php

namespace App\Repositories;

use App\Models\CompanyShippingType;
use App\Models\CompanyShippingTypeSize;

class CompanyShippingTypeSizeRepository
{
    public function __construct(private readonly CompanyShippingTypeSize $model) {}

    public function create(CompanyShippingType $company_shipping_type, string $shipping_size_id)
    {
        return $this->model->create([
            'company_shipping_type_id' => $company_shipping_type->id,
            'shipping_size_id' => $shipping_size_id,
        ]);
    }
}
