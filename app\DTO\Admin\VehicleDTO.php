<?php

namespace App\DTO\Admin;

use Illuminate\Http\UploadedFile;
use Spa<PERSON>\LaravelData\Data;

class VehicleDTO extends Data
{
    public function __construct(
        public array $name,
        public string $model,
        public int $year,
        public int $company_id,
        public int $transportion_method_id,
        public string $plate_number,
        public string $license_number,
        public string $license_expiration_date,
        public ?UploadedFile $image,
        public ?UploadedFile $registration_license,
        public ?UploadedFile $driving_license,
        public ?UploadedFile $insurance_policy,
        public string $status = 'active',
        public string $approval_status = 'approved'
    ) {
        //
    }
}
