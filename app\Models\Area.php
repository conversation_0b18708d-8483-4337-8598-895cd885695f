<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use MatanYadaev\EloquentSpatial\Objects\Polygon;
use MatanYadaev\EloquentSpatial\Traits\HasSpatial;
use Spatie\Translatable\HasTranslations;

class Area extends Model
{
    use HasSpatial, HasTranslations, SoftDeletes;

    protected $fillable = [
        'city_id',
        'name',
        'status',
        'area',
    ];

    public array $translatable = ['name'];

    protected $casts = [
        'area' => Polygon::class,
    ];

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    public function drivers(): BelongsToMany
    {
        return $this->belongsToMany(Driver::class, 'driver_areas');
    }

    public function addresses(): HasMany
    {
        return $this->hasMany(Address::class);
    }
}
