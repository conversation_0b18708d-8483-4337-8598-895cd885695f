<?php

namespace App\Services\Admin;

use App\Models\Company;
use App\Repositories\CompanyRepository;
use App\Services\MediaService;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class CompanyRegistrationRequestService
{
    public function __construct(private readonly CompanyRepository $companyRepository, private readonly MediaService $mediaService) {}

    public function getCompany(string $id)
    {
        $company = $this->companyRepository->getById($id);

        if (! $company || $company->approval_status == 'approved') {
            abort(404);
        }

        return $company;
    }

    public function updateApprovalStatus(string $id): void
    {
        $company = $this->companyRepository->getById($id);

        if (! $company || $company->approval_status == 'approved') {
            throw new BadRequestHttpException('Company is already approved');
        }

        if (request('status') == 'on_review') {
            $this->updateToOnReview($company);
        }

        if (request('status') == 'approved') {
            $this->updateToApproved($company);
        }

        if (request('status') == 'rejected') {
            $this->updateToRejected($company);
        }
    }

    public function updateToOnReview(Company $company): void
    {
        if ($company->approval_status != 'pending') {
            throw new BadRequestHttpException('Registration request is not pending');
        }

        $this->companyRepository->update($company, ['approval_status' => 'on_review']);
    }

    public function updateToApproved(Company $company): void
    {
        if ($company->approval_status != 'on_review') {
            throw new BadRequestHttpException('Registration request is not on review');
        }

        $this->companyRepository->update($company, ['approval_status' => 'approved']);

        // Send Mail To Super Admin
    }

    public function updateToRejected(Company $company): void
    {
        if ($company->approval_status != 'on_review') {
            throw new BadRequestHttpException('Registration request is not on review');
        }

        DB::transaction(function () use ($company): void {
            $company->superAdmin->forceDelete();
            $company->forceDelete();
            $this->mediaService->deleteAllMedia($company);
        });
    }
}
