<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class AreasRequiredWithImmediateShipping implements ValidationRule
{
    public function __construct(private array $shippingOptions) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!preg_match('/shipping_options\.(\d+)\.cities\.(\d+)\.id/', $attribute, $matches)) {
            return;
        }

        [$full, $shippingIndex, $cityIndex] = $matches;

        $shippingTypeId = $this->shippingOptions[$shippingIndex]['shipping_type_id'] ?? null;
        $areas = $this->shippingOptions[$shippingIndex]['cities'][$cityIndex]['areas'] ?? [];

        if ($shippingTypeId == 1 && empty($areas)) {
            $fail('Areas required in case of immediate shipping');
        }
    }
}
