<?php

namespace App\Http\Controllers\Company\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Company\RegistrationRequest;
use App\Http\Requests\Company\RegistrationStepperRequest;
use App\Services\Company\RegistrationService;

class RegistrationController extends Controller
{
    public function __construct(private readonly RegistrationService $registrationService) {}

    public function store(RegistrationRequest $request)
    {
        $this->registrationService->register();

        return successMessage('registration request has been sent successfully. please wait for approval');
    }

    public function stepperValidation(RegistrationStepperRequest $request): array
    {
        return ['status' => 'valid'];
    }
}
