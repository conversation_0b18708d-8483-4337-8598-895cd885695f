<?php

namespace App\Services\User;

use App\Models\User;
use App\Repositories\OTPRepository;
use App\Repositories\UserRepository;
use App\Services\Admin\UserService;
use Carbon\Carbon;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class ProfileService
{
    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly UserService $userService,
        private readonly OTPRepository $otpRepository
    ) {}

    public function update(array $data)
    {
        $user = auth('user')->user();

        $this->userRepository->update($user, $data);
    }

    public function sendUpdatePhoneOtp()
    {
        $country_code = request('country_code');
        $phone = normalizeMobileNumber(request('phone'));

        if (RateLimiter::tooManyAttempts("otp-request-user:$country_code-$phone", 3)) {
            $seconds = RateLimiter::availableIn("otp-request-user:$country_code-$phone");
            $remainingTime = formatSecondsToHoursTime($seconds);

            throw new ThrottleRequestsException(__('limit reached: retry after :time hours', ['time' => $remainingTime]));
        }

        RateLimiter::hit("otp-request-user:$country_code-$phone", 60 * 60 * 12);

        $code = generatePassCode();

        $this->otpRepository->create(User::class, $country_code, $phone, $code);
    }

    public function confirmUpdatePhone(array $data)
    {
        $user = auth('user')->user();

        $phone = normalizeMobileNumber(request('phone'));
        $country_code = request('country_code');
        $code = request('code');

        if (RateLimiter::tooManyAttempts("confirm-otp-user:$country_code-$phone", 3)) {
            $seconds = RateLimiter::availableIn("confirm-otp-user:$country_code-$phone");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw new ThrottleRequestsException(__('too many attempts: retry after :time minutes', ['time' => $remainingTime]));
        }

        RateLimiter::hit("confirm-otp-user:$country_code-$phone", 60);

        $otp = $this->otpRepository->get(User::class, $country_code, $phone, $code);

        if (! $otp) {
            throw new BadRequestHttpException(__('invalid code'));
        }

        $latest_otp = $this->otpRepository->getLatest(User::class, $country_code, $phone);

        if ($latest_otp->code != $code) {
            throw new BadRequestHttpException(__('please enter the latest sent code'));
        }

        // expire code after 1 hour
        if (Carbon::parse($latest_otp->created_at)->addHour()->lt(now())) {
            throw new BadRequestHttpException(__('expired code'));
        }

        // Update Phone Number
        $this->userRepository->update($user, $data);
    }

    public function delete()
    {
        $user = auth('user')->user();

        $this->userService->delete($user);
    }

    public function updateAppLocale()
    {
        $user = auth('user')->user();

        $this->userRepository->update($user, [
            'app_locale' => request('app_locale')
        ]);
    }

    public function updatePushNotificationStatus()
    {
        $user = auth('user')->user();

        $this->userRepository->update($user, [
            'push_notifications_enabled' => !$user->push_notifications_enabled,
        ]);

        return $user->push_notifications_enabled;
    }
}
