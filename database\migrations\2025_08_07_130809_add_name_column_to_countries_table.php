<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add JSON name column to countries table
        Schema::table('countries', function (Blueprint $table) {
            $table->json('name')->nullable()->after('id');
        });

        // Migrate data from country_translations to countries
        $countries = DB::table('countries')->get();
        foreach ($countries as $country) {
            $translations = DB::table('country_translations')
                ->where('country_id', $country->id)
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->locale => $item->name];
                })
                ->toArray();

            DB::table('countries')
                ->where('id', $country->id)
                ->update(['name' => json_encode($translations)]);
        }

        // Drop translation table
        Schema::dropIfExists('country_translations');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate translation table
        Schema::create('country_translations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('country_id')->constrained()->cascadeOnDelete();
            $table->string('locale')->index();
            $table->unique(['country_id', 'locale']);
        });

        // Migrate data back from JSON to translation table
        $countries = DB::table('countries')->get();
        foreach ($countries as $country) {
            if ($country->name) {
                $translations = json_decode($country->name, true);
                foreach ($translations as $locale => $name) {
                    DB::table('country_translations')->insert([
                        'country_id' => $country->id,
                        'locale' => $locale,
                        'name' => $name,
                    ]);
                }
            }
        }

        // Drop JSON column
        Schema::table('countries', function (Blueprint $table) {
            $table->dropColumn('name');
        });
    }
};
