<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\Driver;
use App\Models\DriverImmediateShippingCity;
use App\Models\DriverShippingType;
use Illuminate\Database\Seeder;

class DriverSeeder extends Seeder
{
    public function run(): void
    {
        for ($i = 1; $i <= 5; $i++) {
            $company = Company::inRandomOrder()->first();

            $driver = Driver::create([
                'name' => "Driver $i",
                'country_code' => '+971',
                'phone' => "********$i",
                'email' => "driver$<EMAIL>",
                'birth_date' => fake()->optional()->date(),
                'gender' => fake()->optional()->randomElement(['male', 'female']),
                'company_id' => $company->id,
                'id_number' => fake()->randomNumber(4, true) . fake()->randomNumber(6, true),
                'bank_name' => fake()->company(),
                'bank_account_owner' => "Driver $i",
                'bank_account_number' => fake()->bankAccountNumber(),
                'iban' => fake()->optional()->iban('EG'),
            ]);

            // Get company's shipping types
            $companyShippingTypes = $company->shippingTypes()->pluck('shipping_type_id')->toArray();

            // Ensure at least 2 shipping types are selected (or all if less than 2 available)
            $numShippingTypes = max(2, count($companyShippingTypes));
            $selectedShippingTypeIds = fake()->randomElements($companyShippingTypes, fake()->numberBetween(2, $numShippingTypes));

            foreach ($selectedShippingTypeIds as $shippingTypeId) {
                // Create DriverShippingType entry
                $driverShippingType = DriverShippingType::create([
                    'driver_id' => $driver->id,
                    'shipping_type_id' => $shippingTypeId,
                ]);

                // Get transportation methods for this shipping type from the company
                $transportationMethods = $company->shippingTypes()
                    ->where('shipping_type_id', $shippingTypeId)
                    ->first()
                    ->transportionMethods()
                    ->pluck('transportion_method_id')
                    ->toArray();

                // Attach random transportation methods (at least 1, up to all available)
                $selectedTransportationMethods = fake()->randomElements($transportationMethods, fake()->numberBetween(1, count($transportationMethods)));
                $driverShippingType->transportionMethods()->attach($selectedTransportationMethods);

                if ($shippingTypeId == 2) { // Intercity Shipping
                    $intercityCities = $company->intercityShippingPickupCities()->pluck('city_id')->toArray();
                    $intercityCities = array_merge($intercityCities, $company->intercityShippingDeliveryCities()->pluck('city_id')->toArray());
                    $intercityCities = array_unique($intercityCities);
                    if (! empty($intercityCities)) {
                        $selectedCities = fake()->randomElements($intercityCities, fake()->numberBetween(1, count($intercityCities)));
                        foreach ($selectedCities as $cityId) {
                            $driver->intercityShippingCities()->attach($cityId, ['shipping_type' => 'intercity']);
                        }
                    }
                } elseif ($shippingTypeId == 1) { // Immediate Shipping
                    $immediateCities = $company->immediateShippingCities()->pluck('city_id')->toArray();
                    if (! empty($immediateCities)) {
                        $selectedCities = fake()->randomElements($immediateCities, fake()->numberBetween(1, count($immediateCities)));
                        foreach ($selectedCities as $cityId) {
                            $driverImmediateCity = DriverImmediateShippingCity::create([
                                'driver_id' => $driver->id,
                                'city_id' => $cityId,
                            ]);

                            // Attach random areas for the selected city
                            $cityAreas = $company->immediateShippingCities()
                                ->where('city_id', $cityId)
                                ->first()
                                ->areas()
                                ->pluck('area_id')
                                ->toArray();
                            if (! empty($cityAreas)) {
                                $selectedAreas = fake()->randomElements($cityAreas, fake()->numberBetween(1, count($cityAreas)));
                                $driverImmediateCity->cityAreas()->createMany(
                                    array_map(fn($areaId) => ['area_id' => $areaId], $selectedAreas)
                                );
                            }
                        }
                    }
                }
            }
        }
    }
}
