<x-layout :title="__('Admins')">
    <div class="card shadow-sm rounded-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ __('Admin') }}: {{ $admin->name }}</h5>
            @if(auth('admin')->user()->hasPermission('update admin'))
            <a class="btn btn-sm btn-outline-primary" href="{{ route('admin.admins.edit', $admin->id) }}">
                <i class="fas fa-edit me-1"></i> {{ __('Edit') }}
            </a>
            @endif
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Name') }}</div>
                <div class="col-sm-9">{{ $admin->name }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Email') }}</div>
                <div class="col-sm-9">{{ $admin->email }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Phone') }}</div>
                <div class="col-sm-9">{!! formatPhone($admin->country_code, $admin->phone) !!}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Status') }}</div>
                <div class="col-sm-9">{!! statusBadge($admin->status) !!}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Created At') }}</div>
                <div class="col-sm-9" dir="ltr">{{ formatDateTime($admin->created_at) }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Role') }}</div>
                <div class="col-sm-9" dir="ltr">{{ $admin->role->name }}</div>
            </div>
            <div class="row mb-3">
            <div class="col-sm-3 text-muted">{{ __('Permissions') }}</div>
            <div class="col-sm-9"></div>
            <div class="col-sm-9 mt-3">
                @foreach($permissions as $group => $perms)
                <div class="mb-3">
                    <h6 class="fw-bold border-bottom pb-2 mb-2" style="font-size: 0.85rem;">
                        {{ __(ucwords($group)) }}
                    </h6>
                    <div class="row">
                        @foreach($perms as $permission)
                        <div class="col-md-4 col-sm-6 mb-2">
                            <div class="form-check">
                                <input
                                    type="checkbox"
                                    class="form-check-input"
                                    id="permission-{{ $permission->id }}"
                                    value="{{ $permission->id }}"
                                    @checked(in_array($permission->id, $admin->permissions->pluck('id')->toArray()))
                                    disabled
                                >
                                <label class="form-check-label" for="permission-{{ $permission->id }}">
                                    {{ __(explode(' ', $permission->name, 2)[0]) . ' ' . __(explode(' ', $permission->name, 2)[1]) }}
                                </label>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endforeach
            </div>
        </div>

        </div>
    </div>
</x-layout>