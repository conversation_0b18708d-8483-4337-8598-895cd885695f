@if (session('success') || session('fail'))
    @push('js')
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                @if (session('success'))
                    Swal.fire({
                        toast: true,
                        position: 'top-end',
                        icon: 'success',
                        title: @json(session('success')),
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        background: '#198754', // success green
                        color: '#fff',
                        customClass: {
                            popup: 'custom-toast',
                            timerProgressBar: 'custom-timer-bar'
                        }
                    });
                @endif

                @if (session('fail'))
                    Swal.fire({
                        toast: true,
                        position: 'top-end',
                        icon: 'error',
                        title: @json(session('fail')),
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        background: '#DC3545', // danger red
                        color: '#fff',
                        customClass: {
                            popup: 'custom-toast',
                            timerProgressBar: 'custom-timer-bar'
                        }
                    });
                @endif
            });
        </script>
    @endpush
@endif
