<?php

namespace App\DataTables\Company;

use App\Models\Order;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class OrdersDataTable extends DataTable
{
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->setRowId('id')
            ->addColumn('actions', fn ($order) => view('pages.company.orders.actions', ['order' => $order])->render())
            ->addColumn('shipping_type', fn ($order) => $order->shippingType?->name)
            ->addColumn('shipping_method', fn ($order): string => $order->shipping_type_id != 1 ? ($order->is_express ? 'Express' : 'Standard') : '-')
            ->addColumn('shipping_size', fn ($order) => $order->shippingSize->name)
            ->addColumn('status', fn ($order) => __(ucfirst(str_replace('_', ' ', $order->status->value))))
            ->addColumn('created_at', fn ($order): ?string => formatDateTime($order->created_at))
            ->orderColumn('DT_RowIndex', function ($query, $direction): void {
                $query->orderBy('id', $direction);
            })
            ->rawColumns(['actions', 'status'])
            ->addIndexColumn();
    }

    public function query(Order $model): QueryBuilder
    {
        return $model->newQuery()
            ->whereHas('driver', function ($query): void {
                $query->where('company_id', auth('company')->user()->company_id);
            })
            ->with(['shippingType', 'shippingSize'])
            ->when(request('search_param'), function ($query): void {
                $query->where('order_number', 'LIKE', '%'.request('search_param').'%')
                    ->orWhereHas('user', function ($q): void {
                        $q->where('name', 'LIKE', '%'.request('search_param').'%');
                    });
            })
            ->when(request('shipping_type_id'), fn ($q) => $q->where('shipping_type_id', request('shipping_type_id')))
            ->when(request('shipping_size_id'), fn ($q) => $q->where('shipping_size_id', request('shipping_size_id')))
            ->when(request()->has('is_express') && request('is_express') != '', fn ($q) => $q->where('is_express', request('is_express')))
            ->when(request('status'), fn ($q) => $q->where('status', request('status')))
            ->when(request('from_date'), fn ($q) => $q->whereDate('created_at', '>=', request('from_date')))
            ->when(request('to_date'), fn ($q) => $q->whereDate('created_at', '<=', request('to_date')));
    }

    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('orders-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->searching(false)
            ->parameters([
                'language' => [
                    'url' => asset('assets/json/datatable-translations/'.app()->getLocale().'.json'),
                ],
            ]);
    }

    public function getColumns(): array
    {
        return [
            Column::make('DT_RowIndex')->title('#')->addClass('text-center')->searchable(false),
            Column::make('order_number')->title(__('Order Number'))->addClass('text-center'),
            Column::computed('created_at')->title(__('Date'))->addClass('text-center'),
            Column::computed('shipping_type')->title(__('Shipping Type'))->addClass('text-center'),
            Column::computed('shipping_method')->title(__('Shipping Method'))->addClass('text-center'),
            Column::computed('shipping_size')->title(__('Shipping Size'))->addClass('text-center'),
            Column::computed('status')->title(__('Status'))->addClass('text-center'),
            Column::computed('actions')->title(__('Actions'))->addClass('text-center'),
        ];
    }

    protected function filename(): string
    {
        return 'Orders_'.date('YmdHis');
    }
}
