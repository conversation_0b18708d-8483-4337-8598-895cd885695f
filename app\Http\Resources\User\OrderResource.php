<?php

namespace App\Http\Resources\User;

use App\Enum\OrderStatus;
use App\Repositories\ChatRepository;
use App\Services\User\OrderService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $pickupPoint = app(OrderService::class)->resolvePickupPoint($this->resource);

        return [
            'id' => $this->id,
            'order_number' => $this->order_number,
            'payment_method' => $this->payment_method,
            'status' => $this->status,
            'cancel_reason' => $this->when($this->status == OrderStatus::CANCELLED->value, $this->cancel_reason),
            'is_express' => $this->is_express,
            'shipping_type' => [
                'id' => $this->shippingType->id,
                'name' => __($this->shippingType->name . " shipping"),
            ],
            'shipping_size' => [
                'id' => $this->shippingSize->id,
                'name' =>  __($this->shippingSize->name),
            ],
            'transportion_method' => $this->transportionMethod?->name,
            'time' => formatDateTime($this->created_at),
            'items_count' => $this->items_count,
            'items' => OrderItemResource::collection($this->whenLoaded('items')),
            'pickup_address' => $this->when(
                $this->pickup_address_id,
                fn(): \App\Http\Resources\User\AddressResource => new AddressResource($this->whenLoaded('pickupAddress'))
            ),
            'pickup_location' => $this->when(
                $this->pickup_location,
                fn(): array => [
                    'lat' => (string) $this->pickup_location->latitude,
                    'lng' => (string) $this->pickup_location->longitude,
                ]
            ),
            'price_summary' => [
                'delivery_cost' => $this->cost,
                'fees' => $this->fees,
                'total' => $this->total,
            ],
            'driver' => [
                'id' => $this->driver?->id,
                'country_code' => $this->driver?->country_code,
                'phone' => $this->driver?->phone,
                'name' => $this->driver?->name,
                'image' => $this->driver?->profileImage?->url,
                'location' => [
                    'lat' => (string) $this->driver?->current_lat,
                    'lng' => (string) $this->driver?->current_lng,
                ],
            ],
            'vehicle' => [
                'model' => $this->vehicle?->name,
                'year' => $this->vehicle?->year,
                'image' => $this->vehicle?->photo?->url,
            ],
            'estimated_arrival_time' => ($this->driver_id && $this->driver->current_lat && $this->driver->current_lng) ? estimateTimeFromDistance($pickupPoint->latitude, $pickupPoint->longitude, $this->driver->current_lat, $this->driver->current_lng) : null,
            'chat_id' => app(ChatRepository::class)->firstOrCreate($this->resource)->id,
        ];
    }
}
