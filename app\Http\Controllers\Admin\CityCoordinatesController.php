<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\City;
use Illuminate\Http\JsonResponse;

class CityCoordinatesController extends Controller
{
    public function getCoordinates(City $city): JsonResponse
    {
        try {
            $coordinates = null;
            if ($city->area) {
                $coordinates = $city->area->getCoordinates();
                \Log::info('City coordinates retrieved:', [
                    'city_id' => $city->id,
                    'coordinates' => $coordinates,
                ]);
            } else {
                \Log::warning('City has no area defined:', ['city_id' => $city->id]);
            }

            return response()->json([
                'success' => true,
                'coordinates' => $coordinates,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error getting city coordinates:', [
                'city_id' => $city->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Error retrieving city coordinates',
            ], 500);
        }
    }
}
