<?php

namespace App\Repositories;

use App\Models\Driver;
use App\Models\DriverShippingType;

class DriverShippingTypeRepository
{
    public function __construct(private readonly DriverShippingType $model) {}

    public function create(Driver $driver, string $shipping_type_id)
    {
        return $this->model->create([
            'driver_id' => $driver->id,
            'shipping_type_id' => $shipping_type_id,
        ]);
    }
}
