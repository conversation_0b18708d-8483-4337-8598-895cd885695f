<?php

namespace App\Services;

use MatanYadaev\EloquentSpatial\Objects\Polygon;

class GeometryService
{
    public function __construct() {}

    public function getPolygonPoints(Polygon $polygon): array
    {
        $points = [];

        foreach ($polygon->getCoordinates()[0] as $point) {
            $points[] = [
                'lat' => (string) $point[1],
                'lng' => (string) $point[0]
            ];
        }

        return $points;
    }
}
