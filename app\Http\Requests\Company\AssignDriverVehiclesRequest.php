<?php

namespace App\Http\Requests\Company;

use App\Http\Requests\BaseFormRequest;

class AssignDriverVehiclesRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'vehicles' => ['array'],
            'vehicles.*' => ['required', 'exists:vehicles,id'],
        ];
    }

    protected function prepareForValidation()
    {
        // If vehicles is null, set it to an empty array
        $this->merge([
            'vehicles' => $this->input('vehicles') ?? [],
        ]);
    }
}
