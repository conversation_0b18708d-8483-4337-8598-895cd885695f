<?php

namespace App\Services\User;

use App\DTO\User\OrderDTO;
use App\Enum\OrderDriverActionStatus;
use App\Enum\OrderStatus;
use App\Events\NewOrder;
use App\Http\Resources\User\AddressResource;
use App\Models\Driver;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Repositories\DriverRatingRepository;
use App\Repositories\DriverRepository;
use App\Repositories\OrderItemRepository;
use App\Repositories\OrderRepository;
use App\Services\MediaService;
use App\Services\MpdfService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class OrderService
{
    public function __construct(
        private readonly OrderRepository $orderRepository,
        private readonly OrderItemRepository $orderItemRepository,
        private readonly MediaService $mediaService,
        private readonly DriverRepository $driverRepository,
        private readonly DriverRatingRepository $driverRatingRepository,
    ) {
        //
    }

    private function generateShipmentNumber(): string
    {
        do {
            $number = strtoupper(substr(md5(uniqid()), 0, 8));
        } while ($this->orderItemRepository->shipmentNumberExists($number));

        return $number;
    }

    public function create(OrderDTO $dto, array $items): Order
    {
        return DB::transaction(function () use ($dto, $items) {
            $data = $this->prepareOrderData($dto);

            $order = $this->orderRepository->create($data);

            foreach ($items as $itemData) {
                $this->processOrderItem($order->id, $itemData);
            }

            return $order->refresh()->load(['items', 'pickupCountry', 'pickupAddress', 'items.dropoffAddress', 'items.dropoffCountry', 'items.media']);
        });
    }

    private function prepareOrderData(OrderDTO $dto): array
    {
        $data = $dto->toArray();
        $data['user_id'] = auth('user')->id();
        $data['order_number'] = $this->generateShipmentNumber();

        if (! empty($data['pickup_location'])) {
            $data['pickup_location'] = $this->makePoint(
                $data['pickup_location']['lat'],
                $data['pickup_location']['lng']
            );
        }

        return $data;
    }

    private function processOrderItem(int $orderId, array $itemData): void
    {
        $itemData['shipment_number'] = $this->generateShipmentNumber();
        $itemData['order_id'] = $orderId;

        if (! empty($itemData['dropoff_location'])) {
            $itemData['dropoff_location'] = $this->makePoint(
                $itemData['dropoff_location']['lat'],
                $itemData['dropoff_location']['lng']
            );
        }

        $orderItem = $this->orderItemRepository->create($itemData);

        $this->handleItemMedia($orderItem, $itemData['images'] ?? []);
    }

    private function handleItemMedia(\Illuminate\Database\Eloquent\Model $orderItem, array $images): void
    {
        foreach ($images as $image) {
            $this->mediaService->save($orderItem, $image, 'order_items');
        }
    }

    private function makePoint(float $lat, float $lng): Point
    {
        return new Point($lat, $lng);
    }

    public function assignDriversToOrder(int $orderId): bool
    {
        $order = $this->orderRepository->findById($orderId);

        if (! $order) {
            throw new NotFoundHttpException('Order not found');
        }

        if ($order->driver_id) {
            throw new BadRequestHttpException('Order already assigned to a driver');
        }

        $pickupPoint = $this->resolvePickupPoint($order);
        if (! $pickupPoint instanceof \MatanYadaev\EloquentSpatial\Objects\Point) {
            return false;
        }

        $drivers = $this->driverRepository->getNearbyDrivers(
            $order,
            $pickupPoint
        );

        if (empty($drivers)) {
            return false;
        }

        $order->driverActions()->createMany(
            $drivers->map(fn ($driver): array => [
                'driver_id' => $driver->id,
            ])->toArray()
        );

        $drivers->each->update(['is_available' => false]);

        event(new NewOrder($order));

        return true;
    }

    public function resolvePickupPoint(Order $order): ?Point
    {
        if ($order->pickup_location instanceof Point) {
            return $order->pickup_location;
        }

        $address = $order->pickupAddress;

        return ($address && $address->location instanceof Point)
            ? $address->location
            : null;
    }

    public function resolveDropoffPoint(Order $order): ?Point
    {
        $item = $order->items->first();

        if ($item->dropoff_location && ! $item->dropoff_address_id) {
            return $item->dropoff_location;
        }

        if ($item->relationLoaded('dropoffAddress') && $item->dropoffAddress) {
            return $item->dropoffAddress->location;
        }

        return null;
    }

    public function show(string $id)
    {
        $order = $this->orderRepository->findById($id);

        if (! $order || $order->user_id != auth('user')->id()) {
            throw new NotFoundHttpException('Order not found');
        }

        return $order->load(['items', 'pickupAddress', 'items.dropoffAddress', 'items.media'])
            ->loadCount('items');
    }

    public function acceptDriver(string $orderId, string $driverId): bool
    {
        $order = $this->orderRepository->findById($orderId);
        $driver = $this->driverRepository->findById($driverId);

        if (! $order || $order->user_id != auth('user')->id()) {
            throw new NotFoundHttpException('Order not found');
        }

        if (! $driver) {
            throw new NotFoundHttpException('Driver not found');
        }

        if (! $order->driverActions()->where('driver_id', $driver->id)->where('status', OrderDriverActionStatus::ACCEPTED)->exists()) {
            throw new NotFoundHttpException('Driver has not accepted the order');
        }

        $order->driver_id = $driver->id;
        $order->vehicle_id = $driver->activeVehicle->vehicle_id;
        $order->transportion_method_id = $driver->activeVehicle->vehicle->transportionMethod->id;
        $order->save();

        return true;
    }

    public function complete(string $orderId, array $data): bool
    {
        $order = $this->orderRepository->findById($orderId);

        if (! $order) {
            throw new NotFoundHttpException('Order not found');
        }

        if (! $order->driver_id) {
            throw new BadRequestHttpException('Order is not ready to be completed');
        }

        if ($order->payment_method) {
            throw new BadRequestHttpException('Order already has a payment method.');
        }

        $order->update([
            'payment_method' => $data['payment_method'],
        ]);

        return true;
    }

    public function cancel(string $orderId): bool
    {
        $order = $this->orderRepository->findById($orderId);

        if (! $order || $order->user_id != auth('user')->id()) {
            throw new NotFoundHttpException('Order not found');
        }

        if (! in_array($order->status->value, [OrderStatus::READY_TO_PICKUP->value, OrderStatus::HEADING_TO_PICKUP->value, OrderStatus::ARRIVED_AT_PICKUP->value])) {
            throw new BadRequestHttpException('Order cannot be cancelled');
        }

        DB::transaction(function () use ($order): void {
            $this->orderRepository->update($order, ['status' => OrderStatus::CANCELLED->value]);

            if ($order->driver_id) {
                $this->driverRepository->update($order->driver, ['is_available' => true]);
            }
        });

        return true;
    }

    public function rateDriver(string $orderId): bool
    {
        $order = $this->orderRepository->findById($orderId);

        if (! $order || $order->user_id !== auth('user')->id()) {
            throw new NotFoundHttpException('Order not found');
        }

        if (! in_array($order->status->value, [
            OrderStatus::DELIVERED->value,
            OrderStatus::DELIVERED_TO_LOCAL_HUB->value,
            OrderStatus::PARTIALLY_DELIVERED->value,
        ])) {
            throw new BadRequestHttpException('Order has not been delivered');
        }

        $this->driverRatingRepository->create([
            'ratable_type' => $order::class,
            'ratable_id' => $order->id,
            'driver_id' => $order->driver_id,
            'rating' => request('rating'),
            'comment' => request('comment'),
        ]);

        return true;
    }

    public function userCanActOnOrder(Order|OrderItem $actable, User $user): bool
    {
        $user_phone = $user->country_code.$user->phone;
        $recipent_phone = $actable->country_code.normalizePhoneNumber($actable->phone);

        if ($actable::class == Order::class && $actable->user_id != $user->id) { // sender user
            return false;
        }

        if ($actable::class == OrderItem::class && $recipent_phone != $user_phone) { // recipient user
            return false;
        }

        return true;
    }

    public function driverCanActOnOrder(Order|OrderItem $actable, Driver $driver): bool
    {
        if ($actable->driver_id != $driver->id && $actable->order?->driver_id != $driver->id) {
            return false;
        }

        return true;
    }

    public function resolvePickupLocation($order): mixed
    {
        if ($order->pickup_location && ! $order->pickup_address_id) {
            return [
                'lat' => $order->pickup_location->latitude,
                'lng' => $order->pickup_location->longitude,
            ];
        }

        if ($order->relationLoaded('pickupAddress') && $order->pickupAddress) {
            return new AddressResource($order->pickupAddress);
        }

        return null;
    }

    public function resolveDropoffLocation($order): mixed
    {
        if (! $order->is_express) {
            // TODO: return hub location assigned to driver
            return null;
        }

        if (! $order->relationLoaded('items') || $order->items->count() !== 1) {
            return null;
        }

        $item = $order->items->first();

        if ($item->dropoff_location && ! $item->dropoff_address_id) {
            return [
                'lat' => $item->dropoff_location->latitude,
                'lng' => $item->dropoff_location->longitude,
            ];
        }

        if ($item->relationLoaded('dropoffAddress') && $item->dropoffAddress) {
            return new AddressResource($item->dropoffAddress);
        }

        return null;
    }

    public function getPdf(string $orderId)
    {
        $order = $this->orderRepository->findById($orderId);

        if (! $order || ! $this->userCanActOnOrder($order, auth('user')->user())) {
            throw new NotFoundHttpException('Order not found');
        }

        if ($order->pdf) {
            $this->mediaService->delete($order->pdf);
        }

        return $this->generatePdf($order);
    }

    public function generatePdf(Order $order)
    {
        // 1. Generate HTML for PDF
        $html = view('pages.user.orders.order-details-pdf', ['order' => $order])->render();

        // 2. Get mPDF instance
        $mpdf = (new MpdfService)->getInstance();
        $mpdf->WriteHTML($html);

        // 3. Define storage path
        $fileName = 'order-'.$order->id.'.pdf';
        $directory = 'orders';
        $storagePath = storage_path("app/public/{$directory}");

        if (! is_dir($storagePath)) {
            mkdir($storagePath, 0775, true);
        }

        $fullPath = "{$storagePath}/{$fileName}";

        // 4. Save PDF to disk
        $mpdf->Output($fullPath, \Mpdf\Output\Destination::FILE);

        // 5. Wrap it as UploadedFile for your save method
        $uploadedFile = new UploadedFile(
            $fullPath,
            $fileName,
            'application/pdf',
            null,
        );

        // 6. Save it as media
        $file = $this->mediaService->save($order, $uploadedFile, $directory, 'pdf');

        // ✅ Delete the original file after saving
        if (file_exists($fullPath)) {
            unlink($fullPath);
        }

        return $file->url;
    }

    public function getAcceptedDriverActions(Order $order)
    {
        if ($order->user_id !== auth('user')->id()) {
            throw new NotFoundHttpException('Order not found');
        }

        if ($order->driver_id) {
            throw new BadRequestHttpException('Order already assigned to a driver');
        }

        return $order->driverActions()
            ->where('status', OrderDriverActionStatus::ACCEPTED)
            ->with(['driver', 'order', 'order.pickupAddress'])
            ->orderByDesc('accepted_at')
            ->get();
    }
}
