@if (auth('admin')->user()->hasPermission('update vehicle'))
    <div class="row mb-3" id="approval-status-wrapper">
        <div class="col-sm-3 text-muted">{{ __('Approval Status') }}</div>
        <div class="col-sm-9 d-flex align-items-center gap-3">
            {!! statusBadge($vehicle->approval_status) !!}

            @if ($vehicle->approval_status === 'pending')
                <span class="text-muted">➡</span>

                <a href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#statusModal"
                    onclick="openStatusModal(this)"
                    data-url="{{ route('admin.vehicles.update-approval-status', $vehicle->id) }}" data-status="approved">
                    <button class="btn btn-sm btn-success me-2">
                        {{ __('Approve') }}
                    </button>
                </a>

                <a href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#statusModal"
                    onclick="openStatusModal(this)"
                    data-url="{{ route('admin.vehicles.update-approval-status', $vehicle->id) }}"
                    data-status="rejected">
                    <button class="btn btn-sm btn-danger">
                        {{ __('Reject') }}
                    </button>
                </a>
            @endif
        </div>
    </div>
@endif
