<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->string('payment_method_id')->nullable()->after('status')->constrained();
            $table->boolean('is_paid')->default(false)->after('payment_method_id');
        });
    }

    public function down(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropColumn('payment_method_id');
            $table->dropColumn('is_paid');
        });
    }
};
