@php
    $locales = config('app.locales', ['en', 'ar']);
@endphp

<x-layout :title="__('City Details')">
    <x-session-message />

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title">{{ __('City Details') }}</h4>
            <div>
                <a href="{{ route('admin.cities.edit', $city->id) }}" class="btn btn-primary">
                    {{ __('Edit') }}
                </a>
                <a href="{{ route('admin.cities.index') }}" class="btn btn-outline-secondary">
                    {{ __('Back') }}
                </a>
            </div>
        </div>

        <div class="card-body">
            <div class="row">
                @foreach ($locales as $locale)
                    <div class="col-md-4 mb-3">
                        <strong>{{ __('Name') }} ({{ strtoupper($locale) }})</strong>
                        <p>{{ $city->getTranslation('name', $locale) ?? '-' }}</p>
                    </div>
                @endforeach

                <div class="col-md-4 mb-3">
                    <strong>{{ __('Country') }}</strong>
                    <p>{{ $city->country?->name }}</p>
                </div>

                <div class="col-md-4 mb-3">
                    <strong>{{ __('Status') }}</strong>
                    <p>
                        <span class="badge bg-{{ $city->status === 'active' ? 'success' : 'danger' }}">
                            {{ __($city->status) }}
                        </span>
                    </p>
                </div>

                <div class="col-md-4 mb-3">
                    <strong>{{ __('Created At') }}</strong>
                    <p>{{ $city->created_at?->format('Y-m-d H:i') ?? '-' }}</p>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <strong>{{ __('City Area') }}</strong>
                    <div id="map-container">
                        <input id="map-search-input" type="text" placeholder="{{ __('Search for a location') }}"
                            disabled>
                        <div id="city-map"></div>
                        <div id="map-controls" style="display: none;">
                            <button type="button" id="draw-polygon" class="btn btn-sm btn-primary map-button">
                                <i data-feather="edit-2"></i> {{ __('Draw Area') }}
                            </button>
                            <button type="button" id="clear-polygon" class="btn btn-sm btn-danger map-button">
                                <i data-feather="trash"></i> {{ __('Clear') }}
                            </button>
                        </div>
                        <div id="coordinates-container">
                            <input type="hidden" id="area-coordinates-json"
                                value="{{ json_encode($city->area?->getCoordinates() ?? []) }}">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('css')
        <link rel="stylesheet" href="{{ asset('assets/css/city-map.css') }}">
    @endpush

    @push('js')
        <script
            src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.api_key') }}&libraries=drawing,places">
        </script>
        <script src="{{ asset('assets/js/city-map.js') }}"></script>
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                // Hide map controls in view mode
                const mapControls = document.getElementById('map-controls');
                if (mapControls) {
                    mapControls.style.display = 'none';
                }

                // Initialize map
                initMap();

                // Make polygon non-editable after it's drawn
                if (cityPolygon) {
                    cityPolygon.setEditable(false);
                    cityPolygon.setDraggable(false);
                }

                // Disable drawing manager
                if (drawingManager) {
                    drawingManager.setMap(null);
                }
            });
        </script>
    @endpush
</x-layout>
