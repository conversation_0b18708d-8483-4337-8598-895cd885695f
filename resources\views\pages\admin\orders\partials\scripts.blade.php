{{-- Order Show Page JavaScript --}}
@push('js')
    <script
        src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google_maps.api_key') }}&libraries=geometry">
    </script>
    <script>
        let map;
        let marker;
        let geocoder;
        let bootstrapMapModal = new bootstrap.Modal(document.getElementById('mapModal'));

        // Open map modal
        function showLocationOnMap(lat, lng, title) {
            document.getElementById('mapModalLabel').textContent = title;

            // Update Google Maps link
            const googleMapsUrl = `https://www.google.com/maps?q=${lat},${lng}&z=15`;
            document.getElementById('openInGoogleMaps').href = googleMapsUrl;

            // Decide if reverse geocode is needed
            const shouldGeocode = /(Pickup Location|Dropoff Location)/i.test(title);

            // Toggle address info section
            document.getElementById('addressInfo').style.display = shouldGeocode ? 'block' : 'none';
            if (shouldGeocode) {
                document.getElementById('addressText').textContent = '{{ __('Loading...') }}';
            }

            // Show modal
            bootstrapMapModal.show();

            // Initialize or update map
            if (!map) {
                initializeMap(lat, lng, shouldGeocode);
            } else {
                updateMapLocation(lat, lng, shouldGeocode);
            }
        }

        function initializeMap(lat, lng, shouldGeocode = false) {
            const location = {
                lat: parseFloat(lat),
                lng: parseFloat(lng)
            };

            map = new google.maps.Map(document.getElementById('locationMap'), {
                zoom: 15,
                center: location,
                mapTypeId: google.maps.MapTypeId.ROADMAP
            });

            marker = new google.maps.Marker({
                position: location,
                map: map,
                title: '{{ __('Location') }}'
            });

            geocoder = new google.maps.Geocoder();

            if (shouldGeocode) reverseGeocode(location);
        }

        function updateMapLocation(lat, lng, shouldGeocode = false) {
            const location = {
                lat: parseFloat(lat),
                lng: parseFloat(lng)
            };

            map.setCenter(location);
            marker.setPosition(location);

            if (shouldGeocode) reverseGeocode(location);
        }

        function reverseGeocode(location) {
            geocoder.geocode({
                location
            }, function(results, status) {
                if (status === 'OK' && results[0]) {
                    document.getElementById('addressText').textContent = results[0].formatted_address;
                } else {
                    document.getElementById('addressText').textContent =
                        '{{ __('Unable to retrieve address') }}';
                }
            });
        }

        // Show cancel modal
        $(document).on('click', '.cancel-order-btn', function(e) {
            e.preventDefault();
            const url = $(this).data('url');
            $('#cancelOrderUrl').val(url);
            $('#cancelOrderModal').modal('show');
        });

        // Handle cancel form submit
        $('#cancelOrderForm').on('submit', function(e) {
            e.preventDefault();

            const url = $('#cancelOrderUrl').val();
            const token = '{{ csrf_token() }}';
            const reason = $('#cancelReason').val();
            const spinner = $('#cancelOrderSpinner');

            spinner.removeClass('d-none');

            $.ajax({
                url: url,
                method: 'POST',
                data: {
                    _token: token,
                    cancel_reason: reason
                },
                dataType: 'json',
                success: function(response) {
                    $("#general-order-status").text("{{ __('Cancelled') }}");
                    $("#cancelOrderBtn").addClass("d-none");

                    $("#cancelReasonText").text(reason);
                    $("#cancelReasonRow").removeClass("d-none");

                    $('#cancelOrderModal').modal('hide');
                    showToast('success', response.message ||
                        '{{ __('Order cancelled successfully') }}');
                },
                error: function(xhr) {
                    const message = xhr.responseJSON?.message || '{{ __('Something went wrong') }}';
                    showToast('error', message);
                },
                complete: function() {
                    spinner.addClass('d-none');
                }
            });
        });

        function showToast(icon, message) {
            let bgColor = icon === 'error' ? '#DC3545' : '#198754';

            Swal.fire({
                toast: true,
                position: 'top-end',
                icon: icon,
                title: message,
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                background: bgColor,
                color: '#fff'
            });
        }
    </script>
@endpush
