<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidPolygon implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Check if we have an array and contains coordinates
        if (! is_array($value) || ! isset($value['coordinates']) || ! is_array($value['coordinates'])) {
            $fail(__('The polygon must be a valid array of coordinates.'));

            return;
        }

        // Get the first ring of coordinates (GeoJSON format has coordinates as [[[x,y], [x,y]]])
        $coordinates = $value['coordinates'][0] ?? [];
        if (! is_array($coordinates)) {
            $fail(__('Invalid polygon coordinates structure.'));

            return;
        }

        // Check if we have enough points
        $lastIndex = count($coordinates) - 1;
        if ($lastIndex < 3) {
            $fail(__('Please draw a valid polygon with at least 3 distinct points.'));

            return;
        }

        // Check for distinct points (at least 3 excluding the closing point)
        $distinctPoints = [];
        for ($i = 0; $i < $lastIndex; $i++) {
            if (! isset($coordinates[$i][0]) || ! isset($coordinates[$i][1])) {
                $fail(__('Each point must have longitude and latitude coordinates.'));

                return;
            }

            $point = json_encode($coordinates[$i]);
            $distinctPoints[$point] = true;
        }

        if (count($distinctPoints) < 3) {
            $fail(__('Please draw a valid polygon with at least 3 distinct points.'));

            return;
        }

        // Check if polygon is closed (first point equals last point)
        $firstPoint = $coordinates[0];
        $lastPoint = $coordinates[$lastIndex];

        if (
            ! isset($firstPoint[0]) || ! isset($firstPoint[1]) ||
            ! isset($lastPoint[0]) || ! isset($lastPoint[1]) ||
            $firstPoint[0] != $lastPoint[0] || $firstPoint[1] != $lastPoint[1]
        ) {
            $fail(__('The polygon must be closed (first and last points must be the same).'));

            return;
        }

        // Add debug logging
        \Log::debug('Polygon validation:', [
            'coordinates' => $coordinates,
            'distinct_points' => count($distinctPoints),
            'total_points' => count($coordinates),
        ]);
    }
}
