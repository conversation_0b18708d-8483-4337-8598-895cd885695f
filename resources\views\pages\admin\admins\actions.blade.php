@php
    $canShow = auth('admin')->user()->hasPermission('show admin') && $id != 1 && $id != auth('admin')->id();
    $canEdit = auth('admin')->user()->hasPermission('update admin') && $id != 1 && $id != auth('admin')->id();
    $canDelete = auth('admin')->user()->hasPermission('delete admin') && $id != 1 && $id != auth('admin')->id();
@endphp

<div class="actions">
    @if ($canShow || $canEdit || $canDelete)

        @if ($canShow)
            <a href="{{ route('admin.admins.show', $id) }}"><i class="ti ti-eye"></i></a>
        @endcan

        @if ($canEdit)
            <a href="{{ route('admin.admins.edit', $id) }}"><i class="ti ti-edit"></i></a>
        @endcan

        @if ($canDelete)
            <a href="javascript:void(0)">
                <i data-bs-toggle="modal" data-bs-target="#delete-modal" onclick="changeDeleteModalData(this)"
                    delete-route="{{ route('admin.admins.destroy', $id) }}"
                    delete-name="{{ __('Admin') }} : {{ $name }}" class="ti ti-archive">
                </i>
            </a>
        @endcan
    @else
        -
    @endif
</div>
