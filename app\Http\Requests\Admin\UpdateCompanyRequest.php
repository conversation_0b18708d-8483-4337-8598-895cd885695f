<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;
use App\Models\Company;
use App\Models\CompanyAdmin;
use App\Rules\UniquePhone;
use App\Rules\ValidMedia;
use App\Rules\ValidPassword;
use App\Rules\ValidPhone;

class UpdateCompanyRequest extends BaseFormRequest
{
    public function rules(): array
    {
        $company = $this->route('company');

        return [
            'name' => ['required', 'string', 'min:5', 'max:320', 'unique:companies,name'],
            'country_code' => ['required', 'string', 'exists:countries,code'],
            'phone' => ['required', 'string', new ValidPhone($this->country_code), new UniquePhone($this->country_code, Company::class, $company?->id)],
            'email' => ['nullable', 'string', 'email:rfc,dns', 'max:255', 'unique:companies,email'],
            'business_registration_number' => ['required', 'string', 'min:8', 'max:20', 'unique:companies,business_registration_number'],
            'address' => ['nullable', 'string', 'min:5', 'max:100'],
            'bank_name' => ['required', 'string', 'min:5', 'max:100'],
            'bank_account_owner' => ['required', 'string', 'min:5', 'max:100'],
            'bank_account_number' => ['required', 'digits_between:10,20'],
            'iban' => ['nullable', 'string', 'regex:/^[A-Z]{2}\d{2}[A-Z0-9]{11,30}$/i'],
            'admin_name' => ['required', 'string', 'min:4', 'max:30'],
            'admin_email' => ['required', 'string', 'email:rfc,dns', 'max:255', 'unique:company_admins,email'],
            'admin_country_code' => ['required', 'string', 'exists:countries,code'],
            'admin_phone' => ['required', 'string', new ValidPhone($this->admin_country_code), new UniquePhone($this->admin_country_code, CompanyAdmin::class, $company?->superAdmin?->id)],
            'admin_password' => ['required', 'string', new ValidPassword, 'confirmed'],
            'logo' => ['nullable', new ValidMedia(['image'])],
            'commercial_registration_certificate' => ['required', new ValidMedia(['image', 'pdf'])],
            'tax_certificate' => ['nullable', new ValidMedia(['image', 'pdf'])],
            'cargo_insurance_certificate' => ['required', new ValidMedia(['image', 'pdf'])],
        ];
    }
}
