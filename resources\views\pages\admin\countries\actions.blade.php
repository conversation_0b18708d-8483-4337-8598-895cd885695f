@php
    $admin = auth('admin')->user();
@endphp

<div class="actions">
    @if (
        $admin->hasPermission('show country') ||
            $admin->hasPermission('update country') ||
            $admin->hasPermission('delete country'))
        @if ($admin->hasPermission('show country'))
            <a href="{{ route('admin.countries.show', $id) }}">
                <i class="ti ti-eye"></i>
            </a>
        @endif

        @if ($admin->hasPermission('update country'))
            <a href="{{ route('admin.countries.edit', $id) }}">
                <i class="ti ti-edit"></i>
            </a>
        @endif

        @if ($admin->hasPermission('delete country'))
            <a href="javascript:void(0)">
                <i data-bs-toggle="modal" data-bs-target="#delete-modal" onclick="changeDeleteModalData(this)"
                    delete-route="{{ route('admin.countries.destroy', $id) }}"
                    delete-name="{{ __('Country') }} : {{ Arr::get($name, app()->getLocale()) }}" class="ti ti-archive">
                </i>
            </a>
        @endif
    @else
        -
    @endif
</div>
