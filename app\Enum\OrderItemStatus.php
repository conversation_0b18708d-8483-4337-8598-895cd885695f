<?php

namespace App\Enum;

enum OrderItemStatus: string
{
    case NOT_YET_PICKED_UP = 'not_yet_picked_up'; // Default
    case PICKED_UP = 'picked_up'; // when driver pick it up from pickup location
    case PICKED_UP_FAILED = 'picked_up_failed'; // when driver failed to pick it up from pickup location
    case DELIVERED_TO_FIRST_HUB = 'delivered_to_first_hub'; // non express shipping
    case IN_TRANSIT = 'in_transit'; // when first hub transport it to second hub
    case DELIVERED_TO_SECOND_HUB = 'delivered_to_second_hub'; // non express shipping
    case WITH_DRIVER = 'with_driver'; // when driver pick it up from second hub
    case DELIVERED = 'delivered'; // delivered to end user
    case DELIVERY_FAILED = 'delivery_failed'; // failed to deliver
}
