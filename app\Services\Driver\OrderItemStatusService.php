<?php

namespace App\Services\Driver;

use App\Contracts\OrderItemStatusHandlerInterface;
use App\Enum\OrderItemStatus;
use App\Enum\OrderStatus;
use App\Events\OrderItemStatusUpdated;
use App\Events\OrderStatusUpdated;
use App\Models\OrderItem;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class OrderItemStatusService implements OrderItemStatusHandlerInterface
{
    /**
     * @var array<string, array<string>>
     */
    private const STATUS_TRANSITIONS = [
        OrderStatus::ARRIVED_AT_PICKUP->value => [
            OrderItemStatus::NOT_YET_PICKED_UP->value => OrderItemStatus::PICKED_UP->value,
        ],
        OrderStatus::PICKED_UP->value => [
            OrderItemStatus::NOT_YET_PICKED_UP->value => OrderItemStatus::PICKED_UP->value,
        ],
    ];

    /**
     * @var array<string>
     */
    private const VALID_FAILURE_TRANSITIONS = [
        OrderItemStatus::NOT_YET_PICKED_UP->value => OrderItemStatus::PICKED_UP_FAILED->value,
        OrderItemStatus::WITH_DRIVER->value => OrderItemStatus::PICKED_UP_FAILED->value,
        OrderItemStatus::PICKED_UP->value => OrderItemStatus::DELIVERY_FAILED->value,
    ];

    public function __construct(
        private readonly OrderStatusService $orderStatusService
    ) {}

    /**
     * Update the order item status based on the current order status
     */
    public function update(OrderItem $item): OrderItem
    {
        $nextStatus = $this->getNextStatus($item);

        if ($nextStatus === null || $nextStatus === '' || $nextStatus === '0') {
            throw new BadRequestHttpException(__('Invalid or unavailable status transition.'));
        }

        return $this->updateStatus($item, $nextStatus);
    }

    /**
     * Mark an order item as failed during pickup
     */
    public function markAsPickupFailed(OrderItem $item): OrderItem
    {
        if ($item->status === OrderItemStatus::PICKED_UP_FAILED->value) {
            throw new BadRequestHttpException(__('Item is already marked as pickup failed.'));
        }

        $nextStatus = $this->getFailureTransition($item->status);
        if ($nextStatus === null || $nextStatus === '' || $nextStatus === '0' || $nextStatus !== OrderItemStatus::PICKED_UP_FAILED->value) {
            throw new BadRequestHttpException(__('Item cannot be marked as pickup failed in its current state.'));
        }

        return $this->updateStatus($item, $nextStatus);
    }

    /**
     * Mark an order item as failed during delivery
     */
    public function markAsDeliveryFailed(OrderItem $item): OrderItem
    {
        $nextStatus = $this->getFailureTransition($item->status);
        if ($nextStatus === null || $nextStatus === '' || $nextStatus === '0' || $nextStatus !== OrderItemStatus::DELIVERY_FAILED->value) {
            throw new BadRequestHttpException(__('Item cannot be marked as delivery failed in its current state.'));
        }

        return $this->updateStatus($item, $nextStatus);
    }

    /**
     * Get the next valid status for the item based on order status
     */
    private function getNextStatus(OrderItem $item): ?string
    {
        $orderStatus = $item->order->status instanceof OrderStatus
            ? $item->order->status->value
            : $item->order->status;

        return self::STATUS_TRANSITIONS[$orderStatus][$item->status] ?? null;
    }

    /**
     * Get the failure status transition if valid
     */
    private function getFailureTransition(string $currentStatus): ?string
    {
        return self::VALID_FAILURE_TRANSITIONS[$currentStatus] ?? null;
    }

    /**
     * Update the item status and sync related data
     */
    private function updateStatus(OrderItem $item, string $newStatus): OrderItem
    {
        $item->update(['status' => $newStatus]);
        $this->orderStatusService->update($item->order);

        // Broadcast events
        event(new OrderItemStatusUpdated($item));
        event(new OrderStatusUpdated($item->order));

        return $item;
    }
}
