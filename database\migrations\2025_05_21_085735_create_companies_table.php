<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('country_code');
            $table->string('phone')->unique();
            $table->string('email')->nullable()->unique();
            $table->string('code')->unique();
            $table->string('business_registration_number')->unique();
            $table->string('address', 500)->nullable();
            $table->string('bank_name');
            $table->string('bank_account_owner');
            $table->string('bank_account_number');
            $table->string('iban')->nullable();
            $table->string('approval_status')->default('pending');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
