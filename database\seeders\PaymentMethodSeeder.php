<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    public function run(): void
    {
        $paymentMethods = [
            ['name' => 'cash'],
            ['name' => 'online'],
            ['name' => 'wallet']
        ];

        foreach ($paymentMethods as $paymentMethod) {
            PaymentMethod::firstOrCreate($paymentMethod);
        }
    }
}
