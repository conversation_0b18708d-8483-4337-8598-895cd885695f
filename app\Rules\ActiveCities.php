<?php

namespace App\Rules;

use App\Repositories\CityRepository;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ActiveCities implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $cityRepository = app(CityRepository::class);

        $cities = $cityRepository->getByIds($value);

        foreach ($cities as $city) {
            if ($city->status == 'inactive') {
                $fail('The :attribute contains inactive cities.');
            }
        }
    }
}
