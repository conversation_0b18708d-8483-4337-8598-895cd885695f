<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Http\Requests\Driver\Order\CancelRequest;
use App\Http\Requests\Driver\Order\FilterRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\Driver\OrderResource;
use App\Services\Driver\OrderService;

class OrderController extends Controller
{
    public function __construct(private readonly OrderService $orderService)
    {
        //
    }

    public function index(FilterRequest $request)
    {
        $filters = $request->validated();
        $orders = $this->orderService->index($filters);

        return success(BaseCollection::make($orders, OrderResource::class));
    }

    public function show(string $id)
    {
        return success(new OrderResource($this->orderService->show($id)));
    }

    public function update(string $id)
    {
        $order = $this->orderService->updateStatus($id);

        return success(new OrderResource($order), 'Order status updated successfully.');
    }

    public function cancel(CancelRequest $request, string $id)
    {
        $order = $this->orderService->cancel($id, $request->validated('reason'));

        return success(new OrderResource($order), 'Order cancelled successfully.');
    }

    public function deliveryFailed(CancelRequest $request, string $id)
    {
        $order = $this->orderService->deliveryFailed($id, $request->validated('reason'));

        return success(new OrderResource($order), 'Order is failed to deliver.');
    }
}
