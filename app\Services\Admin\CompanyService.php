<?php

namespace App\Services\Admin;

use App\Models\Company;
use App\Repositories\AreaRepository;
use App\Repositories\CompanyAdminRepository;
use App\Repositories\CompanyImmediateShippingCityRepository;
use App\Repositories\CompanyRepository;
use App\Repositories\CompanyShippingTypeRepository;
use App\Repositories\CompanyShippingTypeSizeRepository;
use App\Repositories\DriverRepository;
use App\Repositories\RoleRepository;
use App\Repositories\ShippingTypeRepository;
use App\Repositories\TransportionMethodRepository;
use App\Services\Company\RegistrationService;
use App\Services\MediaService;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class CompanyService
{
    public function __construct(
        private readonly CompanyRepository $companyRepository,
        private readonly CompanyAdminRepository $companyAdminRepository,
        private readonly MediaService $mediaService,
        private readonly CompanyShippingTypeRepository $companyShippingTypeRepository,
        private readonly CompanyShippingTypeSizeRepository $companyShippingTypeSizeRepository,
        private readonly CompanyImmediateShippingCityRepository $companyImmediateShippingCityRepository,
        private readonly AreaRepository $areaRepository,
        private readonly RegistrationService $registrationService,
        private readonly DriverRepository $driverRepository,
        private readonly ShippingTypeRepository $shippingTypeRepository,
        private readonly TransportionMethodRepository $transportionMethodRepository,
        private readonly RoleRepository $roleRepository
    ) {}

    public function getCompany(Company $company): Company
    {
        if ($company->approval_status != 'approved') {
            abort(404);
        }

        return $company;
    }

    public function create(array $data): void
    {
        $this->validateRegistration($data);

        DB::transaction(function () use ($data): void {

            $data['code'] = $this->registrationService->generateCode();
            $data['phone'] = normalizeMobileNumber($data['phone']);
            $data['approval_status'] = 'approved';

            $company = $this->companyRepository->register($data);

            if (request('commercial_registration_certificate')) {
                $this->mediaService->save($company, request('commercial_registration_certificate'), 'companies', 'commercial_registration_certificate');
            }

            if (request('cargo_insurance_certificate')) {
                $this->mediaService->save($company, request('cargo_insurance_certificate'), 'companies', 'cargo_insurance_certificate');
            }

            if (request('tax_certificate')) {
                $this->mediaService->save($company, request('tax_certificate'), 'companies', 'tax_certificate');
            }

            if (request('logo')) {
                $this->mediaService->save($company, request('logo'), 'companies', 'logo');
            }

            foreach ($data['shipping_options'] as $shipping_option) {
                $companyShippingType = $this->companyShippingTypeRepository->create(
                    $company,
                    $shipping_option['shipping_type_id'],
                    $shipping_option['has_express_delivery'] ?? null
                );

                foreach ($shipping_option['shipping_sizes'] as $shipping_size_id) {
                    $companyShippingTypeSize = $this->companyShippingTypeSizeRepository->create($companyShippingType, $shipping_size_id);

                    foreach ($shipping_option['transportion_methods'] as $transportion_method_id) {
                        $companyShippingTypeSize->transportionMethods()->create([
                            'company_shipping_type_id' => $companyShippingType->id,
                            'transportion_method_id' => $transportion_method_id,
                        ]);
                    }
                }

                // Immediate shipping
                if ($shipping_option['shipping_type_id'] == 1) {
                    foreach ($shipping_option['cities'] as $city) {
                        $companyImmediateShippingCity = $this->companyImmediateShippingCityRepository->create($company, $city['id'])->refresh();
                        $companyImmediateShippingCity->areas()->attach($city['areas']);
                    }
                }

                // Intercity shipping
                if ($shipping_option['shipping_type_id'] == 2) {
                    $company->intercityShippingPickupCities()->attach($shipping_option['pickup_cities'], ['type' => 'pickup']);
                    $company->intercityShippingDeliveryCities()->attach($shipping_option['delivery_cities'], ['type' => 'delivery']);
                }

                // International shipping
                if ($shipping_option['shipping_type_id'] == 3) {
                    $company->internationalShippingCountires()->attach($shipping_option['shipping_countries']);
                }
            }

            // Creating Super Admin
            $data['company_id'] = $company->id;
            $data['admin_phone'] = normalizeMobileNumber($data['admin_phone']);
            $data['admin_password'] = bcrypt($data['admin_password']);
            $data['admin_role_id'] = 3;
            $data['is_super_admin'] = 1;

            $admin = $this->companyAdminRepository->register($data);
            $superAdminRole = $this->roleRepository->getCompanySuperAdmin();
            $admin->permissions()->attach($superAdminRole->permissions);
        });
    }

    public function validateRegistration(array $data): void
    {
        foreach ($data['shipping_options'] as $shipping_option) {

            // Immediate shipping
            if ($shipping_option['shipping_type_id'] == 1) {
                foreach ($shipping_option['cities'] as $city) {
                    foreach ($city['areas'] as $area) {
                        $area = $this->areaRepository->getById($area);

                        if ($area->city_id != $city['id']) {
                            throw new BadRequestHttpException("area id $area->id doesn't belong to city id $city[id]");
                        }
                    }
                }
            }
        }
    }

    public function update(Company $company): void
    {
        if ($company->approval_status != 'approved') {
            abort(404);
        }

        DB::transaction(function () use ($company): void {
            $data = request()->all();
            $data['status'] = request('status') ? 'active' : 'inactive';

            $this->companyRepository->update($company, $data);

            if (request('commercial_registration_certificate')) {
                $this->mediaService->delete($company->commercialRegistrationCertificate);
                $this->mediaService->save($company, request('commercial_registration_certificate'), 'companies', 'commercial_registration_certificate');
            }

            if (request('cargo_insurance_certificate')) {
                $this->mediaService->delete($company->cargoInsuranceCertificate);
                $this->mediaService->save($company, request('cargo_insurance_certificate'), 'companies', 'cargo_insurance_certificate');
            }

            if (request('tax_certificate')) {
                $this->mediaService->delete($company->taxCertificate);
                $this->mediaService->save($company, request('tax_certificate'), 'companies', 'tax_certificate');
            }

            if (request('logo')) {
                $this->mediaService->delete($company->logo);
                $this->mediaService->save($company, request('logo'), 'companies', 'logo');
            }

            $superAdminData = [
                'name' => $data['admin_name'],
                'email' => $data['admin_email'],
                'password' => request('admin_password') ? bcrypt($data['admin_password']) : $company->superAdmin->password,
                'country_code' => $data['admin_country_code'],
                'phone' => normalizeMobileNumber($data['admin_phone']),
            ];

            $this->companyAdminRepository->update($company->superAdmin, $superAdminData);
        });
    }

    public function delete(Company $company): void
    {
        if ($company->approval_status != 'approved') {
            abort(404);
        }

        if ($company->inProgressOrders()->count() > 0) {
            throw new BadRequestHttpException('Company has in progress orders');
        }

        DB::transaction(function () use ($company): void {
            $company->delete();
            $this->companyRepository->invalidateUniqueData($company);

            foreach ($company->drivers as $driver) {
                $driver->delete();
                $this->driverRepository->invalidateUniqueData($driver);
            }

            foreach ($company->admins as $admin) {
                $admin->delete();
                $this->companyAdminRepository->invalidateUniqueData($admin);
            }
        });
    }

    public function getAssociatedShippingTypes(Company $company)
    {
        $shippingTypeIds = $company->shippingTypes->pluck('shipping_type_id')->toArray();

        return $this->shippingTypeRepository->getByIds($shippingTypeIds);
    }

    public function getAssociatedTransportionMethods(Company $company)
    {
        $transportionMethodsIds = $company->shippingTypes
            ->flatMap->sizes
            ->flatMap->transportionMethods->pluck('transportion_method_id')->toArray();

        $transportionMethodsIds = array_unique($transportionMethodsIds);

        return $this->transportionMethodRepository->getByIds($transportionMethodsIds);
    }

    public function getAssociatedImmediateCities(Company $company)
    {
        return $company->immediateShippingCities()
            ->whereHas('areas')
            ->whereHas('city', function ($query) {
                $query->where('status', 'active');
            })
            ->get()->map(function ($companyCity) {
                return (object) [
                    'id' => $companyCity->city->id,
                    'name' => $companyCity->city->name,
                    'areas' => $companyCity->areas->where('status', 'active'),
                ];
            });
    }
}
