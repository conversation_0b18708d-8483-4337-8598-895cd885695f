@php
    $canShow = auth('admin')->user()->hasPermission('show driver');
    $canDelete = auth('admin')->user()->hasPermission('delete driver');
    $canAssignVehicles = auth('admin')->user()->hasPermission('assign vehicles to driver');
@endphp

<div class="actions">
    @if ($canShow || $canDelete || $canAssignVehicles)

        @if ($canAssignVehicles)
            <a href="{{ route('admin.drivers.assign-vehicles', $id) }}"><i class="ti ti-motorbike"></i></a>
        @endif

        @if ($canShow)
            <a href="{{ route('admin.drivers.show', $id) }}"><i class="ti ti-eye"></i></a>
        @endif

        @if ($canDelete)
            <a href="javascript:void(0)">
                <i data-bs-toggle="modal" data-bs-target="#delete-modal" onclick="changeDeleteModalData(this)"
                    delete-route="{{ route('admin.drivers.destroy', $id) }}"
                    delete-name="{{ __('Driver') }} : {{ $name }}" class="ti ti-archive">
                </i>
            </a>
        @endif
    @else
        -
    @endif
</div>
