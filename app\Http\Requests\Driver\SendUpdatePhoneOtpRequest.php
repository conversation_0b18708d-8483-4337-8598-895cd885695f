<?php

namespace App\Http\Requests\Driver;

use App\Models\Driver;
use App\Rules\UniquePhone;
use App\Rules\ValidPhone;
use Illuminate\Foundation\Http\FormRequest;

class SendUpdatePhoneOtpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'country_code' => ['required', 'string', 'exists:countries,code'],
            'phone' => ['required', 'string', new ValidPhone($this->country_code), new UniquePhone($this->country_code, Driver::class)],
        ];
    }
}
