@php
    $admin = auth('admin')->user();
@endphp

<div class="actions">
    @if ($admin->hasPermission('show area') || $admin->hasPermission('update area') || $admin->hasPermission('delete area'))
        @if ($admin->hasPermission('show area'))
            <a href="{{ route('admin.areas.show', $id) }}">
                <i class="ti ti-eye"></i>
            </a>
        @endif

        @if ($admin->hasPermission('update area'))
            <a href="{{ route('admin.areas.edit', $id) }}">
                <i class="ti ti-edit"></i>
            </a>
        @endif

        @if ($admin->hasPermission('delete area'))
            <a href="javascript:void(0)">
                <i data-bs-toggle="modal" data-bs-target="#delete-modal" onclick="changeDeleteModalData(this)"
                    delete-route="{{ route('admin.areas.destroy', $id) }}"
                    delete-name="{{ __('Area') }} : {{ Arr::get($name, app()->getLocale()) }}" class="ti ti-archive">
                </i>
            </a>
        @endif
    @else
        -
    @endif
</div>
