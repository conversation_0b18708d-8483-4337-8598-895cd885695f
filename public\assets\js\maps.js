// maps.js - Version 1.0.5 (for cache busting)
console.log('maps.js loaded - Version 1.0.5 at ' + new Date().toISOString());

let map, marker;

function initMap() {
    console.log('initMap called at ' + new Date().toISOString());
    const mapCanvas = document.getElementById('mapCanvas');
    if (!mapCanvas) {
        console.error('mapCanvas element not found');
        return;
    }

    const rawLat = mapCanvas.dataset.latitude;
    const rawLng = mapCanvas.dataset.longitude;
    console.log('Raw data attributes:', { rawLat, rawLng });

    const hotelLat = rawLat && rawLat.trim() !== '' && !isNaN(parseFloat(rawLat)) ? parseFloat(rawLat) : NaN;
    const hotelLng = rawLng && rawLng.trim() !== '' && !isNaN(parseFloat(rawLng)) ? parseFloat(rawLng) : NaN;
    console.log('Parsed coordinates:', { hotelLat, hotelLng });

    const hasValidCoordinates = !isNaN(hotelLat) && !isNaN(hotelLng);
    const defaultLat = hasValidCoordinates ? hotelLat : 21.3891; // Mecca
    const defaultLng = hasValidCoordinates ? hotelLng : 39.8579;
    const zoomLevel = hasValidCoordinates ? 15 : 8;

    console.log('Final coordinates and zoom:', { lat: defaultLat, lng: defaultLng, zoom: zoomLevel });

    try {
        map = new google.maps.Map(mapCanvas, {
            zoom: zoomLevel,
            center: { lat: defaultLat, lng: defaultLng }
        });
        console.log('Map initialized successfully');
    } catch (error) {
        console.error('Map initialization failed:', error);
        return;
    }

    marker = new google.maps.Marker({
        position: { lat: defaultLat, lng: defaultLng },
        map: map,
        draggable: true
    });
    console.log('Marker initialized');

    google.maps.event.addListener(marker, 'dragend', function () {
        const position = marker.getPosition();
        const lat = position.lat();
        const lng = position.lng();
        updateLatLngInputs(lat, lng);
        console.log('Marker moved to:', { lat, lng });
    });

    // NEW: Allow clicking anywhere on the map to move the marker
    google.maps.event.addListener(map, 'click', function (event) {
        const lat = event.latLng.lat();
        const lng = event.latLng.lng();
        marker.setPosition({ lat, lng });
        updateLatLngInputs(lat, lng);
        console.log('Marker moved via map click:', { lat, lng });
    });
}


function updateLatLngInputs(lat, lng) {
    const latInput = document.getElementById('lat');
    const lngInput = document.getElementById('lng');
    const latitudeInput = document.getElementById('latitude');
    const longitudeInput = document.getElementById('longitude');

    if (latInput) latInput.value = lat;
    if (lngInput) lngInput.value = lng;
    if (latitudeInput) latitudeInput.value = lat;
    if (longitudeInput) longitudeInput.value = lng;
}

document.addEventListener('DOMContentLoaded', function () {
    console.log('DOMContentLoaded event fired at ' + new Date().toISOString());

    const generateLinkBtn = document.getElementById('generateLink');
    if (!generateLinkBtn) {
        console.error('Generate Link button not found');
        return;
    }

    generateLinkBtn.addEventListener('click', function () {
        console.log('Generate Link button clicked');
        const latitudeInput = document.getElementById('latitude');
        const longitudeInput = document.getElementById('longitude');

        const lat = latitudeInput && !isNaN(parseFloat(latitudeInput.value)) ? parseFloat(latitudeInput.value) : NaN;
        const lng = longitudeInput && !isNaN(parseFloat(longitudeInput.value)) ? parseFloat(longitudeInput.value) : NaN;

        if (!isNaN(lat) && !isNaN(lng)) {
            const googleMapsUrl = `https://www.google.com/maps?q=${lat},${lng}&z=15`;

            const locationUrlInput = document.getElementById('location_url');
            if (locationUrlInput) {
                locationUrlInput.value = googleMapsUrl;
                console.log('Generated Google Maps URL:', googleMapsUrl);
            } else {
                console.error('location_url input not found');
            }

            if (map && marker) {
                const newPosition = { lat, lng };
                map.setCenter(newPosition);
                map.setZoom(15);
                marker.setPosition(newPosition);
                console.log('Map and marker updated');
            } else {
                console.error('Map or marker not initialized');
            }

            updateLatLngInputs(lat, lng);
        } else {
            alert('يرجى إدخال إحداثيات صحيحة لخط العرض وخط الطول');
            console.error('Invalid coordinates for generating map link');
        }
    });
});

// Expose globally and initialize after full load
window.initMap = initMap;

window.addEventListener('load', () => {
    if (typeof google !== 'undefined' && google.maps) {
        initMap();
    } else {
        const interval = setInterval(() => {
            if (typeof google !== 'undefined' && google.maps) {
                clearInterval(interval);
                initMap();
            }
        }, 300);
    }
});
