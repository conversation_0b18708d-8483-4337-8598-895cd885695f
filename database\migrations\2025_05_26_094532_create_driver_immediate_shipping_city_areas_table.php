<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('driver_immediate_shipping_city_areas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('driver_city_id')->constrained('driver_immediate_shipping_cities')->cascadeOnDelete();
            $table->foreignId('area_id')->constrained()->cascadeOnDelete();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('driver_immediate_shipping_city_areas');
    }
};
