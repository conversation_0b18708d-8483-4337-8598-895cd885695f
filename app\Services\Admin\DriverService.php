<?php

namespace App\Services\Admin;

use App\Models\Driver;
use App\Repositories\DriverRepository;
use App\Models\Order;
use App\Models\Vehicle;
use App\Repositories\DriverVehicleRepository;
use App\Repositories\VehicleRepository;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class DriverService
{
    public function __construct(
        private readonly DriverVehicleRepository $driverVehicleRepository,
        private readonly VehicleRepository $vehicleRepository,
        private readonly DriverRepository $driverRepository
    ) {}

    public function getDriver(Driver $driver): Driver
    {
        if ($driver->approval_status != 'approved') {
            abort(404);
        }

        return $driver;
    }

    public function delete(Driver $driver): void
    {
        if ($driver->approval_status != 'approved') {
            abort(404);
        }

        $hasActiveOrder = $this->driverRepository->hasActiveOrder($driver);

        if ($hasActiveOrder) {
            throw new BadRequestHttpException(__('driver has an active order'));
        }

        $driver->delete();
    }

    public function getPendingDriverVehicles(Driver $driver)
    {
        return $this->driverVehicleRepository->getPendingDriverVehicles($driver)->map(fn($driverVehicle) => $driverVehicle->vehicle);
    }

    public function getApprovedDriverVehicles(Driver $driver)
    {
        return $this->driverVehicleRepository->getApprovedDriverVehicles($driver)->map(function ($driverVehicle) {
            $vehicle = $driverVehicle->vehicle;
            // attach is_active from the pivot (DriverVehicle) to the vehicle so the view can use it
            $vehicle->setAttribute('is_active', (bool) $driverVehicle->is_active);

            return $vehicle;
        });
    }

    public function assignVehicles(Driver $driver, array $vehicles): void
    {
        DB::transaction(function () use ($driver, $vehicles): void {
            $alreadyAssignedVehicles = $this->driverVehicleRepository->getApprovedDriverVehicles($driver)->pluck('vehicle_id')->toArray();

            // assign new vehicles
            foreach ($vehicles as $vehicle_id) {

                $vehicle = $this->vehicleRepository->getById($vehicle_id);

                if ($vehicle->company_id != $driver->company_id) {
                    throw new BadRequestHttpException('Vehicle not found');
                }

                if (! in_array($vehicle_id, $alreadyAssignedVehicles)) {
                    $this->driverVehicleRepository->create($driver->id, $vehicle_id);
                }
            }

            // delete removed vehicles
            foreach ($alreadyAssignedVehicles as $assigned_vehicle_id) {

                // if vehicle is not removed, go to next one
                if (in_array($assigned_vehicle_id, request('vehicles'))) {
                    continue;
                }

                $driverVehicle = $this->driverVehicleRepository->get($driver->id, $assigned_vehicle_id);

                // prevent remove the active vehicle
                if ($driverVehicle->is_active) {
                    throw new BadRequestHttpException('You can not remove active vehicle');
                }

                $this->driverVehicleRepository->delete($driver->id, $assigned_vehicle_id);
            }
        });
    }

    public function activation(Driver $driver, Vehicle $vehicle): array
    {
        if ($vehicle->company_id !== $driver->company_id) {
            throw new BadRequestHttpException('Vehicle not found');
        }

        $driverVehicle = $this->driverVehicleRepository->get($driver->id, $vehicle->id);

        if (! $driverVehicle) {
            throw new BadRequestHttpException('Vehicle is not assigned to this driver');
        }

        if ($driverVehicle->approval_status !== 'approved') {
            throw new BadRequestHttpException('Vehicle is not approved for this driver');
        }

        // One-button behavior: only allow enabling an inactive vehicle.
        // If requested vehicle is already active, no change.
        $approvedVehicles = $this->driverVehicleRepository->getApprovedDriverVehicles($driver);
        $currentActive = $approvedVehicles->firstWhere('is_active', true);

        if ($currentActive && $currentActive->vehicle_id === $vehicle->id) {
            return [
                'is_active' => true,
                'active_vehicle_id' => $vehicle->id,
            ];
        }

        // If switching away from a current active vehicle, block if it has an active order
        if ($currentActive) {
            $hasActiveOrderOnCurrent = Order::active()
                ->where('driver_id', $driver->id)
                ->where('vehicle_id', $currentActive->vehicle_id)
                ->exists();

            if ($hasActiveOrderOnCurrent) {
                throw new BadRequestHttpException('You cannot switch vehicles while there is an active order on the current vehicle');
            }
        }

        // Activate requested and deactivate all others
        DB::transaction(function () use ($driver, $driverVehicle): void {
            $this->driverVehicleRepository->update($driverVehicle, ['is_active' => true]);
            $this->driverVehicleRepository
                ->getApprovedDriverVehicles($driver)
                ->where('id', '!=', $driverVehicle->id)
                ->each(fn($dv) => $this->driverVehicleRepository->update($dv, ['is_active' => false]));
        });

        return [
            'is_active' => true,
            'active_vehicle_id' => $vehicle->id,
        ];
    }
}
