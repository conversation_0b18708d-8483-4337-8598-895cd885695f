<x-layout :title="__('Vehicle') . ' #' . $vehicle->id">
    <div class="card shadow-sm rounded-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ __('Vehicle') }} #{{ $vehicle->id }}</h5>
            @if(auth('company')->user()->hasPermission('update vehicle') && $vehicle->approval_status != 'rejected')
                <a class="btn btn-sm btn-outline-primary" href="{{ route('company.vehicles.edit', $vehicle->id) }}">
                    <i class="fas fa-edit me-1"></i> {{ __('Edit') }}
                </a>
            @endif
        </div>

        <div class="card-body">
            {{-- Vehicle Basic Info --}}
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Transportion Method') }}</div>
                <div class="col-sm-9">{{ $vehicle->transportionMethod->name }}</div>
            </div>

            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Name (EN)') }}</div>
                <div class="col-sm-9">{{ $vehicle->getTranslation('name', 'en') ?? '-' }}</div>
            </div>

            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Name (AR)') }}</div>
                <div class="col-sm-9">{{ $vehicle->getTranslation('name', 'ar') ?? '-' }}</div>
            </div>

            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Name (UR)') }}</div>
                <div class="col-sm-9">{{ $vehicle->getTranslation('name', 'ur') ?? '-' }}</div>
            </div>

            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Model') }}</div>
                <div class="col-sm-9">{{ $vehicle->model }}</div>
            </div>

            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Plate Number') }}</div>
                <div class="col-sm-9">{{ $vehicle->plate_number }}</div>
            </div>

            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('License Number') }}</div>
                <div class="col-sm-9">{{ $vehicle->license_number }}</div>
            </div>

            @if($vehicle->approval_status == 'approved')
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Status') }}</div>
                <div class="col-sm-9">{!! statusBadge($vehicle->status) !!}</div>
            </div>
            @endif

            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Approval Status') }}</div>
                <div class="col-sm-9">{!! statusBadge($vehicle->approval_status) !!}</div>
            </div>

            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Created At') }}</div>
                <div class="col-sm-9" dir="ltr">{{ formatDateTime($vehicle->created_at) }}</div>
            </div>

            @if($vehicle->approval_status == 'rejected')
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Rejection Reason') }}</div>
                <div class="col-sm-9">{{ $vehicle->rejection_reason ?? '-' }}</div>
            </div>
            @endif

            {{-- Attachments --}}
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Photo') }}</div>
                <div class="col-sm-9">
                    @if ($vehicle->photo)
                        <img src="{{ $vehicle->photo->url }}" alt="Photo" width="150" class="rounded border">
                    @else
                        <span class="text-muted">{{ __('No photo uploaded') }}</span>
                    @endif
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Registration License') }}</div>
                <div class="col-sm-9">
                    @if ($vehicle->registrationLicense)
                        <img src="{{ $vehicle->registrationLicense->url }}" alt="Registration License" width="150" class="rounded border">
                    @else
                        <span class="text-muted">{{ __('No registration license uploaded') }}</span>
                    @endif
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Driving License') }}</div>
                <div class="col-sm-9">
                    @if ($vehicle->drivingLicense)
                        <img src="{{ $vehicle->drivingLicense->url }}" alt="Driving License" width="150" class="rounded border">
                    @else
                        <span class="text-muted">{{ __('No driving license uploaded') }}</span>
                    @endif
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Insurance Policy') }}</div>
                <div class="col-sm-9">
                    @if ($vehicle->insurancePolicy)
                        <img src="{{ $vehicle->insurancePolicy->url }}" alt="Insurance Policy" width="150" class="rounded border">
                    @else
                        <span class="text-muted">{{ __('No insurance policy uploaded') }}</span>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-layout>
