<?php

namespace App\Repositories;

use App\Enum\OrderDriverActionStatus;
use App\Models\Driver;
use App\Models\Order;
use App\Models\OrderDriverAction;

class OrderDriverActionRepository
{
    public function __construct(private readonly OrderDriverAction $model)
    {
        //
    }

    public function createDriversForOrder($order, $driverIds): void
    {
        $data = collect($driverIds)->map(fn ($id): array => ['driver_id' => $id])->toArray();
        $order->driverActions()->createMany($data);
    }

    public function findOrderDriver(Order $order, Driver $driver)
    {
        return $this->model
            ->where('order_id', $order->id)
            ->where('driver_id', $driver->id)
            ->first();
    }

    public function updateOtherDrivers($order, $driver)
    {
        return $this->model
            ->where('order_id', $order->id)
            ->where('driver_id', '!=', $driver->id)
            ->update(['status' => OrderDriverActionStatus::ACCEPTED_BY_ANOTHER->value]);
    }

    public function accept(OrderDriverAction $orderDriverAction): bool
    {
        if ($orderDriverAction->status == OrderDriverActionStatus::ACCEPTED) {
            return false;
        }

        return $orderDriverAction->update([
            'status' => OrderDriverActionStatus::ACCEPTED,
            'accepted_at' => now(),
        ]);
    }

    public function reject(OrderDriverAction $orderDriverAction): bool
    {
        if ($orderDriverAction->status === OrderDriverActionStatus::REJECTED) {
            return false;
        }

        return $orderDriverAction->update([
            'status' => OrderDriverActionStatus::REJECTED,
        ]);
    }

    public function resend(OrderDriverAction $orderDriverAction): bool
    {
        if ($orderDriverAction->resend_attempts >= 1) {
            return false;
        }

        return $orderDriverAction->update([
            'resend_attempts' => $orderDriverAction->resend_attempts + 1,
            'status' => OrderDriverActionStatus::ACCEPTED,
            'accepted_at' => now(),
        ]);
    }

    public function getAcceptedDriversForOrder(Order $order)
    {
        return $this->model
            ->where('order_id', $order->id)
            ->where('status', OrderDriverActionStatus::ACCEPTED)
            ->with(['driver.activeVehicle.vehicle'])
            ->orderBy('accepted_at', 'desc')
            ->get();
    }
}
