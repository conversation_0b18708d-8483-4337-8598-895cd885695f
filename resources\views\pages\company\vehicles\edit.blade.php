<x-layout :title="__('Edit Vehicle')">
    <x-session-message />

    <div class="card">
        <div class="card-header">{{ __('Edit Vehicle') }}</div>
        <div class="card-body">
            <form action="{{ route('company.vehicles.update', $vehicle) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <div class="row">
                    <x-translated-text-input name="name" label="Name" :model="$vehicle" />
                </div>

                <div class="row">
                    <!-- Transportation Method -->
                    <div class="mb-3 col-lg-4">
                        <label for="transportion_method_id" class="form-label"><b>{{ __('Transportation Method') }}</b></label>
                        <select name="transportion_method_id" id="transportion_method_id" class="form-select select2">
                            <option disabled>{{ __('Choose...') }}</option>
                            @foreach($transportionMethods as $method)
                                <option value="{{ $method->id }}" @selected(old('transportion_method_id', $vehicle->transportion_method_id) == $method->id)>
                                    {{ $method->name }}
                                </option>
                            @endforeach
                        </select>
                        <x-input-error name="transportion_method_id" />
                    </div>

                    <!-- Model -->
                    <div class="mb-3 col-lg-4">
                        <label for="model" class="form-label"><b>{{ __('Model') }}</b></label>
                        <input type="text" name="model" id="model" class="form-control" value="{{ old('model', $vehicle->model) }}">
                        <x-input-error name="model" />
                    </div>

                    <!-- Year -->
                    <div class="mb-3 col-lg-4">
                        <label for="year" class="form-label"><b>{{ __('Year') }}</b></label>
                        <select name="year" id="year" class="select2 form-select form-select-lg" data-allow-clear="true">
                            <option disabled selected>{{ __("Choose") }}...</option>
                            @for($year = 1980; $year <= now()->year; $year++)
                                <option value="{{ $year }}" @selected($vehicle->year == $year)>{{ $year }}</option>
                            @endfor
                        </select>
                        <x-input-error name="year" />
                    </div>
                </div>

                <div class="row">
                    <!-- Plate Number -->
                    <div class="mb-3 col-lg-4">
                        <label for="plate_number" class="form-label"><b>{{ __('Plate Number') }}</b></label>
                        <input type="text" name="plate_number" id="plate_number" class="form-control" value="{{ old('plate_number', $vehicle->plate_number) }}">
                        <x-input-error name="plate_number" />
                    </div>

                    <!-- License Number -->
                    <div class="mb-3 col-lg-4">
                        <label for="license_number" class="form-label"><b>{{ __('License Number') }}</b></label>
                        <input type="text" name="license_number" id="license_number" class="form-control" value="{{ old('license_number', $vehicle->license_number) }}">
                        <x-input-error name="license_number" />
                    </div>

                    <!-- License Expiration Date -->
                    <div class="mb-3 col-lg-4">
                        <label for="license_expiration_date" class="form-label"><b>{{ __('License Expiration Date') }}</b></label>
                        <input type="date" name="license_expiration_date" id="license_expiration_date" class="form-control" value="{{ old('license_expiration_date', $vehicle->license_expiration_date) }}">
                        <x-input-error name="license_expiration_date" />
                    </div>
                </div>

                <div class="row">
                    <!-- Vehicle Photo -->
                    <div class="mb-4 col-lg-4">
                        <label class="form-label"><b>{{ __('Vehicle Photo') }}</b></label>
                        <input type="file" name="photo" class="form-control" accept="image/*,application/pdf">
                        @if($vehicle->photo)
                            <div class="mt-2">
                                <a href="{{ $vehicle->photo->url }}" target="_blank">
                                    <img src="{{ $vehicle->photo->url }}" alt="Vehicle Photo" class="card-image">
                                </a>
                            </div>
                        @endif
                        <x-input-error name="photo" />
                    </div>

                    <!-- Registration License -->
                    <div class="mb-4 col-lg-4">
                        <label class="form-label"><b>{{ __('Registration License') }}</b></label>
                        <input type="file" name="registration_license" class="form-control" accept="image/*,application/pdf">
                        @if($vehicle->registrationLicense)
                            <div class="mt-2">
                                <a href="{{ $vehicle->registrationLicense->url }}" target="_blank">
                                    <img src="{{ $vehicle->registrationLicense->url }}" alt="Registration License" class="card-image">
                                </a>
                            </div>
                        @endif
                        <x-input-error name="registration_license" />
                    </div>

                    <!-- Driving License -->
                    <div class="mb-4 col-lg-4">
                        <label class="form-label"><b>{{ __('Driving License') }}</b></label>
                        <input type="file" name="driving_license" class="form-control" accept="image/*,application/pdf">
                        @if($vehicle->drivingLicense)
                            <div class="mt-2">
                                <a href="{{ $vehicle->drivingLicense->url }}" target="_blank">
                                    <img src="{{ $vehicle->drivingLicense->url }}" alt="Driving License" class="card-image">
                                </a>
                            </div>
                        @endif
                        <x-input-error name="driving_license" />
                    </div>
                </div>

                <div class="row">
                    <!-- Insurance Policy -->
                    <div class="mb-4 col-lg-4">
                        <label class="form-label"><b>{{ __('Insurance Policy') }}</b> ({{ __('Optional') }})</label>
                        <input type="file" name="insurance_policy" class="form-control" accept="image/*,application/pdf">
                        @if($vehicle->insurancePolicy)
                            <div class="mt-2">
                                <a href="{{ $vehicle->insurancePolicy->url }}" target="_blank">
                                    <img src="{{ $vehicle->insurancePolicy->url }}" alt="Insurance Policy" class="card-image">
                                </a>
                            </div>
                        @endif
                        <x-input-error name="insurance_policy" />
                    </div>
                </div>

                <button type="submit" class="btn btn-outline-primary btn-sm">{{ __('Update') }}</button>
            </form>
        </div>
    </div>

    @push('js')
        <script>
            $("#license_expiration_date").flatpickr();
        </script>
    @endpush
</x-layout>
