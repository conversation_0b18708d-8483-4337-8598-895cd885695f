<?php

namespace App\Services\Admin;

use App\DTO\Admin\CountryDTO;
use App\Models\Country;
use App\Repositories\CountryRepository;

class CountryService
{
    public function __construct(private readonly CountryRepository $countryRepository)
    {
        //
    }

    public function create(CountryDTO $dto): Country
    {
        return $this->countryRepository->create($dto);
    }

    public function update(Country $country, CountryDTO $dto): bool
    {
        return $this->countryRepository->update($country, $dto);
    }

    public function delete(Country $country)
    {
        $country->delete();

        return redirect()->back()->with('success', __('Country deleted successfully.'));
    }
}
