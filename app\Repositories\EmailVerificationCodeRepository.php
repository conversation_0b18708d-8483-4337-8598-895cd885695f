<?php

namespace App\Repositories;

use App\Models\EmailVerificationCode;

class EmailVerificationCodeRepository
{
    public function __construct(private readonly EmailVerificationCode $model) {}

    public function create($user_type, $email, $code)
    {
        return $this->model->create([
            'user_type' => $user_type,
            'email' => $email,
            'code' => $code,
            'created_at' => now(),
        ]);
    }

    public function get($user_type, $email, $code)
    {
        return $this->model->where(['user_type' => $user_type, 'email' => $email, 'code' => $code])->first();
    }

    public function getLatest($user_type, $email)
    {
        return $this->model->where(['user_type' => $user_type, 'email' => $email])->latest()->first();
    }

    public function deleteAll($user_type, $email)
    {
        return $this->model->where(['user_type' => $user_type, 'email' => $email])->delete();
    }
}
