<?php

namespace App\Repositories;

use App\Enum\OrderStatus;
use App\Models\User;

class UserRepository
{
    public function __construct(private readonly User $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByEmail(string $email)
    {
        return $this->model->where('email', $email)->first();
    }

    public function getByPhone($country_code, $phone)
    {
        return $this->model->where('country_code', $country_code)->where('phone', $phone)->first();
    }

    public function register(array $data)
    {
        return $this->model->create([
            'name' => $data['name'],
            'country_code' => $data['country_code'],
            'phone' => $data['phone'],
            'email' => $data['email'] ?? null,
            'gender' => $data['gender'] ?? null,
            'birth_date' => $data['birth_date'] ?? null,
            'last_login' => now(),
        ]);
    }

    public function create(array $data)
    {
        return $this->model->create([
            'name' => $data['name'],
            'country_code' => $data['country_code'],
            'phone' => $data['phone'],
            'email' => $data['email'] ?? null,
            'password' => bcrypt($data['password']),
            'birth_date' => $data['birth_date'] ?? null,
            'gender' => $data['gender'] ?? null,
        ]);
    }

    public function update(User $user, array $data): void
    {
        $user->update($data);
    }

    public function updatePassword(User $user, string $password): void
    {
        $user->update([
            'password' => bcrypt($password),
        ]);
    }

    public function invalidateUniqueData(User $user): void
    {
        $user->update([
            'phone' => getInvalidatedValue($user->phone),
            'email' => getInvalidatedValue($user->email),
        ]);
    }

    public function hasActiveOrder(User $user)
    {
        return $user->orders()->whereNotIn('status', [
            OrderStatus::DELIVERED->value,
            OrderStatus::PARTIALLY_DELIVERED->value,
            OrderStatus::CANCELLED->value,
            OrderStatus::DELIVERY_FAILED->value
        ])->exists();
    }
}
