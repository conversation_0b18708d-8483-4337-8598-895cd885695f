<?php

namespace App\Http\Requests\Driver\Order;

use App\Enum\OrderStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class FilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => 'required|in:current,completed,incomplete,cancelled_or_failed',
            'shipping_type_id' => 'required|exists:shipping_types,id',
            'is_express' => 'nullable|boolean',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after:date_from',
            'status' => [
                'nullable',
                Rule::in(array_map(fn ($s) => $s->value, OrderStatus::availableStatuses($this->shipping_type_id, $this->is_express))),
            ],
            'search' => 'nullable|string|max:50',
        ];
    }
}
