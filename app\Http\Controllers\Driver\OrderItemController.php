<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Http\Resources\Driver\OrderItemResource;
use App\Services\Driver\OrderItemService;

class OrderItemController extends Controller
{
    public function __construct(private readonly OrderItemService $orderItemService)
    {
        //
    }

    public function updateStatus(string $id)
    {
        $updatedItem = $this->orderItemService->updateStatus($id);

        return success(new OrderItemResource($updatedItem), 'Order item status updated.');
    }

    public function pickupFailed(string $id)
    {
        $updatedItem = $this->orderItemService->pickupFailed($id);

        return success(new OrderItemResource($updatedItem), 'Order item status updated.');
    }

    public function deliveryFailed(string $id)
    {
        $updatedItem = $this->orderItemService->deliveryFailed($id);

        return success(new OrderItemResource($updatedItem), 'Order item status updated.');
    }
}
