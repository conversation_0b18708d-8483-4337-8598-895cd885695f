<?php

namespace App\Repositories;

use App\Models\Driver;
use App\Models\DriverImmediateShippingCity;

class DriverImmediateShippingCityRepository
{
    public function __construct(private readonly DriverImmediateShippingCity $model) {}

    public function create(Driver $driver, string $city_id)
    {
        return $this->model->create([
            'driver_id' => $driver->id,
            'city_id' => $city_id,
        ]);
    }
}
