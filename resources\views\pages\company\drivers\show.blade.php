<x-layout :title="__('Driver') . ': ' . $driver->name">
    <x-session-message />
    <div class="card shadow-sm rounded-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ __('Driver') }}: {{ $driver->name }}</h5>
            @if($driver->approval_status == 'approved')
            <div>
                @if(auth('company')->user()->hasPermission('update driver'))
                <a class="btn btn-sm btn-outline-primary" href="{{ route('company.drivers.assign-vehicles', $driver->id) }}">
                    {{ __('Assign Vehicles') }}
                </a>
                <a class="btn btn-sm btn-outline-primary" href="{{ route('company.drivers.edit', $driver->id) }}">
                    <i class="fas fa-edit me-1"></i> {{ __('Edit') }}
                </a>
                @endif
            </div>
            @endif
        </div>

        <div class="card-body">

            <!-- Basic Info -->
            <div class="row mb-3">
                    <div class="col-sm-3 text-muted">{{ __('ID') }}</div>
                    <div class="col-sm-9">{{ $driver->id }}</div>
                </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Name') }}</div>
                <div class="col-sm-9">{{ $driver->name }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Email') }}</div>
                <div class="col-sm-9">{{ $driver->email ?? '-' }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Phone') }}</div>
                <div class="col-sm-9">{!! formatPhone($driver->country_code, $driver->phone) !!}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Company Code') }}</div>
                <div class="col-sm-9">{{  $driver->company->code }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Date of Birth') }}</div>
                <div class="col-sm-9">{{ $driver->birth_date ?? '-' }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Gender') }}</div>
                <div class="col-sm-9">{{ $driver->gender ? __($driver->gender) : '-' }}</div>
            </div>

            <!-- Bank Info -->
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Bank Name') }}</div>
                <div class="col-sm-9">{{ $driver->bank_name ?? '-' }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Bank Account Owner') }}</div>
                <div class="col-sm-9">{{ $driver->bank_account_owner ?? '-' }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Bank Account Number') }}</div>
                <div class="col-sm-9">{{ $driver->bank_account_number ?? '-' }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('IBAN') }}</div>
                <div class="col-sm-9">{{ $driver->iban ?? '-' }}</div>
            </div>
            @if ($driver->status == 'approved')
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Status') }}</div>
                <div class="col-sm-9">{!! statusBadge($driver->status) !!}</div>
            </div>
            @endif

            @if ($driver->approval_status != 'approved')
            @include('pages.company.drivers.registration-requests.approval-status')
            @endif

            <!-- Shipping Options -->
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Shipping Options') }}</div>
                <div class="col-sm-9">
                   @foreach ($driver->shippingTypes as $shippingType)
                        <div class="mb-3 p-3 border rounded">
                            <h6 class="mb-2 text-primary">
                                <strong>{{ $shippingType->type->name }}</strong>
                            </h6>

                            <div class="mb-2">
                                <span class="fw-bold">{{ __('Transportion Methods') }}:</span>
                                <span class="text-dark">{{ $shippingType->transportionMethods->pluck('name')->join(', ') }}</span>
                            </div>

                            @if($shippingType->type->id == 1)
                                <div class="mb-2">
                                    <span class="fw-bold">{{ __('Cities & Areas') }}:</span>
                                    <ul class="mb-0 ps-3 small">
                                        @foreach($driver->immediateShippingCities as $driverCity)
                                            <li>
                                                <b>{{ $driverCity->city->name }}</b>:
                                                {{ $driverCity->cityAreas->pluck('area.name')->join(', ') }}
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            @if($shippingType->type->id == 2)
                                <div class="mb-2">
                                    <span class="fw-bold">{{ __('Cities') }}:</span>
                                    <span class="text-dark">{{ $driver->intercityShippingCities->pluck('name')->join(', ') }}</span>
                                </div>
                            @endif

                            @if($shippingType->type->id == 3)
                                <div class="mb-2">
                                    <span class="fw-bold">{{ __('Cities') }}:</span>
                                    <span class="text-dark">{{ $driver->internationalShippingCities->pluck('name')->join(', ') }}</span>
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Attachments Section -->
            <div class="row mb-3">
                <div class="col-sm-12">
                    <div class="p-3">
                        <h6 class="text-primary mb-3"><strong>{{ __('Attachments') }}</strong></h6>

                        <div class="row mb-3">
                            <div class="col-sm-3 text-muted">{{ __('Profile Picture') }}</div>
                            <div class="col-sm-9">
                                @if ($driver->profileImage)
                                    <a href="{{ $driver->profileImage->url }}" target="_blank">
                                        <img src="{{ $driver->profileImage->url }}" alt="Profile Image" class="img-thumbnail" style="max-height: 150px;">
                                    </a>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-3 text-muted">{{ __('National ID') }}</div>
                            <div class="col-sm-9">
                                @if ($driver->idImage)
                                    <a href="{{ $driver->idImage->url }}" target="_blank">
                                        <img src="{{ $driver->idImage->url }}" alt="National ID" class="img-thumbnail" style="max-height: 150px;">
                                    </a>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-3 text-muted">{{ __('Driving Licence') }}</div>
                            <div class="col-sm-9">
                                @if ($driver->drivingLiscenceImage)
                                    <a href="{{ $driver->drivingLiscenceImage->url }}" target="_blank">
                                        <img src="{{ $driver->drivingLiscenceImage->url }}" alt="Driving Licence" class="img-thumbnail" style="max-height: 150px;">
                                    </a>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-3 text-muted">{{ __('Additional Attachments') }}</div>
                            <div class="col-sm-9 d-flex flex-wrap gap-3">
                                @forelse ($driver->additionalAttachments as $attachment)
                                    <a href="{{ $attachment->url }}" target="_blank">
                                        <img src="{{ $attachment->url }}" alt="Attachment" class="rounded border" style="max-height: 150px;">
                                    </a>
                                @empty
                                    <span class="text-muted">-</span>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</x-layout>
