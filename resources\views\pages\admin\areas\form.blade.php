<div class="row">
    <x-translated-text-input name="name" label="Name" :model="$area ?? null" />

    <div class="row">
        <div class="mb-3 col-lg-4">
            <label for="city_id" class="form-label"><b>{{ __('City') }}</b></label>
            <select name="city_id" id="city_id" class="form-select">
                <option value="">{{ __('Select City') }}</option>
                @foreach ($cities as $city)
                    <option value="{{ $city->id }}" @selected(old('city_id', $area->city_id ?? '') == $city->id)>
                        {{ $city->name }}
                    </option>
                @endforeach
            </select>
            <x-input-error name="city_id" />
        </div>

        <div class="mb-3 col-lg-4">
            <label style="margin-bottom:15px" class="form-label"><b>{{ __('Status') }}</b></label><br>
            <label class="switch">
                <input type="hidden" name="status" value="inactive">
                <input type="checkbox" class="switch-input" name="status" value="active" @checked(old('status', $area->status ?? 'active') == 'active')>
                <span class="switch-toggle-slider">
                    <span class="switch-on"></span>
                    <span class="switch-off"></span>
                </span>
                <span class="switch-label">{{ __('active') }}</span>
            </label>
            <x-input-error name="status" />
        </div>
    </div>

    <div class="col-12">
        <div class="mb-1">
            <label class="form-label">{{ __('Area Map') }}</label>
            <div id="map-container">
                <input id="map-search-input" type="text" placeholder="{{ __('Search for a location') }}">
                <div id="area-map"></div>
                <div id="map-controls">
                    <button type="button" id="draw-polygon" class="btn btn-sm btn-primary map-button">
                        <i data-feather="edit-2"></i> {{ __('Draw Area') }}
                    </button>
                    <button type="button" id="clear-polygon" class="btn btn-sm btn-danger map-button">
                        <i data-feather="trash"></i> {{ __('Clear') }}
                    </button>
                </div>
                <!-- Location coordinates inputs - automatically set to polygon center -->
                <div id="location-container">
                    <input type="hidden" name="location[type]" value="Point">
                    <input type="hidden" name="location[coordinates][]" value="" id="location-lng">
                    <input type="hidden" name="location[coordinates][]" value="" id="location-lat">
                </div>
            </div>
            <div id="coordinates-container">
                <!-- Coordinates inputs will be added here dynamically -->
                @if (old('area.coordinates'))
                    @php
                        $oldCoordinates = is_string(old('area.coordinates'))
                            ? json_decode(old('area.coordinates'))
                            : old('area.coordinates');
                    @endphp
                    @foreach ($oldCoordinates as $index => $coordinate)
                        @foreach ($coordinate as $i => $point)
                            <input type="hidden" name="area[coordinates][0][{{ $i }}][]"
                                value="{{ $point[0] }}">
                            <input type="hidden" name="area[coordinates][0][{{ $i }}][]"
                                value="{{ $point[1] }}">
                        @endforeach
                    @endforeach
                @elseif(isset($area->area) && isset($area->area->getCoordinates()[0]))
                    @foreach ($area->area->getCoordinates()[0] as $i => $point)
                        <input type="hidden" name="area[coordinates][0][{{ $i }}][]"
                            value="{{ $point[0] }}">
                        <input type="hidden" name="area[coordinates][0][{{ $i }}][]"
                            value="{{ $point[1] }}">
                    @endforeach
                @endif
            </div>
        </div>
        @php
            $coordinates = [];
            if (isset($area->area) && is_object($area->area)) {
                $coordinates = $area->area->getCoordinates();
                // Add debug info
                error_log('City area coordinates: ' . json_encode($coordinates));
            }
            $coordinatesJson = json_encode($coordinates);

            // Handle old input properly
            $oldAreaCoordinates = old('area.coordinates');
            if ($oldAreaCoordinates) {
                $oldCoordinatesJson = is_string($oldAreaCoordinates)
                    ? $oldAreaCoordinates
                    : json_encode($oldAreaCoordinates);
            } else {
                $oldCoordinatesJson = $coordinatesJson;
            }
        @endphp
        <input type="hidden" id="city-coordinates-json" value="">
        <input type="hidden" id="area-coordinates-json" value="{{ $oldCoordinatesJson }}">
        <!-- Debug info for coordinates -->
        <div class="d-none">
            <pre id="debug-coordinates">{{ $coordinatesJson }}</pre>
        </div>
        <input type="hidden" name="area[type]" value="Polygon">
        @error('area')
            <div class="text-danger">{{ $message }}</div>
        @enderror
        @error('area.coordinates')
            <div class="text-danger">{{ $message }}</div>
        @enderror
        @error('area.coordinates.0')
            <div class="text-danger">
                {{ __('Please draw a valid polygon with at least 3 points.') }}</div>
        @enderror
        @if (session('errors') && session('errors')->has('msg'))
            <div class="text-danger">{{ __('Please draw a valid polygon ') }}</div>
        @endif

        @error('location')
            <div class="text-danger">{{ $message }}</div>
        @enderror
        @error('location.coordinates')
            <div class="text-danger">{{ __('Please set a valid location point') }}</div>
        @enderror
    </div>
</div>
