<?php

namespace App\Http\Requests\Admin\Vehicle;

use App\Rules\LocalizedAlpha;
use App\Rules\ValidMedia;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => ['required', 'array'],
            'model' => ['required', 'string', 'min:3', 'max:50'],
            'year' => ['required', 'integer', 'min:1900', 'max:'.date('Y')],
            'license_expiration_date' => ['required', 'date', 'after_or_equal:today'],
            'plate_number' => [
                'required',
                'string',
                'min:3',
                'max:50',
                Rule::unique('vehicles', 'plate_number')->whereNull('deleted_at'),
            ],
            'license_number' => [
                'required',
                'string',
                'min:3',
                'max:50',
                Rule::unique('vehicles', 'license_number')->whereNull('deleted_at'),
            ],
            'company_id' => [
                'required',
                Rule::exists('companies', 'id')
                    ->where('status', 'active')
                    ->where('approval_status', 'approved'),
            ],
            'transportion_method_id' => ['required', 'exists:transportion_methods,id'],
            'image' => ['required', new ValidMedia(['image'])],
            'registration_license' => ['required', new ValidMedia(['image'])],
            'driving_license' => ['required', new ValidMedia(['image'])],
            'insurance_policy' => ['nullable', new ValidMedia(['image'])],
            'status' => ['required', 'string', 'in:active,inactive'],
        ];

        foreach (config('app.locales', ['en', 'ar']) as $locale) {
            $rules["name.{$locale}"] = ['required', 'string', 'min:3', 'max:255', new LocalizedAlpha, Rule::unique('vehicles', "name->{$locale}")];
        }

        return $rules;
    }
}
