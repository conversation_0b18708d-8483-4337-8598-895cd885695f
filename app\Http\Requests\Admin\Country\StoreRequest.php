<?php

namespace App\Http\Requests\Admin\Country;

use App\Rules\LocalizedAlpha;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->route('country')?->id ?? null;

        $rules = [
            'name' => ['required', 'array'],
            'code' => ['required', 'string', 'max:5', Rule::unique('countries', 'code')->ignore($id)],
            'abbv' => ['required', 'string', 'regex:/^[a-zA-Z]+$/', 'max:5', Rule::unique('countries', 'abbv')->ignore($id)],
        ];

        foreach (config('app.locales', ['en', 'ar']) as $locale) {
            $rules["name.{$locale}"] = ['required', 'string', 'min:3', 'max:255', new LocalizedAlpha, Rule::unique('countries', "name->{$locale}")->ignore($id)];
        }

        return $rules;
    }
}
