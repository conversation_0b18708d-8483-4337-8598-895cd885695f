<?php

namespace App\Http\Controllers\Company;

use App\DataTables\Company\DriversDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Company\AssignDriverVehiclesRequest;
use App\Http\Requests\Company\DriverRequest;
use App\Models\Driver;
use App\Repositories\CityRepository;
use App\Repositories\CountryRepository;
use App\Repositories\TransportionMethodRepository;
use App\Repositories\VehicleRepository;
use App\Services\Admin\CompanyService;
use App\Services\Company\DriverService;

class DriverController extends Controller
{
    public function __construct(
        private readonly DriverService $driverService,
        private readonly CompanyService $companyService,
        private readonly TransportionMethodRepository $transportionMethodRepository,
        private readonly CountryRepository $countryRepository,
        private readonly CityRepository $cityRepository,
        private readonly VehicleRepository $vehicleRepository
    ) {}

    public function index(DriversDataTable $dataTable)
    {
        $transportionMethods = $this->transportionMethodRepository->getActiveMethods();

        return $dataTable->render('pages.company.drivers.index', ['transportionMethods' => $transportionMethods]);
    }

    public function create()
    {
        $countries = $this->countryRepository->getAll();
        $immediateCities = $this->companyService->getAssociatedImmediateCities(auth('company')->user()->company);
        $shippingTypes = auth('company')->user()->company->shippingTypes;
        $intercityShippingPickupCities = auth('company')->user()->company->intercityShippingPickupCities;
        $internationalShippingCities = $this->cityRepository->getActiveCities(false);

        return view('pages.company.drivers.create', ['countries' => $countries, 'shippingTypes' => $shippingTypes, 'immediateCities' => $immediateCities, 'intercityShippingPickupCities' => $intercityShippingPickupCities, 'internationalShippingCities' => $internationalShippingCities]);
    }

    public function store(DriverRequest $request)
    {
        $this->driverService->create($request->all());

        return to_route('company.drivers.index')->with('success', __('Created successfully'));
    }

    public function show(Driver $driver)
    {
        $driver = $this->driverService->getDriver($driver);

        return view('pages.company.drivers.show', ['driver' => $driver]);
    }

    public function edit(Driver $driver)
    {
        $driver = $this->driverService->getDriver($driver);
        $countries = $this->countryRepository->getAll();
        $immediateCities = $this->companyService->getAssociatedImmediateCities(auth('company')->user()->company);
        $shippingTypes = auth('company')->user()->company->shippingTypes;
        $intercityShippingPickupCities = auth('company')->user()->company->intercityShippingPickupCities;
        $internationalShippingCities = $this->cityRepository->getActiveCities(false);

        return view('pages.company.drivers.edit', ['driver' => $driver, 'countries' => $countries, 'shippingTypes' => $shippingTypes, 'immediateCities' => $immediateCities, 'intercityShippingPickupCities' => $intercityShippingPickupCities, 'internationalShippingCities' => $internationalShippingCities]);
    }

    public function update(DriverRequest $request, Driver $driver)
    {
        $driver = $this->driverService->getDriver($driver);

        $this->driverService->update($driver, $request->validated());

        return back()->with('success', __('Updated successfully'));
    }

    public function destroy(Driver $driver)
    {
        $this->driverService->delete($driver);

        return to_route('company.drivers.index')->with('success', __('Deleted successfully'));
    }

    public function assignVehiclesView(Driver $driver)
    {
        $driver = $this->driverService->getDriver($driver);
        $availableVehicles = $this->vehicleRepository->getAvailableVehiclesForDriver($driver);
        $pendingVehicles = $this->driverService->getPendingDriverVehicles($driver);
        $approvedVehicles = $this->driverService->getApprovedDriverVehicles($driver);

        return view('pages.company.drivers.assign-vehicles', ['driver' => $driver, 'availableVehicles' => $availableVehicles, 'pendingVehicles' => $pendingVehicles, 'approvedVehicles' => $approvedVehicles]);
    }

    public function assignVehicles(AssignDriverVehiclesRequest $request, Driver $driver)
    {
        $this->driverService->assignVehicles($driver, $request->validated());

        return to_route('company.drivers.show', $driver)->with('success', __('Vehicles Assigned successfully'));
    }
}
