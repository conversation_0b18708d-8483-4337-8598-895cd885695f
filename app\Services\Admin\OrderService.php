<?php

namespace App\Services\Admin;

use App\Enum\OrderStatus;
use App\Models\Order;
use App\Repositories\DriverRepository;
use App\Repositories\OrderRepository;
use Illuminate\Support\Facades\DB;

class OrderService
{
    public function __construct(private OrderRepository $orderRepository, private DriverRepository $driverRepository) {}

    public function cancel(Order $order, string $reason)
    {
        if ($order->status == OrderStatus::CANCELLED) {
            return error(__('Order already cancelled'));
        }

        if (! in_array($order->status->value, [
            OrderStatus::READY_TO_PICKUP->value,
            OrderStatus::HEADING_TO_PICKUP->value,
        ])) {
            return error(__('Order cannot be cancelled.'));
        }

        DB::transaction(function () use ($order, $reason) {
            $this->orderRepository->update($order, [
                'status' => OrderStatus::CANCELLED->value,
                'cancel_reason' => $reason,
            ]);

            if ($order->driver) {
                $this->driverRepository->update($order->driver, ['is_available' => true]);
            }
        });

        return success(__('Order cancelled successfully.'));
    }
}
