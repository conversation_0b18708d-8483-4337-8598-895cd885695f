<?php

namespace App\Http\Requests\User;

use App\Rules\ValidPhone;
use Illuminate\Foundation\Http\FormRequest;

class ConfirmOTPRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'country_code' => ['required', 'string', 'exists:countries,code'],
            'phone' => ['required', 'string', new ValidPhone($this->country_code)],
            'code' => ['required', 'string'],
        ];
    }
}
