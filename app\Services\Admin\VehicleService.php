<?php

namespace App\Services\Admin;

use App\DTO\Admin\VehicleDTO;
use App\Models\Vehicle;
use App\Repositories\VehicleRepository;
use App\Services\MediaService;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class VehicleService
{
    public function __construct(
        private readonly VehicleRepository $vehicleRepository,
        private readonly MediaService $mediaService
    ) {
        //
    }

    public function create(VehicleDTO $dto): Vehicle
    {
        $vehicle = $this->vehicleRepository->create($dto->toArray());
        $this->updateMedia($vehicle, $dto);

        return $vehicle;
    }

    public function update(Vehicle $vehicle, VehicleDTO $dto): bool
    {
        if ($vehicle->approval_status === 'pending') {
            $dto->status = $vehicle->status;
        }

        $dto->approval_status = $vehicle->approval_status;
        $this->vehicleRepository->update($vehicle, $dto->toArray());
        $this->updateMedia($vehicle, $dto);

        return true;
    }

    public function delete(Vehicle $vehicle): void
    {
        if ($this->vehicleRepository->hasActiveOrder($vehicle)) {
            throw new BadRequestHttpException('vehicle has an active order');
        }

        $vehicle->delete();
    }

    public function updateApprovalStatus(Vehicle $vehicle, string $status): void
    {
        if ($vehicle->approval_status === 'approved') {
            throw new BadRequestHttpException('Vehicle is already approved');
        }

        if ($vehicle->approval_status === 'rejected') {
            throw new BadRequestHttpException('Vehicle is already rejected');
        }

        if ($status === 'approved') {
            $this->updateToApproved($vehicle);
        }

        if ($status === 'rejected') {
            $this->updateToRejected($vehicle);
        }
    }

    private function updateToApproved(Vehicle $vehicle): void
    {
        if ($vehicle->approval_status !== 'pending') {
            throw new BadRequestHttpException('Vehicle is not in pending state');
        }

        $this->vehicleRepository->update($vehicle, [
            'statue' => 'active',
            'approval_status' => 'approved',
        ]);
    }

    private function updateToRejected(Vehicle $vehicle): void
    {
        if ($vehicle->approval_status !== 'pending') {
            throw new BadRequestHttpException('Vehicle is not in pending state');
        }

        $this->vehicleRepository->update($vehicle, [
            'status' => 'inactive',
            'approval_status' => 'rejected',
            'rejection_reason' => request('rejection_reason'),
        ]);
    }

    private function updateMedia(Vehicle $vehicle, VehicleDTO $dto): void
    {
        if ($dto->image instanceof \Illuminate\Http\UploadedFile) {
            $this->mediaService->delete($vehicle->photo);
            $this->mediaService->save($vehicle, $dto->image, 'vehicles', 'photo');
        }
        if ($dto->registration_license instanceof \Illuminate\Http\UploadedFile) {
            $this->mediaService->delete($vehicle->registrationLicense);
            $this->mediaService->save($vehicle, $dto->registration_license, 'vehicles', 'registration_license');
        }
        if ($dto->driving_license instanceof \Illuminate\Http\UploadedFile) {
            $this->mediaService->delete($vehicle->drivingLicense);
            $this->mediaService->save($vehicle, $dto->driving_license, 'vehicles', 'driving_license');
        }
        if ($dto->insurance_policy instanceof \Illuminate\Http\UploadedFile) {
            $this->mediaService->delete($vehicle->insurancePolicy);
            $this->mediaService->save($vehicle, $dto->insurance_policy, 'vehicles', 'insurance_policy');
        }
    }
}
