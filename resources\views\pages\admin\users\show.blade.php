<x-layout :title="__('Users')">
    <div class="card shadow-sm rounded-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ __('User') }}: {{ $user->name }}</h5>
            @if(auth('admin')->user()->hasPermission('update user'))
            <a class="btn btn-sm btn-outline-primary" href="{{ route('admin.users.edit', $user->id) }}">
                <i class="fas fa-edit me-1"></i> {{ __('Edit') }}
            </a>
            @endif
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Name') }}</div>
                <div class="col-sm-9">{{ $user->name }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Email') }}</div>
                <div class="col-sm-9">{{ $user->email }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Phone') }}</div>
                <div class="col-sm-9">{!! formatPhone($user->country_code, $user->phone) !!}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Birth Date') }}</div>
                <div class="col-sm-9">{{ $user->birth_date }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Gender') }}</div>
                <div class="col-sm-9">{{ __($user->gender) }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Status') }}</div>
                <div class="col-sm-9">{!! statusBadge($user->status) !!}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Created At') }}</div>
                <div class="col-sm-9" dir="ltr">{{ formatDateTime($user->created_at) }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Last Login') }}</div>
                <div class="col-sm-9" dir="ltr">{{ $user->last_login ? formatDateTime($user->last_login) : '-' }}</div>
            </div>
            <div class="row mb-3">
                <div class="col-sm-3 text-muted">{{ __('Number of Completed Orders') }}</div>
                <div class="col-sm-9" dir="ltr">{{ $user->completedOrders()->count() }}</div>
            </div>
        </div>
    </div>
</x-layout>