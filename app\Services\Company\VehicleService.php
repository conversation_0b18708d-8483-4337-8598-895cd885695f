<?php

namespace App\Services\Company;

use App\Models\Vehicle;
use App\Repositories\VehicleRepository;
use App\Services\MediaService;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class VehicleService
{
    public function __construct(
        private readonly MediaService $mediaService,
        private readonly VehicleRepository $vehicleRepository
    ) {}

    public function getVehicle(Vehicle $vehicle): Vehicle
    {
        if ($vehicle->company_id != auth('company')->user()->company_id) {
            abort(404);
        }

        return $vehicle;
    }

    public function create(array $data)
    {
        $data['company_id'] = auth('company')->user()->company_id;
        $data['status'] = 'inactive';

        return DB::transaction(function () use ($data) {

            $vehicle = $this->vehicleRepository->create($data);

            if (request('photo')) {
                $this->mediaService->save($vehicle, request('photo'), 'vehicles', 'photo');
            }

            if (request('registration_license')) {
                $this->mediaService->save($vehicle, request('registration_license'), 'vehicles', 'registration_license');
            }

            if (request('driving_license')) {
                $this->mediaService->save($vehicle, request('driving_license'), 'vehicles', 'driving_license');
            }
            if (request('insurance_policy')) {
                $this->mediaService->save($vehicle, request('insurance_policy'), 'vehicles', 'insurance_policy');
            }

            return $vehicle;
        });
    }

    public function update(Vehicle $vehicle, array $data): void
    {
        if ($vehicle->company_id != auth('company')->user()->company_id) {
            abort(404);
        }

        if ($vehicle->approval_status == 'rejected') {
            throw new BadRequestHttpException("Can't update rejected vehicle");
        }

        DB::transaction(function () use ($vehicle, $data): void {

            $this->vehicleRepository->update($vehicle, $data);

            if (request('photo')) {
                $this->mediaService->delete($vehicle->photo);
                $this->mediaService->save($vehicle, request('photo'), 'vehicles', 'photo');
            }

            if (request('registration_license')) {
                $this->mediaService->delete($vehicle->registrationLicense);
                $this->mediaService->save($vehicle, request('registration_license'), 'vehicles', 'registration_license');
            }

            if (request('driving_license')) {
                $this->mediaService->delete($vehicle->drivingLicense);
                $this->mediaService->save($vehicle, request('driving_license'), 'vehicles', 'driving_license');
            }
            if (request('insurance_policy')) {
                $this->mediaService->delete($vehicle->insurancePolicy);
                $this->mediaService->save($vehicle, request('insurance_policy'), 'vehicles', 'insurance_policy');
            }
        });
    }

    public function delete(Vehicle $vehicle): void
    {
        if ($vehicle->company_id != auth('company')->user()->company_id) {
            abort(404);
        }

        if ($this->vehicleRepository->hasActiveOrder($vehicle)) {
            throw new BadRequestHttpException('vehicle has an active order');
        }

        $vehicle->delete();
    }
}
