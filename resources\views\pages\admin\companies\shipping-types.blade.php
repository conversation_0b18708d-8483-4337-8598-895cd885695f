<div class="row mb-3">
    <div class="col-sm-3 text-muted"></div>
    <div class="col-sm-9">
        @foreach ($company->shippingTypes as $shippingType)
            <div class="mb-3 p-3 border rounded">
                <h6 class="mb-2 text-primary">
                    <strong>{{ $shippingType->shippingType->name }}</strong>
                </h6>

                @if($shippingType->shippingType->id != 1)
                <div class="mb-3">
                    <span class="fw-bold">{{ __('Express Delivery') }}:</span>
                    {{ $shippingType->has_express_delivery ? __('Yes') : __('No') }}
                </div>
                @endif

                @foreach ($shippingType->sizes as $size)
                    <div class="mb-3">
                        <span class="fw-bold">- {{ __('Size') }}:</span>
                        {{ $size->size->name }}

                        @if ($size->transportionMethods->isNotEmpty())
                            <div class="mt-1 ps-3">
                                <span class="fw-bold">{{ __('Transportion Methods') }}:</span>
                                {{ $size->transportionMethods->pluck('transportionMethod.name')->join(', ') }}
                            </div>
                        @endif
                    </div>
                @endforeach

                @if($shippingType->shippingType->id == 1)
                    <!-- Immediate -->
                    <div class="mb-2">
                        <span class="fw-bold">{{ __('Cities & Areas') }}:</span>
                        <ul class="mb-0 ps-3 small">
                            @foreach($company->immediateShippingCities as $shippingCity)
                                <li>
                                    <b>{{ $shippingCity->city->name }}</b>:
                                    {{ $shippingCity->areas->pluck('name')->join(', ') }}
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if($shippingType->shippingType->id == 2)
                    <!-- Intercity -->
                    <div class="mb-2">
                        <span class="fw-bold">{{ __('Pickup Cities') }}:</span>
                        {{ $company->intercityShippingPickupCities->pluck('name')->join(', ') }}
                    </div>
                    <div class="mb-2">
                        <span class="fw-bold">{{ __('Delivery Cities') }}:</span>
                        {{ $company->intercityShippingDeliveryCities->pluck('name')->join(', ') }}
                    </div>
                @endif

                @if($shippingType->shippingType->id == 3)
                    <!-- International -->
                    <div class="mb-2">
                        <span class="fw-bold">{{ __('Countries') }}:</span>
                        {{ $company->internationalShippingCountires->pluck('name')->join(', ') }}
                    </div>
                @endif
            </div>
        @endforeach
    </div>
</div>