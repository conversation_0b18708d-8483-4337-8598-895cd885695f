<?php

namespace App\Http\Controllers;

use App\Http\Requests\GetAreasRequest;
use App\Http\Resources\AreaResource;
use App\Http\Resources\CityResource;
use App\Http\Resources\CountryResource;
use App\Http\Resources\ShippingSizeResource;
use App\Http\Resources\ShippingTypeResource;
use App\Http\Resources\TransportionMethodResource;
use App\Repositories\AreaRepository;
use App\Repositories\CityRepository;
use App\Repositories\CountryRepository;
use App\Repositories\ShippingSizeRepository;
use App\Repositories\ShippingTypeRepository;
use App\Repositories\TransportionMethodRepository;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class DDLController extends Controller
{
    public function __construct(
        private readonly ShippingTypeRepository $shippingTypeRepository,
        private readonly ShippingSizeRepository $shippingSizeRepository,
        private readonly TransportionMethodRepository $transportionMethodRepository,
        private readonly CountryRepository $countryRepository,
        private readonly CityRepository $cityRepository,
        private readonly AreaRepository $areaRepository
    ) {}

    public function countries(Request $request)
    {
        $countries = $this->countryRepository->getAll();

        return success(CountryResource::collection($countries));
    }

    public function cities(Request $request)
    {
        $cities = $this->cityRepository->getActiveCities($request->with_areas);

        return success(CityResource::collection($cities));
    }

    public function shippingTypes()
    {
        $shippingTypes = $this->shippingTypeRepository->getAll();

        return success(ShippingTypeResource::collection($shippingTypes));
    }

    public function shippingSizes()
    {
        $shippingSizes = $this->shippingSizeRepository->getAll();

        return success(ShippingSizeResource::collection($shippingSizes));
    }

    public function transportionMethods()
    {
        $transportionMethods = $this->transportionMethodRepository->getActiveMethods();

        return success(TransportionMethodResource::collection($transportionMethods));
    }

    public function areas(GetAreasRequest $request)
    {
        $city = $this->cityRepository->getById($request->city_id);

        if (! $city) {
            throw new NotFoundHttpException;
        }

        $areas = $this->areaRepository->getByCity($city);

        return success(AreaResource::collection($areas));
    }
}
