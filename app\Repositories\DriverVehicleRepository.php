<?php

namespace App\Repositories;

use App\Models\Driver;
use App\Models\DriverVehicle;

class DriverVehicleRepository
{
    public function __construct(private readonly DriverVehicle $model) {}

    public function get(string $driver_id, string $vehicle_id)
    {
        return $this->model->where('driver_id', $driver_id)
            ->where('vehicle_id', $vehicle_id)
            ->first();
    }

    public function create(string $driver_id, string $vehicle_id)
    {
        return $this->model->create([
            'driver_id' => $driver_id,
            'vehicle_id' => $vehicle_id,
        ]);
    }

    public function update(DriverVehicle $driverVehicle, array $data)
    {
        return $driverVehicle->update($data);
    }

    public function delete(string $driver_id, string $vehicle_id): void
    {
        $this->model->where('driver_id', $driver_id)
            ->where('vehicle_id', $vehicle_id)
            ->delete();
    }

    public function getPendingDriverVehicles(Driver $driver)
    {
        return $driver->vehicles()
            ->with('vehicle')
            ->pending()
            ->get();
    }

    public function getApprovedDriverVehicles(Driver $driver)
    {
        return $driver->vehicles()
            ->with('vehicle')
            ->approved()
            ->get();
    }

    public function getDriverVehicles(Driver $driver)
    {
        return $driver->vehicles()
            ->with('vehicle.transportionMethod')
            ->where('approval_status', request('approval_status'))
            ->get();
    }

    public function hasActiveVehicle(Driver $driver)
    {
        return $driver->vehicles()
            ->active()
            ->exists();
    }
}
