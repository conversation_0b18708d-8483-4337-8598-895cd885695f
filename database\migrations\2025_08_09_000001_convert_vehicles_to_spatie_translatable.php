<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add JSON columns to vehicles table
        Schema::table('vehicles', function (Blueprint $table) {
            $table->json('name')->nullable()->after('company_id');
        });

        // Migrate data from vehicle_translations to vehicles
        $vehicles = DB::table('vehicles')->get();
        foreach ($vehicles as $vehicle) {
            $translations = DB::table('vehicle_translations')
                ->where('vehicle_id', $vehicle->id)
                ->get()
                ->mapWithKeys(function ($item) {
                    return [
                        $item->locale => [
                            'name' => $item->name,
                        ],
                    ];
                })
                ->toArray();

            // Prepare name and description JSON
            $nameTranslations = [];

            foreach ($translations as $locale => $data) {
                $nameTranslations[$locale] = $data['name'];
            }

            DB::table('vehicles')
                ->where('id', $vehicle->id)
                ->update([
                    'name' => json_encode($nameTranslations),
                ]);
        }

        // Drop translation table
        Schema::dropIfExists('vehicle_translations');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate translation table
        Schema::create('vehicle_translations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('vehicle_id')->constrained()->cascadeOnDelete();
            $table->string('locale')->index();
            $table->unique(['vehicle_id', 'locale']);
        });

        // Migrate data back from JSON to translation table
        $vehicles = DB::table('vehicles')->get();
        foreach ($vehicles as $vehicle) {
            if ($vehicle->name) {
                $nameTranslations = json_decode($vehicle->name, true);

                foreach ($nameTranslations as $locale => $name) {
                    DB::table('vehicle_translations')->insert([
                        'vehicle_id' => $vehicle->id,
                        'locale' => $locale,
                        'name' => $name,
                    ]);
                }
            }
        }

        // Drop JSON columns
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropColumn(['name', 'description']);
        });
    }
};
