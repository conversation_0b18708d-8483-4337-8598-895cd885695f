<?php

namespace App\Services\User;

use App\Repositories\UserRepository;
use App\Services\MediaService;

class UserService
{
    public function __construct(private readonly UserRepository $userRepository, private readonly MediaService $mediaService) {}

    public function register(array $data)
    {
        $user = $this->userRepository->register($data);

        if (request('profile_image')) {
            $this->mediaService->save($user, request('profile_image'), 'users');
        }

        return $user;
    }
}
