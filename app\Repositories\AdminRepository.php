<?php

namespace App\Repositories;

use App\Models\Admin;

class AdminRepository
{
    public function __construct(private readonly Admin $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByEmail(string $email)
    {
        return $this->model->where('email', $email)->first();
    }

    public function getByPhone($country_code, $phone)
    {
        return $this->model->where('country_code', $country_code)->where('phone', $phone)->first();
    }

    public function create(array $data)
    {
        return $this->model->create([
            'name' => $data['name'],
            'country_code' => $data['country_code'],
            'phone' => $data['phone'],
            'email' => $data['email'] ?? null,
            'password' => $data['password'],
            'role_id' => $data['role_id'],
        ]);
    }

    public function update(Admin $admin, array $data): void
    {
        $admin->update($data);
    }

    public function updatePassword(Admin $admin, string $password): void
    {
        $admin->update([
            'password' => bcrypt($password),
        ]);
    }

    public function invalidateUniqueData(Admin $admin): void
    {
        $admin->update([
            'phone' => getInvalidatedValue($admin->phone),
            'email' => getInvalidatedValue($admin->email),
        ]);
    }
}
