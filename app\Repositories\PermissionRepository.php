<?php

namespace App\Repositories;

use App\Models\Permission;

class PermissionRepository
{
    public function __construct(private readonly Permission $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByIds(array $ids)
    {
        return $this->model->whereIn('id', $ids)->orderBy('id')->get();
    }

    public function getAll(string $type)
    {
        return $this->model->where('type', $type)->orderBy('id')->get();
    }

    public function getAllGrouped(string $type)
    {
        return $this->model->where('type', $type)->orderBy('id')->get()->groupBy('group');
    }
}
