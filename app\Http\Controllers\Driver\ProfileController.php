<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Http\Requests\Driver\ConfirmUpdatePhoneOtpRequest;
use App\Http\Requests\Driver\ProfileRequest;
use App\Http\Requests\Driver\SendUpdatePhoneOtpRequest;
use App\Http\Requests\Driver\UpdateAppLocaleRequest;
use App\Http\Resources\Driver\ProfileResource;
use App\Services\Driver\ProfileService;
use Illuminate\Http\JsonResponse;

class ProfileController extends Controller
{
    public function __construct(private readonly ProfileService $profileService) {}

    public function get(): JsonResponse
    {
        $driver = auth('driver')->user();

        return success(new ProfileResource($driver));
    }

    public function update(ProfileRequest $request): JsonResponse
    {
        $this->profileService->update($request->validated());

        return success(true);
    }

    public function sendUpdatePhoneOtp(SendUpdatePhoneOtpRequest $request): JsonResponse
    {
        $this->profileService->sendUpdatePhoneOtp($request->validated());

        return success(true);
    }

    public function confirmUpdatePhoneOtp(ConfirmUpdatePhoneOtpRequest $request): JsonResponse
    {
        $this->profileService->confirmUpdatePhone($request->validated());

        return success(true);
    }

    public function updateAppLocale(UpdateAppLocaleRequest $request): JsonResponse
    {
        $this->profileService->updateAppLocale();

        return success(true);
    }

    public function updatePushNotificationStatus(): JsonResponse
    {
        return success($this->profileService->updatePushNotificationStatus());
    }

    public function delete(): JsonResponse
    {
        $this->profileService->delete();

        return success(true);
    }
}
