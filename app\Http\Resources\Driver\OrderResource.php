<?php

namespace App\Http\Resources\Driver;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_number' => $this->order_number,
            'created_at' => formatDateTime($this->created_at),
            'shipping_type' => [
                'id' => $this->shippingType->id,
                'name' => __($this->shippingType->name.' shipping'),
            ],
            'shipping_size' => [
                'id' => $this->shippingSize->id,
                'name' => __($this->shippingSize->name),
            ],
            'is_express' => $this->is_express,
            'vehicle' => VehicleResource::make($this->vehicle),
            'cost' => $this->cost,
            'fees' => $this->fees,
            'total' => $this->total,
            'payment_method' => $this->payment_method,
            'pickup_location' => $this->resolvePickupLocation(),
            'dropoff_location' => $this->resolveDropoffLocation(),
            'status' => $this->status,
            'eta_to_pickup' => $this->resolveEtaToPickup(),
            'items' => OrderItemResource::collection($this->whenLoaded('items')),
        ];
    }

    private function resolvePickupLocation(): mixed
    {
        if ($this->pickup_location && ! $this->pickup_address_id) {
            return [
                'lat' => (string) $this->pickup_location->latitude,
                'lng' => (string) $this->pickup_location->longitude,
            ];
        }

        if ($this->relationLoaded('pickupAddress') && $this->pickupAddress) {
            return new AddressResource($this->pickupAddress);
        }

        return null;
    }

    private function resolveDropoffLocation(): mixed
    {
        if (! $this->is_express) {
            // TODO: return hub location assigned to driver
            return null;
        }

        if (! $this->relationLoaded('items') || $this->items->count() !== 1) {
            return null;
        }

        $item = $this->items->first();

        if ($item->dropoff_location && ! $item->dropoff_address_id) {
            return [
                'lat' => (string) $item->dropoff_location->latitude,
                'lng' => (string) $item->dropoff_location->longitude,
            ];
        }

        if ($item->relationLoaded('dropoffAddress') && $item->dropoffAddress) {
            $item->dropoffAddress->load(['area', 'area.city']);

            return new AddressResource($item->dropoffAddress);
        }

        return null;
    }

    private function resolveEtaToPickup(): string
    {
        if (! $this->relationLoaded('driver') || ! $this->driver) {
            return null;
        }

        $pickupLocation = $this->pickup_address_id
            ? $this->pickupAddress->location
            : $this->pickup_location;

        return estimateTimeFromDistance(
            $this->driver->current_lat,
            $this->driver->current_lng,
            $pickupLocation->latitude,
            $pickupLocation->longitude
        );
    }
}
