<?php

namespace App\Http\Controllers\Company;

use App\DataTables\Company\VehiclesDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Company\VehicleRequest;
use App\Models\Vehicle;
use App\Services\Admin\CompanyService;
use App\Services\Company\VehicleService;

class VehicleController extends Controller
{
    public function __construct(
        private readonly VehicleService $vehicleService,
        private readonly CompanyService $companyService
    ) {}

    public function index(VehiclesDataTable $dataTable)
    {
        $transportionMethods = $this->companyService->getAssociatedTransportionMethods(auth('company')->user()->company);

        return $dataTable->render('pages.company.vehicles.index', ['transportionMethods' => $transportionMethods]);
    }

    public function create()
    {
        $transportionMethods = $this->companyService->getAssociatedTransportionMethods(auth('company')->user()->company);

        return view('pages.company.vehicles.create', ['transportionMethods' => $transportionMethods]);
    }

    public function store(VehicleRequest $request)
    {
        $this->vehicleService->create($request->validated());

        return to_route('company.vehicles.index')->with('success', __('Created successfully'));
    }

    public function show(Vehicle $vehicle)
    {
        $vehicle = $this->vehicleService->getVehicle($vehicle);

        return view('pages.company.vehicles.show', ['vehicle' => $vehicle]);
    }

    public function edit(Vehicle $vehicle)
    {
        $vehicle = $this->vehicleService->getVehicle($vehicle);
        $transportionMethods = $this->companyService->getAssociatedTransportionMethods(auth('company')->user()->company);

        return view('pages.company.vehicles.edit', ['vehicle' => $vehicle, 'transportionMethods' => $transportionMethods]);
    }

    public function update(Vehicle $vehicle, VehicleRequest $request)
    {
        $this->vehicleService->update($vehicle, $request->validated());

        return back()->with('success', __('Updated successfully'));
    }

    public function destroy(Vehicle $vehicle)
    {
        $this->vehicleService->delete($vehicle);

        return to_route('company.vehicles.index')->with('success', __('Deleted successfully'));
    }
}
