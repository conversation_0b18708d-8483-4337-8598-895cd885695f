@php
$canShow = auth('company')->user()->hasPermission('show admin') && $admin->id != $admin->company->superAdmin->id && $admin->id != auth('company')->id();
$canEdit = auth('company')->user()->hasPermission('update admin') && $admin->id != $admin->company->superAdmin->id && $admin->id != auth('company')->id();
$canDelete = auth('company')->user()->hasPermission('delete admin') && $admin->id != $admin->company->superAdmin->id && $admin->id != auth('company')->id();
@endphp

<div class="actions">
    @if($canShow || $canEdit || $canDelete)

    @if($canShow)
    <a href="{{ route('company.admins.show', $admin->id) }}"><i class="ti ti-eye"></i></a>
    @endcan

    @if($canEdit)
    <a href="{{ route('company.admins.edit', $admin->id) }}"><i class="ti ti-edit"></i></a>
    @endcan

    @if($canDelete)
    <a href="javascript:void(0)">
        <i
            data-bs-toggle="modal"
            data-bs-target="#delete-modal"
            onclick="changeDeleteModalData(this)"
            delete-route="{{ route('company.admins.destroy', $admin->id) }}"
            delete-name="{{ __('Admin') }} : {{ $admin->name }}"
            class="ti ti-archive">
        </i>
    </a>
    @endcan
    @else
    -
    @endif
</div>