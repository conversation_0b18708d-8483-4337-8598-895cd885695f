<?php

namespace App\Services;

use App\Enum\ChatMessageTypeEnum;
use App\Enum\OrderStatus;
use App\Events\MessageSent;
use App\Models\Order;
use App\Models\OrderItem;
use App\Repositories\ChatRepository;
use App\Repositories\OrderItemRepository;
use App\Repositories\OrderRepository;
use App\Services\User\OrderService;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ChatService
{
    public function __construct(
        private readonly ChatRepository $chatRepository,
        private readonly MediaService $mediaService,
        private readonly OrderRepository $orderRepository,
        private readonly OrderItemRepository $orderItemRepository
    ) {}

    public function getChatMessages()
    {
        $chatable = $this->getChatable();

        $this->validate($chatable);

        $chat = $this->chatRepository->firstOrCreate($chatable);

        return $this->chatRepository->getMessages($chat);
    }

    public function sendMessage(): void
    {
        $chatable = $this->getChatable();

        $this->validate($chatable);

        $content = request('type') == ChatMessageTypeEnum::TEXT->value ? request('content') : null;
        $sent_by = auth('driver')->check() ? 'driver' : 'user';

        $chat = $this->chatRepository->firstOrCreate($chatable);

        DB::transaction(function () use ($chat, $sent_by, $content): void {
            $message = $this->chatRepository->createMessage($chat, [
                'sent_by' => $sent_by,
                'type' => request('type'),
                'content' => $content,
            ]);

            if (request('type') == ChatMessageTypeEnum::MEDIA->value) {
                $this->mediaService->save($message, request('content'), 'chat_messages');
            }

            event(new MessageSent($chat, $message));
        });
    }

    public function validate($chatable): void
    {
        if (! $chatable) {
            throw new NotFoundHttpException;
        }

        if ($chatable instanceof Order) {
            $this->orderValidation($chatable);
        }

        if ($chatable instanceof OrderItem) {
            $this->orderItemValidation($chatable);
        }

        $this->userValidation($chatable);
        $this->driverValidation($chatable);
    }

    public function userValidation($chatable): void
    {
        $user = auth('user')->user();

        // check if auth user belongs to this chat of order or order item
        if ($user && ! app(OrderService::class)->userCanActOnOrder($chatable, $user)) {
            throw new BadRequestHttpException('Chat not found');
        }
    }

    public function driverValidation($chatable): void
    {
        $driver = auth('driver')->user();

        // check if auth driver belongs to this chat of order or order item
        if ($driver && ! app(OrderService::class)->driverCanActOnOrder($chatable, $driver)) {
            throw new BadRequestHttpException('Chat not found');
        }
    }

    public function orderValidation(Order $order)
    {
        if (!$order->driver_id) {
            throw new BadRequestHttpException("Order doesn't have a driver");
        }

        if (in_array($order->status->value, [
            OrderStatus::CANCELLED->value,
            OrderStatus::DELIVERED->value,
            OrderStatus::DELIVERED_TO_LOCAL_HUB->value,
            OrderStatus::PARTIALLY_DELIVERED->value,
            OrderStatus::DELIVERY_FAILED->value
        ])) {
            throw new BadRequestHttpException('Order chat ended');
        }
    }

    public function orderItemValidation(OrderItem $orderItem) {}

    public function getChatable()
    {
        if (request('chatable_type') == 'order') {
            return $this->orderRepository->findById(request('chatable_id'));
        }

        return $this->orderItemRepository->findById(request('chatable_id'));
    }
}
