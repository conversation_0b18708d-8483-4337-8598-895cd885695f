<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_driver_actions', function (Blueprint $table) {
            $table->timestamp('accepted_at')->nullable()->after('status');
            $table->unsignedTinyInteger('resend_attempts')->default(0)->after('accepted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_driver_actions', function (Blueprint $table) {
            $table->dropColumn(['accepted_at', 'resend_attempts']);
        });
    }
};
