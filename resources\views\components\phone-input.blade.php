@props(['countries', 'country_code', 'phone', 'prefix' => ''])

<div class="d-flex align-items-center gap-2">
    <div style="min-width: 100px;">
        <select name="{{ $prefix }}country_code" id="{{ $prefix }}country_code" class="form-select select2" >
            @foreach ($countries as $country)
            <option value="{{ $country->code }}" @selected($country_code==$country->code) dir="ltr">
                {{ $country->code }}
            </option>
            @endforeach
        </select>
    </div>
    <div class="flex-grow-1">
        <input type="number" name="{{ $prefix }}phone" id="phone" class="form-control" value="{{ $phone }}" min="0" placeholder="{{ __('Phone number') }}">
    </div>
</div>