<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\DriversDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AssignDriverVehiclesRequest;
use App\Models\Driver;
use App\Models\Vehicle;
use App\Repositories\CompanyRepository;
use App\Repositories\TransportionMethodRepository;
use App\Repositories\VehicleRepository;
use App\Services\Admin\DriverService;

class DriverController extends Controller
{
    public function __construct(
        private readonly DriverService $driverService,
        private readonly CompanyRepository $companyRepository,
        private readonly TransportionMethodRepository $transportionMethodRepository,
        private readonly VehicleRepository $vehicleRepository
    ) {}

    public function index(DriversDataTable $dataTable)
    {
        $companies = $this->companyRepository->getApprovedCompaniesDdl();
        $transportionMethods = $this->transportionMethodRepository->getActiveMethods();

        return $dataTable->render('pages.admin.drivers.index', ['companies' => $companies, 'transportionMethods' => $transportionMethods]);
    }

    public function show(Driver $driver)
    {
        $driver = $this->driverService->getDriver($driver);

        return view('pages.admin.drivers.show', ['driver' => $driver]);
    }

    public function destroy(Driver $driver)
    {
        $this->driverService->delete($driver);

        return to_route('admin.drivers.index')->with('success', __('Deleted successfully'));
    }

    public function assignVehiclesView(Driver $driver)
    {
        $driver = $this->driverService->getDriver($driver);
        $availableVehicles = $this->vehicleRepository->getAvailableVehiclesForDriver($driver);
        $pendingVehicles = $this->driverService->getPendingDriverVehicles($driver);
        $approvedVehicles = $this->driverService->getApprovedDriverVehicles($driver);

        return view('pages.admin.drivers.assign-vehicles', ['driver' => $driver, 'availableVehicles' => $availableVehicles, 'pendingVehicles' => $pendingVehicles, 'approvedVehicles' => $approvedVehicles]);
    }

    public function assignVehicles(AssignDriverVehiclesRequest $request, Driver $driver)
    {
        $this->driverService->assignVehicles($driver, $request->validated('vehicles'));

        return redirect()->back()->with('success', __('Vehicles Assigned successfully'));
    }

    public function activation(Driver $driver, Vehicle $vehicle)
    {
        try {
            $result = $this->driverService->activation($driver, $vehicle);

            return response()->json(['success' => true] + $result);
        } catch (\Symfony\Component\HttpKernel\Exception\BadRequestHttpException $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 422);
        } catch (\Throwable) {
            return response()->json(['success' => false, 'message' => __('Something went wrong')], 500);
        }
    }
}
