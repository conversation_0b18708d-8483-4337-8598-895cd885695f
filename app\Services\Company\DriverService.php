<?php

namespace App\Services\Company;

use App\Models\Driver;
use App\Repositories\CompanyImmediateShippingCityRepository;
use App\Repositories\CompanyRepository;
use App\Repositories\CompanyShippingTypeRepository;
use App\Repositories\CompanyShippingTypeSizeTransportionMethodRepository;
use App\Repositories\DriverImmediateShippingCityAreaRepository;
use App\Repositories\DriverImmediateShippingCityRepository;
use App\Repositories\DriverRepository;
use App\Repositories\DriverShippingTypeRepository;
use App\Repositories\DriverVehicleRepository;
use App\Repositories\VehicleRepository;
use App\Services\MediaService;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class DriverService
{
    public function __construct(
        private readonly DriverRepository $driverRepository,
        private readonly MediaService $mediaService,
        private readonly CompanyRepository $companyRepository,
        private readonly DriverShippingTypeRepository $driverShippingTypeRepository,
        private readonly DriverImmediateShippingCityRepository $driverImmediateShippingCityRepository,
        private readonly DriverImmediateShippingCityAreaRepository $driverImmediateShippingCityAreaRepository,
        private readonly CompanyShippingTypeRepository $companyShippingTypeRepository,
        private readonly CompanyShippingTypeSizeTransportionMethodRepository $companyShippingTypeSizeTransportionMethodRepository,
        private readonly CompanyImmediateShippingCityRepository $companyImmediateShippingCityRepository,
        private readonly DriverVehicleRepository $driverVehicleRepository,
        private readonly VehicleRepository $vehicleRepository
    ) {}

    public function getDriver(Driver $driver): Driver
    {
        if ($driver->approval_status != 'approved' || $driver->company_id != auth('company')->user()->company_id) {
            abort(404);
        }

        return $driver;
    }

    public function create(array $data)
    {
        $this->validateCreation($data);

        $data['company_id'] = auth('company')->user()->company_id;
        $data['approval_status'] = 'approved';
        $data['phone'] = normalizePhoneNumber($data['phone']);

        return DB::transaction(function () use ($data) {

            $driver = $this->driverRepository->create($data);

            if (request('profile_image')) {
                $this->mediaService->save($driver, request('profile_image'), 'drivers', 'profile');
            }

            if (request('id_number_image')) {
                $this->mediaService->save($driver, request('id_number_image'), 'drivers', 'id_number');
            }

            if (request('driving_licence_image')) {
                $this->mediaService->save($driver, request('driving_licence_image'), 'drivers', 'driving_licence');
            }

            if (request('additional_attachments')) {
                foreach (request('additional_attachments') as $attachment) {
                    $this->mediaService->save($driver, $attachment, 'drivers', 'additional');
                }
            }

            $this->attachShippingOptions($driver, $data['shipping_options']);

            return $driver;
        });
    }

    public function validateCreation(array $data): void
    {
        $company = auth('company')->user()->company;

        foreach ($data['shipping_options'] as $shipping_option) {
            $companyShippingType = $this->companyShippingTypeRepository->get($company, $shipping_option['shipping_type_id']);

            // Check if company has this shipping type
            if (! $companyShippingType) {
                throw new BadRequestHttpException("company doesn't have shipping type id {$shipping_option['shipping_type_id']}");
            }

            // Check if company has these transportion methods
            foreach ($shipping_option['transportion_methods'] as $transportion_method) {
                $companyShippingTypeHasTransportionMethod = $this->companyShippingTypeSizeTransportionMethodRepository->shippingTypeHasTransportionMethod($companyShippingType, $transportion_method);

                if (! $companyShippingTypeHasTransportionMethod) {
                    throw new BadRequestHttpException("company shipping type id {$companyShippingType->shipping_type_id} doesn't have transportion method id $transportion_method");
                }
            }

            // Check if company has these cities
            foreach ($shipping_option['cities'] as $city) {

                // Immediate shipping
                if ($shipping_option['shipping_type_id'] == 1) {

                    $companyShippingCity = $this->companyImmediateShippingCityRepository->get($company, $city['id']);

                    if (! $companyShippingCity) {
                        throw new BadRequestHttpException("company doesn't have city id {$city['id']} for immediate shipping");
                    }

                    // check if city has area
                    foreach ($city['areas'] as $area_id) {

                        $cityHasArea = $this->companyImmediateShippingCityRepository->cityHasArea($companyShippingCity, $area_id);

                        if (! $cityHasArea) {
                            throw new BadRequestHttpException("company city id {$city['id']} doesn't have area id $area_id");
                        }
                    }
                }

                // Intercity shipping
                if ($shipping_option['shipping_type_id'] == 2) {

                    $companyHasPickupCity = $this->companyRepository->hasIntercityShippingPickupCity($company, $city['id']);

                    if (! $companyHasPickupCity) {
                        throw new BadRequestHttpException("company doesn't have city id {$city['id']} for intercity shipping");
                    }
                }
            }
        }
    }

    public function update(Driver $driver, array $data): void
    {
        if ($driver->approval_status != 'approved' || $driver->company_id != auth('company')->user()->company_id) {
            abort(404);
        }

        $this->validateCreation($data);

        $data['phone'] = normalizePhoneNumber($data['phone']);

        DB::transaction(function () use ($driver, $data): void {

            $this->driverRepository->update($driver, $data);

            if (request('profile_image')) {
                $this->mediaService->delete($driver->profileImage);
                $this->mediaService->save($driver, request('profile_image'), 'drivers', 'profile');
            }

            if (request('id_number_image')) {
                $this->mediaService->delete($driver->idImage);
                $this->mediaService->save($driver, request('id_number_image'), 'drivers', 'id_number');
            }

            if (request('driving_licence_image')) {
                $this->mediaService->delete($driver->drivingLiscenceImage);
                $this->mediaService->save($driver, request('driving_licence_image'), 'drivers', 'driving_licence');
            }

            if (request('additional_attachments')) {
                foreach (request('additional_attachments') as $attachment) {
                    $this->mediaService->save($driver, $attachment, 'drivers', 'additional');
                }
            }

            $driver->shippingTypes()->delete();
            $driver->immediateShippingCities()->delete();
            $driver->intercityShippingCities()->detach();
            $driver->internationalShippingCities()->detach();

            $this->attachShippingOptions($driver, $data['shipping_options']);
        });
    }

    private function attachShippingOptions(Driver $driver, array $shipping_options): void
    {
        foreach ($shipping_options as $shipping_option) {
            $driverShippingType = $this->driverShippingTypeRepository->create($driver, $shipping_option['shipping_type_id']);
            $driverShippingType->transportionMethods()->attach($shipping_option['transportion_methods']);

            // Immediate shipping
            if ($shipping_option['shipping_type_id'] == 1) {
                foreach ($shipping_option['cities'] as $city) {
                    $driverImmediateShippingCity = $this->driverImmediateShippingCityRepository->create($driver, $city['id']);

                    foreach ($city['areas'] as $area_id) {
                        $this->driverImmediateShippingCityAreaRepository->create($driverImmediateShippingCity, $area_id);
                    }
                }
            }

            // Intercity shipping
            if ($shipping_option['shipping_type_id'] == 2) {
                $cityIds = array_column($shipping_option['cities'], 'id');
                $driver->intercityShippingCities()->attach($cityIds, ['shipping_type' => 'intercity']);
            }

            // International shipping
            if ($shipping_option['shipping_type_id'] == 3) {
                $cityIds = array_column($shipping_option['cities'], 'id');
                $driver->internationalShippingCities()->attach($cityIds, ['shipping_type' => 'international']);
            }
        }
    }

    public function delete(Driver $driver): void
    {
        if ($driver->approval_status != 'approved' || $driver->company_id != auth('company')->user()->company_id) {
            abort(404);
        }

        $hasActiveOrder = $this->driverRepository->hasActiveOrder($driver);

        if ($hasActiveOrder) {
            throw new BadRequestHttpException('driver has an active order');
        }

        $driver->delete();

        $driver->vehicles()->delete();
    }

    public function getApprovedDriverVehicles(Driver $driver)
    {
        return $this->driverVehicleRepository->getApprovedDriverVehicles($driver)->map(fn($driverVehicle) => $driverVehicle->vehicle);
    }

    public function getPendingDriverVehicles(Driver $driver)
    {
        return $this->driverVehicleRepository->getPendingDriverVehicles($driver)->map(fn($driverVehicle) => $driverVehicle->vehicle);
    }

    public function assignVehicles(Driver $driver, array $data): void
    {
        if ($driver->approval_status != 'approved' || $driver->company_id != auth('company')->user()->company_id) {
            abort(404);
        }

        DB::transaction(function () use ($driver, $data): void {
            $alreadyAssignedVehicles = $this->driverVehicleRepository->getApprovedDriverVehicles($driver)->pluck('vehicle_id')->toArray();

            // assign new vehicles
            foreach ($data['vehicles'] as $vehicle_id) {

                $vehicle = $this->vehicleRepository->getById($vehicle_id);

                if ($vehicle->company_id != $driver->company_id) {
                    throw new BadRequestHttpException('Vehicle not found');
                }

                if (! in_array($vehicle_id, $alreadyAssignedVehicles)) {
                    $this->driverVehicleRepository->create($driver->id, $vehicle_id);
                }
            }

            // delete removed vehicles
            foreach ($alreadyAssignedVehicles as $assigned_vehicle_id) {

                // if vehicle is not removed, go to next one
                if (in_array($assigned_vehicle_id, $data['vehicles'])) {
                    continue;
                }

                $driverVehicle = $this->driverVehicleRepository->get($driver->id, $assigned_vehicle_id);

                // prevent remove the active vehicle
                if ($driverVehicle->is_active) {
                    throw new BadRequestHttpException('You can not remove active vehicle');
                }

                $this->driverVehicleRepository->delete($driver->id, $assigned_vehicle_id);
            }
        });
    }
}
