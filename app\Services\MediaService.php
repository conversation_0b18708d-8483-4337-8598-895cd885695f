<?php

namespace App\Services;

use App\Models\Media;
use App\Repositories\MediaRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class MediaService
{
    public function __construct(private readonly MediaRepository $mediaRepository) {}

    public function save(Model $record, UploadedFile $file, string $directory, ?string $type = null)
    {
        $path = $file->store($directory);

        $fullPath = storage_path("app/public/$path");

        $mimeType = $file->getClientMimeType();

        if (str_starts_with($mimeType, 'image/')) {
            $this->compressImage($fullPath);
        }

        return $this->mediaRepository->save($record, $file, $path, $type);
    }

    public function getMaxUploadSize()
    {
        $maxUploadSize = ini_get('upload_max_filesize'); // in megabytes

        return preg_replace('/\D/', '', $maxUploadSize) * 1024; // get in kilobytes
    }

    public function compressImage(string $fullPath): void
    {
        $supported_formats = \Imagick::queryFormats();
        $ext = strtoupper(pathinfo($fullPath, PATHINFO_EXTENSION));

        if (in_array($ext, $supported_formats)) {

            try {
                $image = new \Imagick($fullPath);

                $image->setImageCompressionQuality(20);

                file_put_contents($fullPath, $image);
            } catch (\Exception) {
            }
        }
    }

    public function delete(?Media $media): void
    {
        if (is_null($media)) {
            return;
        }

        Storage::disk('public')->delete($media->path);
        $media->delete();
    }

    public function deleteAllMedia(Model $model): void
    {
        $model->media()->delete();

        foreach ($model->media as $media) {
            $this->delete($media);
        }
    }
}
