<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\ConfirmOTPRequest;
use App\Http\Requests\User\RequestOtpRequest;
use App\Http\Resources\Driver\LoginResource;
use App\Models\Driver;
use App\Repositories\DriverRepository;
use App\Repositories\OTPRepository;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\RateLimiter;

class AuthenticationController extends Controller
{
    public function __construct(private readonly DriverRepository $driverRepository, private readonly OTPRepository $otpRepository) {}

    public function requestOTP(RequestOtpRequest $request)
    {
        $phone = normalizeMobileNumber($request->phone);

        if (App::environment('production')) {
            if (RateLimiter::tooManyAttempts("otp-request-driver:$request->country_code-$phone", 3)) {

                $seconds = RateLimiter::availableIn("otp-request-driver:$request->country_code-$phone");
                $remainingTime = formatSecondsToHoursTime($seconds);

                return errorMessage(__('limit reached: retry after :time hours', ['time' => $remainingTime]));
            }
        }

        RateLimiter::hit("otp-request-driver:$request->country_code-$phone", 60 * 60 * 12);

        $code = generatePassCode();

        $this->otpRepository->create(Driver::class, $request->country_code, $phone, $code);

        return success(__('code has been sent successfully to your phone'));
    }

    public function allowedToSendOTP(RequestOtpRequest $request)
    {
        $phone = normalizeMobileNumber($request->phone);

        if (RateLimiter::tooManyAttempts("otp-request-driver:$request->country_code-$phone", 3)) {
            return success(['allowed' => false]);
        }

        return success(['allowed' => true]);
    }

    public function confirmCode(ConfirmOTPRequest $request)
    {
        $phone = normalizeMobileNumber($request->phone);

        if (RateLimiter::tooManyAttempts("confirm-otp-driver:$request->country_code-$phone", 3)) {
            $seconds = RateLimiter::availableIn("confirm-otp-driver:$request->country_code-$phone");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            return errorMessage(__('too many attempts: retry after :time minutes', ['time' => $remainingTime]));
        }

        RateLimiter::hit("confirm-otp-driver:$request->country_code-$phone", 60);

        $otp = $this->otpRepository->get(Driver::class, $request->country_code, $phone, $request->code);

        if (! $otp) {
            return errorMessage(__('invalid code'));
        }

        $latest_otp = $this->otpRepository->getLatest(Driver::class, $request->country_code, $phone);

        if ($latest_otp->code != $request->code) {
            return errorMessage(__('please enter the latest sent code'));
        }

        // expire code after 1 hour
        if (Carbon::parse($latest_otp->created_at)->addHour()->lt(now())) {
            return errorMessage(__('expired code'));
        }

        $driver = $this->driverRepository->getByPhone($request->country_code, $phone);

        // if driver not found, it should be redirect to complete profile screen
        if (! $driver) {
            return success(__('code confirmed successfully. please complete your profile'));
        }

        if ($driver->approval_status != 'approved') {
            return errorMessage(__('your account is not approved'));
        }

        if ($driver->status != 'active') {
            return errorMessage(__('your account is disabled'));
        }

        $token = DB::transaction(function () use ($driver, $request, $phone) {
            // delete all driver tokens
            $driver->tokens()->delete();

            // create new token
            $token = $driver->createToken('token')->plainTextToken;

            // delete all phone otp
            $this->otpRepository->deleteAll(Driver::class, $request->country_code, $phone);

            return $token;
        });

        return success([
            'token' => $token,
            'driver' => new LoginResource($driver),
        ]);
    }

    public function logout(Request $request)
    {
        auth('driver')->user()->currentAccessToken()->delete();

        return success(__('logged out successfully'));
    }
}
