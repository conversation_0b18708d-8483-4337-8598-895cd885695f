@php
    $canShow = auth('admin')->user()->hasPermission('show user');
    $canEdit = auth('admin')->user()->hasPermission('update user');
    $canDelete = auth('admin')->user()->hasPermission('delete user');
@endphp

<div class="actions">
    @if ($canShow || $canEdit || $canDelete)
        @if ($canShow)
            <a href="{{ route('admin.users.show', $id) }}"><i class="ti ti-eye"></i></a>
        @endif

        @if ($canEdit)
            <a href="{{ route('admin.users.edit', $id) }}"><i class="ti ti-edit"></i></a>
        @endif

        @if ($canDelete)
            <a href="javascript:void(0)">
                <i data-bs-toggle="modal" data-bs-target="#delete-modal" onclick="changeDeleteModalData(this)"
                    delete-route="{{ route('admin.users.destroy', $id) }}"
                    delete-name="{{ __('User') }} : {{ $name }}" class="ti ti-archive">
                </i>
            </a>
        @endif
    @else
        -
    @endif
</div>
