@php
    $canShow = auth('company')->user()->hasPermission('show vehicle');
    $canEdit = auth('company')->user()->hasPermission('update vehicle') && $vehicle->approval_status != 'rejected';
    $canDelete = auth('company')->user()->hasPermission('delete vehicle');
@endphp

<div class="actions">
    @if($canShow || $canEdit || $canDelete)

        @if($canShow)
            <a href="{{ route('company.vehicles.show', $vehicle->id) }}">
                <i class="ti ti-eye"></i>
            </a>
        @endif

        @if($canEdit)
            <a href="{{ route('company.vehicles.edit', $vehicle->id) }}">
                <i class="ti ti-edit"></i>
            </a>
        @endif

        @if($canDelete)
            <a href="javascript:void(0)">
                <i
                    data-bs-toggle="modal"
                    data-bs-target="#delete-modal"
                    onclick="changeDeleteModalData(this)"
                    delete-route="{{ route('company.vehicles.destroy', $vehicle->id) }}"
                    delete-name="{{ __('Vehicle') }} : {{ $vehicle->plate_number }}"
                    class="ti ti-archive">
                </i>
            </a>
        @endif

    @else
        -
    @endif
</div>
