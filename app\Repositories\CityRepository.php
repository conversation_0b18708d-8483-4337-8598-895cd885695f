<?php

namespace App\Repositories;

use App\DTO\Admin\CityDTO;
use App\Models\City;
use MatanYadaev\EloquentSpatial\Objects\Point;

class CityRepository
{
    public function __construct(private readonly City $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByIds(array $ids)
    {
        return $this->model->whereIn('id', $ids)->get();
    }

    public function getActiveCities(?bool $with_areas)
    {
        return $this->model
            ->where('status', 'active')
            ->when($with_areas, function ($query): void {
                $query->with(['areas' => function ($q): void {
                    $q->where('status', 'active');
                }]);
            })
            ->when(!request('country_id'), fn($q) => $q->where('country_id', 1))
            ->when(request('country_id'), fn($q) => $q->where('country_id', request('country_id')))
            ->get();
    }

    public function getByLocation(float $lat, float $lng)
    {
        // Create a Point object from the given coordinates
        // Note: Point constructor takes (latitude, longitude) in that order
        $point = new Point($lat, $lng);

        // First try using the whereContains scope from HasSpatial trait
        $city = $this->model
            ->where('status', 'active')
            ->whereContains('area', $point)
            ->first();

        if ($city) {
            return $city;
        }

        // If that doesn't work, try the raw SQL approach
        // Note: In MySQL, the POINT function takes longitude first, then latitude
        try {
            $city = $this->model
                ->where('status', 'active')
                ->whereRaw('ST_Contains(area, ST_GeomFromText(?))', ["POINT($lng $lat)"])
                ->first();

            if ($city) {
                return $city;
            }
        } catch (\Exception) {
            // If raw SQL fails, continue to return null
        }

        return null;
    }

    public function create(CityDTO $dto)
    {
        return $this->model->create($dto->toArray());
    }

    public function update(City $city, CityDTO $dto)
    {
        return $city->update($dto->toArray());
    }

    public function getForSelect()
    {
        return $this->model->active()->get();
    }
}
