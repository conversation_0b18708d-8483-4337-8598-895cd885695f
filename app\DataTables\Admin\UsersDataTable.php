<?php

namespace App\DataTables\Admin;

use App\Models\User;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class UsersDataTable extends DataTable
{
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->setRowId('id')
            ->addColumn('status', fn ($user): ?string => statusBadge($user->status))
            ->addColumn('actions', 'pages.admin.users.actions')
            ->addColumn('phone', fn ($user): string => formatPhone($user->country_code, $user->phone))
            ->addColumn('email', fn ($user) => $user->email ?? '-')
            ->addColumn('created_at', fn ($user): ?string => formatDateTime($user->created_at))
            ->orderColumn('DT_RowIndex', function ($query, $direction): void {
                $query->orderBy('id', $direction);
            })
            ->rawColumns(['actions', 'phone', 'status'])
            ->addIndexColumn();
    }

    public function query(User $model): QueryBuilder
    {
        return $model->newQuery()
            ->when(request('search_param'), function ($query): void {
                $query->where('name', 'LIKE', '%'.request('search_param').'%')
                    ->orWhereRaw('CONCAT(country_code, phone) LIKE ?', ['%'.request('search_param').'%']);
            })
            ->when(request('status'), fn ($q) => $q->where('status', request('status')));
    }

    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('users-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->searching(false)
            ->parameters([
                'stateSave' => true,
                'stateDuration' => 10,
                'language' => [
                    'url' => asset('assets/json/datatable-translations/'.app()->getLocale().'.json'),
                ],
            ]);
    }

    public function getColumns(): array
    {
        return [
            Column::make('DT_RowIndex')->title('#')->addClass('text-center')->searchable(false),
            Column::make('name')->title(__('Name'))->addClass('text-center')->orderable(false),
            Column::computed('email')->title(__('Email'))->addClass('text-center'),
            Column::computed('phone')->title(__('Phone'))->addClass('text-center'),
            Column::computed('status')->title(__('Status'))->addClass('text-center'),
            Column::computed('created_at')->title(__('Created At'))->addClass('text-center'),
            Column::computed('actions')->title(__('Actions'))->addClass('text-center'),

        ];
    }

    protected function filename(): string
    {
        return 'Users_'.date('YmdHis');
    }
}
