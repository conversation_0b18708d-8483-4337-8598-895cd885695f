<?php

namespace App\Http\Requests\Company;

use App\Http\Requests\BaseFormRequest;
use App\Models\Driver;
use App\Rules\ActiveAreas;
use App\Rules\ActiveImmeditateShippingCities;
use App\Rules\ActiveTransportionMethods;
use App\Rules\AreasRequiredWithImmediateShipping;
use App\Rules\DistinctArrayValues;
use App\Rules\UniquePhone;
use App\Rules\UniqueShippingOptionCities;
use App\Rules\UniqueShippingOptionCityAreas;
use App\Rules\ValidBirthDate;
use App\Rules\ValidMedia;
use App\Rules\ValidPassword;
use App\Rules\ValidPhone;
use Illuminate\Validation\Rule;

class DriverRequest extends BaseFormRequest
{
    public function rules(): array
    {
        $driver = $this->route('driver');

        return [
            'country_code' => ['required', 'string', 'exists:countries,code'],
            'phone' => ['required', 'string', new ValidPhone($this->country_code), new UniquePhone($this->country_code, Driver::class, $driver?->id)],
            'name' => ['required', 'string', 'min:4', 'max:30'],
            'email' => ['nullable', 'string', 'email:rfc,dns', 'max:255', Rule::unique('drivers', 'email')->ignore($driver?->id)],
            'gender' => ['nullable', 'in:male,female'],
            'birth_date' => ['nullable', new ValidBirthDate],
            'profile_image' => ['nullable', new ValidMedia(['image'])],
            'id_number' => ['nullable', 'string', 'min:10', 'max:15', Rule::unique('drivers', 'id_number')->ignore($driver?->id)],
            'id_number_image' => ['nullable', new ValidMedia(['image', 'pdf'])],
            'driving_licence_image' => [Rule::requiredIf(! (bool) $driver), new ValidMedia(['image', 'pdf'])],
            'additional_attachments' => ['nullable', 'array'],
            'additional_attachments.*' => ['nullable', 'file', new ValidMedia(['image', 'pdf'])],
            'bank_name' => ['required', 'string', 'min:5', 'max:100'],
            'bank_account_owner' => ['required', 'string', 'min:5', 'max:100'],
            'bank_account_number' => ['required', 'digits_between:10,20'],
            'iban' => ['nullable', 'string', 'regex:/^[A-Z]{2}\d{2}[A-Z0-9]{11,30}$/i'],
            'shipping_options' => ['required', 'array'],
            'shipping_options.*.shipping_type_id' => ['required', 'exists:shipping_types,id', 'distinct'],
            'shipping_options.*.transportion_methods' => ['required', 'array', new DistinctArrayValues, new ActiveTransportionMethods],
            'shipping_options.*.transportion_methods.*' => ['required', 'exists:transportion_methods,id'],
            'shipping_options.*.cities' => ['required', 'array', new UniqueShippingOptionCities, new ActiveImmeditateShippingCities],
            'shipping_options.*.cities.*.id' => ['required', 'exists:cities,id', new AreasRequiredWithImmediateShipping($this->input('shipping_options'))],
            'shipping_options.*.cities.*.areas' => ['nullable', 'array', new UniqueShippingOptionCityAreas, new ActiveAreas],
            'shipping_options.*.cities.*.areas.*' => ['required', 'exists:areas,id'],
        ];
    }

    public function messages()
    {
        return [
            'shipping_options.*.transportion_methods.required' => __('Please select at least one transportion method'),
            'shipping_options.*.cities.required' => __('Please select at least one city'),
            'shipping_options.*.cities.*.areas.required' => __('Please select at least one area'),
            'shipping_options.*.cities.*.areas.*.required_if' => __('Please select at least one area'),
        ];
    }

    protected function prepareForValidation()
    {
        if ($this->has('shipping_options')) {
            $filtered = collect($this->input('shipping_options'))
                ->filter(fn ($option): bool => ! empty($option['enabled']))
                ->map(function ($option) {
                    // Filter out cities with null or empty 'id'
                    if (isset($option['cities']) && is_array($option['cities'])) {
                        $option['cities'] = collect($option['cities'])
                            ->filter(fn ($city): bool => ! empty($city['id']))
                            ->values() // Reindex after filtering
                            ->all();
                    }

                    return $option;
                })
                ->values() // Re-index the main array
                ->all();

            $this->merge([
                'shipping_options' => $filtered,
            ]);
        }
    }
}
