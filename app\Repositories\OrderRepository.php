<?php

namespace App\Repositories;

use App\Enum\OrderStatus;
use App\Models\Order;
use App\Models\User;

class OrderRepository
{
    public function __construct(private readonly Order $model) {}

    public function getPaginatedUserOrders(User $user)
    {
        return $this->model
            ->where('user_id', $user->id)
            ->with(['shippingType', 'shippingSize', 'transportionMethod'])
            ->withCount('items')
            ->when(request('search'), fn ($query, $search) => $query->where('order_number', 'like', "%{$search}%"))
            ->when(request('shipping_type_id'), fn ($query) => $query->where('shipping_type_id', request('shipping_type_id')))
            ->when(request()->has('is_express'), fn ($query) => $query->where('is_express', request('is_express')))
            ->when(request('from'), fn ($query) => $query->whereDate('created_at', '>=', request('from')))
            ->when(request('to'), fn ($query) => $query->whereDate('created_at', '<=', request('to')))
            ->when(request('statuses'), fn ($query) => $query->whereIn('status', request('statuses')))
            ->when(request('state'), function ($query) {
                $state = request('state');

                if ($state == 'current') {
                    $query->whereNotIn('status', [
                        OrderStatus::READY_TO_PICKUP->value,
                        OrderStatus::DELIVERED->value,
                        OrderStatus::PARTIALLY_DELIVERED->value,
                        OrderStatus::CANCELLED->value,
                        OrderStatus::DELIVERY_FAILED->value,
                    ]);
                }

                if ($state == 'complete') {
                    $query->where('status', OrderStatus::DELIVERED->value);
                }

                if ($state == 'incomplete') {
                    $query->where('status', OrderStatus::PARTIALLY_DELIVERED->value);
                }

                if ($state == 'cancelled') {
                    $query->whereIn('status', [
                        OrderStatus::CANCELLED->value,
                        OrderStatus::DELIVERY_FAILED->value,
                    ]);
                }
            })
            ->latest()
            ->paginate();
    }

    public function getDriverOrders(array $filters = [], int $limit = 10)
    {
        return $this->model->with(['user', 'driver', 'shippingType', 'shippingSize', 'pickupAddress', 'items', 'items.dropoffAddress'])
            ->forDriver()
            ->when($filters['search'] ?? null, fn ($q) => $q->where('order_number', 'like', "%{$filters['search']}%"))
            ->when($filters['shipping_type_id'] ?? null, fn ($q) => $q->where('shipping_type_id', $filters['shipping_type_id']))
            ->when(isset($filters['is_express']) ?? null, fn ($q) => $q->where('is_express', $filters['is_express']))
            ->when($filters['date_from'] ?? null, fn ($q) => $q->whereDate('created_at', '>=', $filters['date_from']))
            ->when($filters['date_to'] ?? null, fn ($q) => $q->whereDate('created_at', '<=', $filters['date_to']))
            ->when(isset($filters['status']), fn ($q) => $q->where('status', $filters['status']))
            ->when(isset($filters['type']), function ($q) use ($filters) {
                $statusMap = [
                    'current' => [
                        OrderStatus::READY_TO_PICKUP,
                        OrderStatus::HEADING_TO_PICKUP,
                        OrderStatus::ARRIVED_AT_PICKUP,
                        OrderStatus::PICKED_UP,
                        OrderStatus::IN_TRANSIT,
                        OrderStatus::ARRIVED_AT_DELIVERY_LOCATION,
                    ],
                    'completed' => [
                        OrderStatus::DELIVERED,
                        OrderStatus::DELIVERED_TO_LOCAL_HUB,
                    ],
                    'incomplete' => [
                        OrderStatus::PARTIALLY_DELIVERED,
                    ],
                    'cancelled_or_failed' => [
                        OrderStatus::CANCELLED,
                        OrderStatus::DELIVERY_FAILED,
                    ],
                ];

                if (isset($statusMap[$filters['type']])) {
                    $q->whereIn('status', array_map(fn ($status) => $status->value, $statusMap[$filters['type']]));
                }
            })->latest()->paginate($limit);
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function findById(string $id)
    {
        return $this->model->find($id);
    }

    public function findOrderForDriver(string $id)
    {
        return $this->model
            ->with(['user', 'driver', 'shippingType', 'shippingSize', 'pickupAddress', 'items', 'items.dropoffAddress', 'items.media'])
            ->forDriver()
            ->find($id);
    }

    public function update(Order $order, array $data)
    {
        return $order->update($data);
    }
}
