<?php

namespace App\Events;

use App\Http\Resources\User\OrderResource;
use App\Models\Order;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NewOrder implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(private \App\Models\Order $order) {}

    public function broadcastOn()
    {
        $channels = [];

        $drivers = $this->order->driverActions()->get();
        foreach ($drivers as $driver) {
            $channels[] = new PrivateChannel("order.{$driver->driver_id}");
        }

        return $channels;
    }

    public function broadcastWith()
    {
        return OrderResource::make($this->order->load(['items', 'pickupAddress', 'items.dropoffAddress', 'items.media']))->resolve();
    }

    public function broadcastAs(): string
    {
        return 'NewOrder';
    }
}
