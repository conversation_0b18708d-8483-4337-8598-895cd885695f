<?php

namespace App\Events;

use App\Http\Resources\User\DriverResource;
use App\Models\Order;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderAcceptedDriversUpdated implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(public Order $order, public $acceptedDrivers) {}

    public function broadcastWith(): array
    {
        return [
            'order_id' => $this->order->id,
            'accepted_drivers' => DriverResource::collection($this->acceptedDrivers),
        ];
    }

    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("order.drivers.list.{$this->order->id}"),
        ];
    }

    public function broadcastAs(): string
    {
        return 'OrderAcceptedDriversUpdated';
    }
}
