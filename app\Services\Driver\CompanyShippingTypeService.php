<?php

namespace App\Services\Driver;

use App\Repositories\CompanyRepository;
use App\Repositories\CompanyShippingTypeRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class CompanyShippingTypeService
{
    public function __construct(
        private readonly CompanyRepository $companyRepository,
        private readonly CompanyShippingTypeRepository $companyShippingTypeRepository,
    ) {}

    public function getCompanyShippingTypesForDriver(string $company_code)
    {
        $company = $this->companyRepository->getByCode($company_code);

        if (! $company) {
            throw new NotFoundHttpException;
        }

        return $this->companyShippingTypeRepository->getCompanyShippingTypes($company);
    }
}
