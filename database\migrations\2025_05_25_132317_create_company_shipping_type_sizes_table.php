<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('company_shipping_type_sizes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_shipping_type_id')->constrained()->cascadeOnDelete();
            $table->foreignId('shipping_size_id')->constrained()->cascadeOnDelete();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('company_shipping_type_sizes');
    }
};
