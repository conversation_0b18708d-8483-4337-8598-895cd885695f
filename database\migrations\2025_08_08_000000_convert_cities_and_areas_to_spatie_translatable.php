<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add JSON name column to cities table
        Schema::table('cities', function (Blueprint $table) {
            $table->json('name')->nullable()->after('country_id');
        });

        // Migrate data from city_translations to cities
        $cities = DB::table('cities')->get();
        foreach ($cities as $city) {
            $translations = DB::table('city_translations')
                ->where('city_id', $city->id)
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->locale => $item->name];
                })
                ->toArray();

            DB::table('cities')
                ->where('id', $city->id)
                ->update(['name' => json_encode($translations)]);
        }

        // Add JSON name column to areas table
        Schema::table('areas', function (Blueprint $table) {
            $table->json('name')->nullable()->after('city_id');
        });

        // Migrate data from area_translations to areas
        $areas = DB::table('areas')->get();
        foreach ($areas as $area) {
            $translations = DB::table('area_translations')
                ->where('area_id', $area->id)
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->locale => $item->name];
                })
                ->toArray();

            DB::table('areas')
                ->where('id', $area->id)
                ->update(['name' => json_encode($translations)]);
        }

        // Drop translation tables
        Schema::dropIfExists('city_translations');
        Schema::dropIfExists('area_translations');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate translation tables
        Schema::create('city_translations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('city_id')->constrained()->cascadeOnDelete();
            $table->string('locale')->index();
            $table->unique(['city_id', 'locale']);
        });

        Schema::create('area_translations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('area_id')->constrained()->cascadeOnDelete();
            $table->string('locale')->index();
            $table->unique(['area_id', 'locale']);
        });

        // Migrate data back from JSON to translation tables
        $cities = DB::table('cities')->get();
        foreach ($cities as $city) {
            if ($city->name) {
                $translations = json_decode($city->name, true);
                foreach ($translations as $locale => $name) {
                    DB::table('city_translations')->insert([
                        'city_id' => $city->id,
                        'locale' => $locale,
                        'name' => $name,
                    ]);
                }
            }
        }

        $areas = DB::table('areas')->get();
        foreach ($areas as $area) {
            if ($area->name) {
                $translations = json_decode($area->name, true);
                foreach ($translations as $locale => $name) {
                    DB::table('area_translations')->insert([
                        'area_id' => $area->id,
                        'locale' => $locale,
                        'name' => $name,
                    ]);
                }
            }
        }

        // Drop JSON columns
        Schema::table('cities', function (Blueprint $table) {
            $table->dropColumn('name');
        });

        Schema::table('areas', function (Blueprint $table) {
            $table->dropColumn('name');
        });
    }
};
