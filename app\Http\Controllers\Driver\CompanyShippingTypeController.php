<?php

namespace App\Http\Controllers\Driver;

use App\Http\Controllers\Controller;
use App\Http\Resources\CompanyShippingTypeResource;
use App\Services\Driver\CompanyShippingTypeService;

class CompanyShippingTypeController extends Controller
{
    public function __construct(private readonly CompanyShippingTypeService $companyShippingTypeService) {}

    public function index(string $company_code)
    {
        $shippingTypes = $this->companyShippingTypeService->getCompanyShippingTypesForDriver($company_code);

        return CompanyShippingTypeResource::collection($shippingTypes);
    }
}
