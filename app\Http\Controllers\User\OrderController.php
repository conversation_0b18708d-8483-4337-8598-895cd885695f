<?php

namespace App\Http\Controllers\User;

use App\DTO\User\OrderDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\User\Order\AcceptDriverRequest;
use App\Http\Requests\Api\User\Order\CompleteOrderRequest;
use App\Http\Requests\User\Order\RateDriverRequest;
use App\Http\Requests\User\Order\StoreRequest;
use App\Http\Resources\BaseCollection;
use App\Http\Resources\User\DriverResource;
use App\Http\Resources\User\OrderResource;
use App\Models\Order;
use App\Repositories\OrderRepository;
use App\Services\User\OrderService;

class OrderController extends Controller
{
    public function __construct(
        private readonly OrderService $orderService,
        private readonly OrderRepository $orderRepository
    ) {}

    public function index()
    {
        $orders = $this->orderRepository->getPaginatedUserOrders(auth('user')->user());

        return success((new BaseCollection($orders, OrderResource::class)));
    }

    public function store(StoreRequest $request)
    {
        $dto = OrderDTO::from($request->validated());
        $order = $this->orderService->create($dto, $request->validated('items'));

        return success(new OrderResource($order));
    }

    public function show(string $id)
    {
        return success(new OrderResource($this->orderService->show($id)));
    }

    public function assignDrivers(string $orderId)
    {
        return success($this->orderService->assignDriversToOrder($orderId));
    }

    public function acceptDriver(AcceptDriverRequest $request, string $orderId)
    {
        return success($this->orderService->acceptDriver($orderId, $request->validated('driver_id')));
    }

    public function complete(CompleteOrderRequest $request, string $orderId)
    {
        return success($this->orderService->complete($orderId, $request->validated()));
    }

    public function cancel(string $orderId)
    {
        return success($this->orderService->cancel($orderId));
    }

    public function rateDriver(RateDriverRequest $request, string $orderId)
    {
        return success($this->orderService->rateDriver($orderId));
    }

    public function pdf(string $orderId)
    {
        $pdf = $this->orderService->getPdf($orderId);

        return success(['url' => $pdf]);
    }

    public function drivers(Order $order)
    {
        $drivers = $this->orderService->getAcceptedDriverActions($order);

        return success(DriverResource::collection($drivers));
    }
}
