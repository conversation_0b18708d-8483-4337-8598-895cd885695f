<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\AreasDataTable;
use App\DTO\Admin\AreaDTO;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Area\StoreRequest;
use App\Models\Area;
use App\Repositories\CityRepository;
use App\Services\Admin\AreaService;

class AreaController extends Controller
{
    public function __construct(
        private readonly AreaService $areaService,
        private readonly CityRepository $cityRepository
    ) {
        //
    }

    /**
     * Display a listing of the resource.
     */
    public function index(AreasDataTable $dataTable)
    {
        return $dataTable->render('pages.admin.areas.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $cities = $this->cityRepository->getForSelect();

        return view('pages.admin.areas.create', ['cities' => $cities]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRequest $request)
    {
        $dto = AreaDTO::from($request->validated());
        $this->areaService->create($dto);

        return to_route('admin.areas.index')->with('success', __('Area created successfully'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Area $area)
    {
        $area->load('city');

        return view('pages.admin.areas.show', ['area' => $area]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Area $area)
    {
        $cities = $this->cityRepository->getForSelect();

        return view('pages.admin.areas.edit', ['area' => $area, 'cities' => $cities]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(StoreRequest $request, Area $area)
    {
        $dto = AreaDTO::from($request->validated());
        $this->areaService->update($area, $dto);

        return to_route('admin.areas.index')->with('success', __('Area updated successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Area $area): \Illuminate\Http\RedirectResponse
    {
        return $this->areaService->delete($area);
    }
}
