<?php

namespace App\Http\Controllers\Company;

use App\DataTables\Company\AdminsDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Company\AdminRequest;
use App\Models\CompanyAdmin;
use App\Repositories\CountryRepository;
use App\Repositories\PermissionRepository;
use App\Repositories\RoleRepository;
use App\Services\Company\AdminService;

class AdminController extends Controller
{
    public function __construct(
        private readonly AdminService $adminService,
        private readonly CountryRepository $countryRepository,
        private readonly RoleRepository $roleRepository,
        private readonly PermissionRepository $permissionRepository
    ) {}

    public function index(AdminsDataTable $dataTable)
    {
        return $dataTable->render('pages.company.admins.index');
    }

    public function create()
    {
        $countries = $this->countryRepository->getAll();
        $roles = $this->roleRepository->getActiveRoles('company')->load('permissions');
        $permissions = $this->permissionRepository->getAllGrouped('company');

        return view('pages.company.admins.create', ['countries' => $countries, 'roles' => $roles, 'permissions' => $permissions]);
    }

    public function store(AdminRequest $request)
    {
        $this->adminService->create();

        return to_route('company.admins.index')->with('success', __('Created successfully'));
    }

    public function show(CompanyAdmin $admin)
    {
        $admin = $this->adminService->getAdmin($admin);

        $permissions = $this->permissionRepository->getAllGrouped('company');

        return view('pages.company.admins.show', ['admin' => $admin, 'permissions' => $permissions]);
    }

    public function edit(CompanyAdmin $admin)
    {
        $admin = $this->adminService->getAdmin($admin);
        $countries = $this->countryRepository->getAll();
        $roles = $this->roleRepository->getActiveRoles('company')->load('permissions');
        $permissions = $this->permissionRepository->getAllGrouped('company');

        return view('pages.company.admins.edit', ['admin' => $admin, 'countries' => $countries, 'roles' => $roles, 'permissions' => $permissions]);
    }

    public function update(AdminRequest $request, CompanyAdmin $admin)
    {
        $this->adminService->update($admin);

        return back()->with('success', __('Updated successfully'));
    }

    public function destroy(CompanyAdmin $admin)
    {
        $this->adminService->delete($admin);

        return to_route('company.admins.index')->with('success', __('Deleted successfully'));
    }
}
