<x-layout :title="__('Driver Registration Requests')">
    <x-session-message />
    <div class="card">
        <div class="card-heading">
            <p>{{ __('Driver Registration Requests') }}</p>
        </div>
        <div class="row" id="filters" data-datatable-id="driver-registration-requests-table">
            <!-- Search -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Search') }}</b></label>
                <input type="text" name="search_param" class="form-control" onchange="dt_filter()">
            </div>

            <!-- Status -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Status') }}</b></label>
                <select name="approval_status" class="select2 form-select form-select-lg" data-placeholder="{{ __('Choose') }}..." data-allow-clear="true" onchange="dt_filter()">
                    <option></option>
                    <option value="pending">{{ __('Pending') }}</option>
                    <option value="on_review">{{ __('On Review') }}</option>
                </select>
            </div>
        </div>
        <div class="card-body">
            <div class="card-datatable table-responsive pt-0">
                {{ $dataTable->table(['class' => 'datatables-basic table table-striped']) }}
            </div>
            <x-delete-modal />
        </div>
    </div>

    @push('css')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}" />
    @endpush

    @push('js')
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    {{ $dataTable->scripts(attributes: ['type' => 'module']) }}
    @endpush
</x-layout>
