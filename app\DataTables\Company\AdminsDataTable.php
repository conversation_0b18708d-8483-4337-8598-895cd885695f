<?php

namespace App\DataTables\Company;

use App\Models\CompanyAdmin;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class AdminsDataTable extends DataTable
{
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->setRowId('id')
            ->addColumn('role', fn ($admin) => $admin->role->name)
            ->addColumn('status', fn ($admin): ?string => statusBadge($admin->status))
            ->addColumn('actions', fn ($admin) => view('pages.company.admins.actions', ['admin' => $admin])->render())
            ->addColumn('phone', fn ($admin): string => formatPhone($admin->country_code, $admin->phone))
            ->orderColumn('DT_RowIndex', function ($query, $direction): void {
                $query->orderBy('id', $direction);
            })
            ->rawColumns(['actions', 'phone', 'status'])
            ->addIndexColumn();
    }

    public function query(CompanyAdmin $model): QueryBuilder
    {
        return $model->newQuery()
            ->where('company_id', auth('company')->user()->company_id)
            ->with('role')
            ->when(request('search_param'), function ($query): void {
                $query->where('name', 'LIKE', '%'.request('search_param').'%')
                    ->orWhere('email', 'LIKE', '%'.request('search_param').'%');
            })
            ->when(request('status'), fn ($q) => $q->where('status', request('status')));
    }

    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('admins-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->searching(false)
            ->parameters([
                'language' => [
                    'url' => asset('assets/json/datatable-translations/'.app()->getLocale().'.json'),
                ],
            ]);
    }

    public function getColumns(): array
    {
        return [
            Column::make('DT_RowIndex')->title('#')->addClass('text-center')->searchable(false),
            Column::make('name')->title(__('Name'))->addClass('text-center')->orderable(false),
            Column::computed('email')->title(__('Email'))->addClass('text-center'),
            Column::computed('phone')->title(__('Phone'))->addClass('text-center'),
            Column::computed('role')->title(__('Role'))->addClass('text-center'),
            Column::computed('status')->title(__('Status'))->addClass('text-center'),
            Column::computed('actions')->title(__('Actions'))->addClass('text-center'),
        ];
    }

    protected function filename(): string
    {
        return 'Admins_'.date('YmdHis');
    }
}
