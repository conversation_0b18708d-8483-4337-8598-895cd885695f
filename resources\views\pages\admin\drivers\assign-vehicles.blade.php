<x-layout :title="__('Assign Vehicles to Driver')">
    <x-session-message />
    @if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
 
    <div id="ajax-alert" class="alert d-none" role="alert"></div>
    <form action="{{ route('admin.drivers.assign-vehicles', $driver) }}" method="POST" enctype="multipart/form-data">
        @csrf
        <div class="card">
            <div class="card-body">

                <!-- Currently Assigned Vehicles -->
                <h5 class="mb-3">{{ __('Currently Assigned Vehicles') }}</h5>
                <div class="row g-2 mb-4">
                    @forelse($approvedVehicles as $vehicle)
                        <div class="col-lg-3 col-md-4 col-sm-6">
                            <div class="card h-100 shadow-sm border rounded" style="font-size: 0.85rem;">
                                <div class="position-relative">
                                    <img src="{{ $vehicle->image_url }}" class="card-img-top"
                                        style="height: 120px; object-fit: cover;">
                                    <div class="position-absolute top-0 end-0 p-1">
                                        <input type="checkbox" name="vehicles[]" value="{{ $vehicle->id }}"
                                            class="form-check-input" checked>
                                    </div>
                                </div>
                                <div class="card-body p-2 text-center">
                                    <h6 class="card-title mb-1 text-truncate" title="{{ $vehicle->name }}">
                                        {{ $vehicle->name }}
                                    </h6>
                                    <small class="text-muted">{{ $vehicle->model }}</small>
                                    <div class="mt-2">
                                        @if ($vehicle->is_active)
                                            <span class="badge bg-success">{{ __('Enabled') }}</span>
                                        @else
                                            <button type="button" class="btn btn-sm btn-outline-success activate-btn"
                                                data-url="{{ route('admin.drivers.vehicles.activation', [$driver->id, $vehicle->id]) }}"
                                                data-vehicle-id="{{ $vehicle->id }}" data-active="0">
                                                {{ __('Enable') }}
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <p class="text-muted">- {{ __('No vehicles assigned.') }}</p>
                    @endforelse
                </div>

                <!-- Pending Assigned Vehicles -->
                <h5 class="mb-3">{{ __('Pending Assigned Vehicles') }}</h5>
                <div class="row g-2 mb-4">
                    @forelse($pendingVehicles as $vehicle)
                        <div class="col-lg-3 col-md-4 col-sm-6">
                            <div class="card h-100 shadow-sm border rounded">
                                <img src="{{ $vehicle->image_url }}" class="card-img-top"
                                    style="height: 120px; object-fit: cover;">
                                <div class="card-body p-2 text-center">
                                    <h6 class="card-title mb-1 text-truncate" title="{{ $vehicle->name }}">
                                        {{ $vehicle->name }}
                                    </h6>
                                    <small class="text-muted">{{ $vehicle->model }}</small>
                                </div>
                            </div>
                        </div>
                    @empty
                        <p class="text-muted">- {{ __('No pending vehicles.') }}</p>
                    @endforelse
                </div>

                <!-- Available Too Assign Vehicles -->
                <h5 class="mb-3">{{ __('Available Vehicles for Assignment') }}</h5>
                <div class="row g-2">
                    @forelse($availableVehicles as $vehicle)
                        <div class="col-lg-3 col-md-4 col-sm-6">
                            <div class="card h-100 shadow-sm border rounded" style="font-size: 0.85rem;">
                                <div class="position-relative">
                                    <img src="{{ $vehicle->image_url }}" class="card-img-top"
                                        style="height: 120px; object-fit: cover;">
                                    <div class="position-absolute top-0 end-0 p-1">
                                        <input type="checkbox" name="vehicles[]" value="{{ $vehicle->id }}"
                                            class="form-check-input" @checked(in_array($vehicle->id, old('vehicles', $assignedVehicleIds ?? [])))>
                                    </div>
                                </div>
                                <div class="card-body p-2 text-center">
                                    <h6 class="card-title mb-1 text-truncate" title="{{ $vehicle->name }}">
                                        {{ $vehicle->name }}
                                    </h6>
                                    <small class="text-muted">{{ $vehicle->model }}</small>
                                </div>
                            </div>
                        </div>
                    @empty
                        <p class="text-muted">{{ __('No vehicles available for assignment.') }}</p>
                    @endforelse
                </div>

                <div class="mt-3">
                    <button type="submit" class="btn btn-outline-primary btn-sm">
                        {{ __('Assign') }}
                    </button>
                </div>

            </div>
        </div>
    </form>

    @push('js')
        <script>
            function showAjaxAlert(message, type = 'danger') {
                const box = document.getElementById('ajax-alert');
                if (!box) return;
                box.className = 'alert alert-' + type;
                box.textContent = message;
                box.classList.remove('d-none');
                box.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }

            document.addEventListener('click', function(e) {
                const btn = e.target.closest('.activate-btn');
                if (!btn) return;
                const url = btn.getAttribute('data-url');
                const csrf = '{{ csrf_token() }}';
                const previousActive = btn.getAttribute('data-active');
                btn.disabled = true;
                btn.innerHTML = '{{ __('Processing...') }}';
                fetch(url, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': csrf,
                            'Accept': 'application/json'
                        }
                    }).then(async response => {
                        const data = await response.json().catch(() => ({}));
                        if (!response.ok) {
                            throw {
                                response,
                                data
                            };
                        }
                        return data;
                    })
                    .then(data => {
                        if (data && data.success && typeof data.is_active !== 'undefined') {
                            const isActive = data.is_active ? '1' : '0';
                            // Update clicked button
                            btn.classList.remove('btn-outline-success', 'btn-outline-danger');
                            btn.classList.add('btn-outline-' + (isActive === '1' ? 'danger' : 'success'));
                            btn.innerHTML = isActive === '1' ? '{{ __('Disable') }}' : '{{ __('Enable') }}';
                            btn.setAttribute('data-active', isActive);

                            // Ensure all other buttons are set to disabled state in UI (Enable action)
                            const activeVehicleId = data.active_vehicle_id;
                            document.querySelectorAll('.activate-btn').forEach(function(otherBtn) {
                                if (otherBtn === btn) return;
                                const thisVehicleId = otherBtn.getAttribute('data-vehicle-id');
                                const shouldBeActive = activeVehicleId && thisVehicleId == activeVehicleId;
                                otherBtn.classList.remove('btn-outline-success', 'btn-outline-danger');
                                otherBtn.classList.add('btn-outline-' + (shouldBeActive ? 'danger' :
                                    'success'));
                                otherBtn.innerHTML = shouldBeActive ? '{{ __('Disable') }}' :
                                    '{{ __('Enable') }}';
                                otherBtn.setAttribute('data-active', shouldBeActive ? '1' : '0');
                            });
                            showAjaxAlert('{{ __('Vehicle activation updated successfully') }}', 'success');
                        } else {
                            // restore previous state on failure
                            btn.classList.remove('btn-outline-success', 'btn-outline-danger');
                            btn.classList.add('btn-outline-' + (previousActive === '1' ? 'danger' : 'success'));
                            btn.innerHTML = previousActive === '1' ? '{{ __('Disable') }}' :
                                '{{ __('Enable') }}';
                            btn.setAttribute('data-active', previousActive);
                            const message = (data && data.message) ? data.message :
                                '{{ __('Something went wrong') }}';
                            showAjaxAlert(message, 'danger');
                        }
                    }).catch(err => {
                        // restore previous state on exception
                        btn.classList.remove('btn-outline-success', 'btn-outline-danger');
                        btn.classList.add('btn-outline-' + (previousActive === '1' ? 'danger' : 'success'));
                        btn.innerHTML = previousActive === '1' ? '{{ __('Disable') }}' : '{{ __('Enable') }}';
                        btn.setAttribute('data-active', previousActive);
                        const message = (err && err.data && err.data.message) ? err.data.message :
                            '{{ __('Something went wrong') }}';
                        showAjaxAlert(message, 'danger');
                        console.error(err);
                    })
                    .finally(() => btn.disabled = false);
            });
        </script>
    @endpush
</x-layout>
