<?php

namespace App\Services\Driver;

use App\Enum\OrderDriverActionStatus;
use App\Events\OrderAcceptedDriversUpdated;
use App\Models\Driver;
use App\Models\Order;
use App\Models\OrderDriverAction;
use App\Repositories\OrderDriverActionRepository;
use Illuminate\Http\Exceptions\HttpResponseException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class OrderDriverActionService
{
    public function __construct(private readonly OrderDriverActionRepository $orderDriverActionRepository)
    {
        //
    }

    public function accept(Order $order, Driver $driver)
    {
        $this->ensureDriverHasNoActiveOrders($driver);

        $action = $this->getOrderDriverActionOrFail($order, $driver);

        if (! $this->orderDriverActionRepository->accept($action)) {
            $this->throwErrorResponse(__('Order already accepted or failed to update.'));
        }

        $this->broadcastAcceptedDrivers($order);

        return __('Order accepted.');
    }

    public function reject(Order $order, Driver $driver)
    {
        $this->ensureDriverHasNoActiveOrders($driver);

        $action = $this->getOrderDriverActionOrFail($order, $driver);

        if (! $this->orderDriverActionRepository->reject($action)) {
            $this->throwErrorResponse(__('Order already rejected or failed to update.'));
        }

        $driver->update(['is_available' => true]);

        return __('Order rejected.');
    }

    public function resend(Order $order, Driver $driver)
    {
        $action = $this->getOrderDriverActionOrFail($order, $driver);

        if ($action->status !== OrderDriverActionStatus::TIMEOUT) {
            $this->throwErrorResponse(__('Order cannot be resent unless it has timed out.'));
        }

        if ($action->resend_attempts >= OrderDriverAction::MAX_RESEND_ATTEMPTS) {
            $this->throwErrorResponse(__('Order already resent.'));
        }

        $updated = $this->orderDriverActionRepository->resend($action);

        if (! $updated) {
            $this->throwErrorResponse(__('Failed to resend order.'));
        }

        $this->broadcastAcceptedDrivers($order);

        return __('Order resent.');
    }

    /** PRIVATE HELPERS */
    private function ensureDriverHasNoActiveOrders(Driver $driver): void
    {
        if ($driver->orders()->active()->exists()) {
            $this->throwErrorResponse(__('You must complete your active orders before responding to a new one.'), 403);
        }
    }

    private function getOrderDriverActionOrFail(Order $order, Driver $driver)
    {
        $action = $this->orderDriverActionRepository->findOrderDriver($order, $driver);

        if (! $action) {
            throw new NotFoundHttpException(__('The driver has not received this order.'));
        }

        return $action;
    }

    private function broadcastAcceptedDrivers(Order $order): void
    {
        $acceptedDrivers = $this->orderDriverActionRepository->getAcceptedDriversForOrder($order);
        event(new OrderAcceptedDriversUpdated($order, $acceptedDrivers));
    }

    private function throwErrorResponse(string $message, int $code = 422): void
    {
        throw new HttpResponseException(response()->json(['message' => $message], $code));
    }
}
