<x-layout :title="__('Add Vehicle')">
    <x-session-message />

    <div class="card">
        <div class="card-header">{{ __('Add Vehicle') }}</div>
        <div class="card-body">
            <form action="{{ route('company.vehicles.store') }}" method="POST" enctype="multipart/form-data">
                @csrf

                <div class="row">
                    <x-translated-text-input name="name" label="Name" :model="null" />
                </div>

                <div class="row">
                    <!-- Transportation Method -->
                    <div class="mb-3 col-lg-4">
                        <label for="transportion_method_id" class="form-label"><b>{{ __('Transportation Method') }}</b></label>
                        <select name="transportion_method_id" id="transportion_method_id" class="form-select select2">
                            <option disabled selected>{{ __('Choose...') }}</option>
                            @foreach($transportionMethods as $method)
                                <option value="{{ $method->id }}" @selected(old('transportion_method_id') == $method->id)>
                                    {{ $method->name }}
                                </option>
                            @endforeach
                        </select>
                        <x-input-error name="transportion_method_id" />
                    </div>

                    <!-- Model -->
                    <div class="mb-3 col-lg-4">
                        <label for="model" class="form-label"><b>{{ __('Model') }}</b></label>
                        <input type="text" name="model" id="model" class="form-control" value="{{ old('model') }}">
                        <x-input-error name="model" />
                    </div>

                    <!-- Year -->
                    <div class="mb-3 col-lg-4">
                        <label for="year" class="form-label"><b>{{ __('Year') }}</b></label>
                        <select name="year" id="year" class="select2 form-select form-select-lg" data-allow-clear="true">
                            <option disabled selected>{{ __("Choose") }}...</option>
                            @for($year = 1980; $year <= now()->year; $year++)
                                <option value="{{ $year }}" @selected(old('year') == $year)>{{ $year }}</option>
                            @endfor
                        </select>
                        <x-input-error name="year" />
                    </div>
                </div>

                <div class="row">
                    <!-- Plate Number -->
                    <div class="mb-3 col-lg-4">
                        <label for="plate_number" class="form-label"><b>{{ __('Plate Number') }}</b></label>
                        <input type="text" name="plate_number" id="plate_number" class="form-control" value="{{ old('plate_number') }}">
                        <x-input-error name="plate_number" />
                    </div>

                    <!-- License Number -->
                    <div class="mb-3 col-lg-4">
                        <label for="license_number" class="form-label"><b>{{ __('License Number') }}</b></label>
                        <input type="text" name="license_number" id="license_number" class="form-control" value="{{ old('license_number') }}">
                        <x-input-error name="license_number" />
                    </div>

                    <!-- License Expiration Date -->
                    <div class="mb-3 col-lg-4">
                        <label for="license_expiration_date" class="form-label"><b>{{ __('License Expiration Date') }}</b></label>
                        <input type="date" name="license_expiration_date" id="license_expiration_date" class="form-control" value="{{ old('license_expiration_date') }}">
                        <x-input-error name="license_expiration_date" />
                    </div>
                </div>

                <div class="row">
                    <!-- Photo -->
                    <div class="mb-3 col-lg-4">
                        <label for="photo" class="form-label"><b>{{ __('Vehicle Photo') }}</b></label>
                        <input type="file" name="photo" id="photo" class="form-control">
                        <x-input-error name="photo" />
                    </div>

                    <!-- Registration License -->
                    <div class="mb-3 col-lg-4">
                        <label for="registration_license" class="form-label"><b>{{ __('Registration License') }}</b></label>
                        <input type="file" name="registration_license" id="registration_license" class="form-control">
                        <x-input-error name="registration_license" />
                    </div>

                    <!-- Driving License -->
                    <div class="mb-3 col-lg-4">
                        <label for="driving_license" class="form-label"><b>{{ __('Driving License') }}</b></label>
                        <input type="file" name="driving_license" id="driving_license" class="form-control">
                        <x-input-error name="driving_license" />
                    </div>
                </div>

                <div class="row">
                    <!-- Insurance Policy (optional) -->
                    <div class="mb-4 col-lg-4">
                        <label for="insurance_policy" class="form-label"><b>{{ __('Insurance Policy') }}</b> ({{ __('Optional') }})</label>
                        <input type="file" name="insurance_policy" id="insurance_policy" class="form-control">
                        <x-input-error name="insurance_policy" />
                    </div>
                </div>

                <button type="submit" class="btn btn-outline-primary btn-sm">{{ __('Create') }}</button>
            </form>
        </div>
    </div>

     @push('js')
    <script>
        $("#license_expiration_date").flatpickr();
    </script>
    @endpush

</x-layout>
