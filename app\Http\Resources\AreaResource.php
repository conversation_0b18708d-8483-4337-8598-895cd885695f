<?php

namespace App\Http\Resources;

use App\Services\GeometryService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AreaResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'polygon' => app(GeometryService::class)->getPolygonPoints($this->area),
        ];
    }
}
